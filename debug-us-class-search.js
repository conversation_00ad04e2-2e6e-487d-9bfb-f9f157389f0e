#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');

const db = new PrismaClient();

async function debugUSClassSearch() {
  console.log('🔍 调试 us_class 搜索功能...\n');

  try {
    // 1. 检查数据库配置
    console.log('1️⃣ 检查数据库配置...');
    const databaseConfig = await db.databaseConfig.findUnique({
      where: { code: 'us_class' }
    });
    
    if (databaseConfig) {
      console.log('✅ 数据库配置存在:');
      console.log(`   - 名称: ${databaseConfig.name}`);
      console.log(`   - 访问级别: ${databaseConfig.accessLevel}`);
      console.log(`   - 表名: ${databaseConfig.tableName}`);
      console.log(`   - 模型名: ${databaseConfig.modelName}`);
      console.log(`   - 是否激活: ${databaseConfig.isActive}`);
    } else {
      console.log('❌ 数据库配置不存在');
      return;
    }

    // 2. 检查字段配置
    console.log('\n2️⃣ 检查字段配置...');
    const fieldConfigs = await db.fieldConfig.findMany({
      where: { 
        databaseCode: 'us_class',
        isActive: true 
      },
      orderBy: { listOrder: 'asc' }
    });

    console.log(`✅ 找到 ${fieldConfigs.length} 个字段配置:`);
    fieldConfigs.forEach(field => {
      console.log(`   - ${field.fieldName} (${field.displayName})`);
      console.log(`     可见: ${field.isVisible}, 可搜索: ${field.isSearchable}, 可筛选: ${field.isFilterable}`);
      console.log(`     搜索类型: ${field.searchType}, 筛选类型: ${field.filterType}`);
    });

    // 3. 检查 devicename 字段配置
    console.log('\n3️⃣ 检查 devicename 字段配置...');
    const devicenameConfig = fieldConfigs.find(f => f.fieldName === 'devicename');
    if (devicenameConfig) {
      console.log('✅ devicename 字段配置:');
      console.log(`   - 显示名称: ${devicenameConfig.displayName}`);
      console.log(`   - 字段类型: ${devicenameConfig.fieldType}`);
      console.log(`   - 可见: ${devicenameConfig.isVisible}`);
      console.log(`   - 可搜索: ${devicenameConfig.isSearchable}`);
      console.log(`   - 可筛选: ${devicenameConfig.isFilterable}`);
      console.log(`   - 搜索类型: ${devicenameConfig.searchType}`);
      console.log(`   - 筛选类型: ${devicenameConfig.filterType}`);
    } else {
      console.log('❌ devicename 字段配置不存在');
    }

    // 4. 检查实际数据
    console.log('\n4️⃣ 检查实际数据...');
    const sampleData = await db.uSClass.findMany({
      take: 5,
      select: {
        id: true,
        devicename: true,
        productcode: true,
        deviceclass: true
      }
    });

    console.log(`✅ 找到 ${sampleData.length} 条样本数据:`);
    sampleData.forEach((item, index) => {
      console.log(`   ${index + 1}. ID: ${item.id}`);
      console.log(`      devicename: "${item.devicename}"`);
      console.log(`      productcode: "${item.productcode}"`);
      console.log(`      deviceclass: "${item.deviceclass}"`);
    });

    // 5. 测试搜索查询
    console.log('\n5️⃣ 测试搜索查询...');
    const searchTerm = 'a'; // 搜索字母 'a'
    
    // 测试 contains 搜索
    const searchResults = await db.uSClass.findMany({
      where: {
        devicename: {
          contains: searchTerm,
          mode: 'insensitive'
        }
      },
      take: 5,
      select: {
        id: true,
        devicename: true,
        productcode: true
      }
    });

    console.log(`✅ 搜索 "${searchTerm}" 的结果 (${searchResults.length} 条):`);
    searchResults.forEach((item, index) => {
      console.log(`   ${index + 1}. devicename: "${item.devicename}"`);
      console.log(`      productcode: "${item.productcode}"`);
    });

    // 6. 检查总数据量
    console.log('\n6️⃣ 检查总数据量...');
    const totalCount = await db.uSClass.count();
    const nonEmptyDevicenameCount = await db.uSClass.count({
      where: {
        devicename: {
          not: null,
          not: ''
        }
      }
    });

    console.log(`✅ 数据统计:`);
    console.log(`   - 总记录数: ${totalCount}`);
    console.log(`   - 有效 devicename 记录数: ${nonEmptyDevicenameCount}`);

    // 7. 测试 API 端点
    console.log('\n7️⃣ 测试 API 端点...');
    try {
      const response = await fetch('http://localhost:3000/api/data/us_class?allFields=a&page=1&limit=5');
      const apiResult = await response.json();
      
      console.log('✅ API 响应:');
      console.log(`   - 成功: ${apiResult.success}`);
      console.log(`   - 数据条数: ${apiResult.data?.length || 0}`);
      if (apiResult.error) {
        console.log(`   - 错误: ${apiResult.error}`);
      }
      if (apiResult.data && apiResult.data.length > 0) {
        console.log('   - 第一条数据:');
        console.log(`     devicename: "${apiResult.data[0].devicename}"`);
        console.log(`     productcode: "${apiResult.data[0].productcode}"`);
      }
    } catch (apiError) {
      console.log(`❌ API 测试失败: ${apiError.message}`);
    }

  } catch (error) {
    console.error('❌ 调试过程中出错:', error);
  } finally {
    await db.$disconnect();
  }
}

// 运行调试
debugUSClassSearch().catch(console.error);
