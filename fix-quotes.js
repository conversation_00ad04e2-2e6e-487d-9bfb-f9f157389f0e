#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 修复被错误转义的引号
function fixQuotes() {
  console.log('🔧 修复被错误转义的引号...');
  
  const filesToFix = [
    'src/components/AccessRestrictedAlert.tsx',
    'src/components/ui/card.tsx',
    'src/components/ui/input.tsx',
    'src/lib/permissions.ts',
    'src/app/admin/analytics-data/page.tsx'
  ];

  for (const filePath of filesToFix) {
    if (fs.existsSync(filePath)) {
      try {
        let content = fs.readFileSync(filePath, 'utf8');
        let modified = false;

        // 恢复在 JavaScript/TypeScript 代码中被错误转义的引号
        // 在字符串字面量、类型定义、JSX 属性中恢复正常引号
        
        // 修复类型定义中的引号
        const typeQuoteFixes = [
          { from: /&apos;([^&]+)&apos;/g, to: "'$1'" },
          { from: /&quot;([^&]+)&quot;/g, to: '"$1"' }
        ];

        for (const fix of typeQuoteFixes) {
          const newContent = content.replace(fix.from, fix.to);
          if (newContent !== content) {
            content = newContent;
            modified = true;
          }
        }

        // 修复特定的语法错误
        if (filePath.includes('permissions.ts')) {
          // 修复缓存过期时间
          content = content.replace(/let configsCacheExpiry = ;/g, 'let configsCacheExpiry = 0;');
          modified = true;
        }

        if (modified) {
          fs.writeFileSync(filePath, content);
          console.log(`✅ 修复引号: ${filePath}`);
        }

      } catch (error) {
        console.error(`❌ 处理 ${filePath} 时出错:`, error.message);
      }
    }
  }
}

// 修复特定的语法错误
function fixSpecificErrors() {
  console.log('🔧 修复特定语法错误...');

  // 修复 AccessRestrictedAlert.tsx
  const alertFile = 'src/components/AccessRestrictedAlert.tsx';
  if (fs.existsSync(alertFile)) {
    let content = fs.readFileSync(alertFile, 'utf8');
    
    // 恢复正确的 JSX 语法
    content = content.replace(
      /requiredLevel === &apos;premium&apos;/g,
      "requiredLevel === 'premium'"
    );
    content = content.replace(
      /requiredLevel === &apos;enterprise&apos;/g,
      "requiredLevel === 'enterprise'"
    );
    
    fs.writeFileSync(alertFile, content);
    console.log(`✅ 修复: ${alertFile}`);
  }

  // 修复 card.tsx
  const cardFile = 'src/components/ui/card.tsx';
  if (fs.existsSync(cardFile)) {
    let content = fs.readFileSync(cardFile, 'utf8');
    
    // 恢复正确的 JSX 语法
    content = content.replace(
      /className={cn\(&quot;([^&]+)&quot;, className\)}/g,
      'className={cn("$1", className)}'
    );
    
    fs.writeFileSync(cardFile, content);
    console.log(`✅ 修复: ${cardFile}`);
  }

  // 修复 input.tsx
  const inputFile = 'src/components/ui/input.tsx';
  if (fs.existsSync(inputFile)) {
    let content = fs.readFileSync(inputFile, 'utf8');
    
    // 恢复正确的 TypeScript 语法
    content = content.replace(
      /React\.ComponentProps<&quot;input&quot;>/g,
      'React.ComponentProps<"input">'
    );
    
    fs.writeFileSync(inputFile, content);
    console.log(`✅ 修复: ${inputFile}`);
  }

  // 修复 analytics-data/page.tsx
  const analyticsFile = 'src/app/admin/analytics-data/page.tsx';
  if (fs.existsSync(analyticsFile)) {
    let content = fs.readFileSync(analyticsFile, 'utf8');
    
    // 恢复正确的 TypeScript 语法
    content = content.replace(
      /useState<&apos;activity&apos; \| &apos;search&apos; \| &apos;stats&apos;>\(&apos;stats&apos;\)/g,
      "useState<'activity' | 'search' | 'stats'>('stats')"
    );
    
    fs.writeFileSync(analyticsFile, content);
    console.log(`✅ 修复: ${analyticsFile}`);
  }

  // 修复 permissions.ts 中的另一个缓存过期时间
  const permissionsFile = 'src/lib/permissions.ts';
  if (fs.existsSync(permissionsFile)) {
    let content = fs.readFileSync(permissionsFile, 'utf8');
    
    // 查找并修复所有的空赋值
    content = content.replace(/let configsCacheExpiry = ;/g, 'let configsCacheExpiry = 0;');
    
    fs.writeFileSync(permissionsFile, content);
    console.log(`✅ 修复: ${permissionsFile}`);
  }
}

// 主函数
function main() {
  console.log('🚀 修复引号和语法错误');
  console.log('========================');
  
  fixQuotes();
  fixSpecificErrors();
  
  console.log('✅ 修复完成！');
}

if (require.main === module) {
  main();
}
