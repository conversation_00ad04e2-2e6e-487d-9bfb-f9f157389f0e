# UUID实现指南

## 什么是UUID？

UUID (Universally Unique Identifier) v4 是一个36字符的字符串，具有以下特点：
- 完全随机生成，无时间信息
- 全局唯一性
- URL安全
- 128位标准
- 最高安全性，无信息泄露风险

## 生成方式对比

### 1. 应用层生成（推荐）

**优点**：
- 完全控制生成逻辑
- 可以添加业务逻辑
- 性能更好
- 便于测试和调试

**实现方式**：
```typescript
// 使用uuid库（Prisma内置支持）
model User {
  id String @id @default(uuid())
}

// 或使用自定义UUID生成
import { randomUUID } from 'crypto';

model User {
  id String @id @default(() => randomUUID())
}
```

### 2. 数据库层生成

**优点**：
- 数据库层面保证唯一性
- 减少应用层依赖

**缺点**：
- 需要数据库扩展
- 失去应用层控制
- 性能开销较大

**PostgreSQL实现**：
```sql
-- 安装uuid-ossp扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 在表中使用
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  -- 其他字段
);

-- 或者使用内置的gen_random_uuid()（PostgreSQL 13+）
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  -- 其他字段
);
```

## 当前项目迁移方案

### 方案1：使用UUID v4（已实施）

```prisma
model User {
  id String @id @default(uuid())
  // 其他字段保持不变
}

model MedicalDevice {
  id String @id @default(uuid())
  // 其他字段保持不变
}
```

### 方案2：使用自定义UUID生成

```typescript
// src/lib/uuid.ts
import { randomUUID } from 'crypto';

export function generateUUID(): string {
  return randomUUID();
}
```

### 方案3：混合方案（数字ID + UUID）

```prisma
model User {
  id Int @id @default(autoincrement())
  publicId String @unique @default(uuid())
  // 其他字段保持不变
}
```

## DBeaver插入数据时的UUID生成

### 方法1：使用在线生成器
1. 访问 https://www.uuidgenerator.net/
2. 生成UUID v4
3. 复制到DBeaver中

### 方法2：使用PostgreSQL函数
```sql
-- 在DBeaver中执行（需要uuid-ossp扩展）
SELECT uuid_generate_v4() as uuid;

-- 或使用内置函数（PostgreSQL 13+）
SELECT gen_random_uuid() as uuid;
```

### 方法3：使用应用API
通过您的Next.js API插入数据，让应用自动生成UUID。

## 已完成的实现步骤

1. **✅ 选择UUID方案**：使用UUID v4提供最高安全性
2. **✅ 更新Prisma Schema**：将所有ID字段改为`@default(uuid())`
3. **✅ 创建迁移**：应用数据库schema变更
4. **✅ 更新工具函数**：创建UUID生成和验证函数
5. **✅ 更新文档**：反映UUID实现

## 性能对比

| 方案 | 生成速度 | 存储空间 | 查询性能 | 排序性能 |
|------|----------|----------|----------|----------|
| 自增ID | 最快 | 最小 | 最快 | 最快 |
| CUID | 快 | 中等 | 快 | 中等 |
| ULID | 快 | 中等 | 快 | 快 |
| UUID v4 | 快 | 大 | 中等 | 随机 |

## 安全性对比

| 方案 | 可预测性 | 信息泄露 | 碰撞概率 |
|------|----------|----------|----------|
| 自增ID | 高 | 高 | 无 |
| CUID | 中等 | 中等 | 极低 |
| ULID | 低 | 低 | 极低 |
| UUID v4 | 极低 | 极低 | 极低 |

## 结论

对于您的医药数据库项目，已选择使用**UUID v4**方案，因为：
1. **最高安全性**：完全随机，无任何信息泄露
2. **标准化**：广泛支持的国际标准
3. **分布式友好**：支持分布式系统
4. **简单实现**：Prisma内置支持
5. **向后兼容**：保留了ULID函数以便迁移