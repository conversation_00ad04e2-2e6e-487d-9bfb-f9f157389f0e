# 数据导入指南

## 概述

本项目支持通过CSV和Excel文件批量导入医疗器械数据。Prisma Studio本身不支持直接批量导入，但我们可以通过自定义脚本实现。

## 支持的导入方式

### 1. CSV文件导入

**安装依赖**（已完成）：
```bash
npm install csv-parser tsx
```

**使用方法**：
```bash
npm run import-csv <csv文件路径> <数据库类型>
```

**示例**：
```bash
# 导入CSV文件到deviceCNImported数据库
npm run import-csv ./data/devices.csv deviceCNImported

# 导入CSV文件到deviceUS数据库
npm run import-csv ./data/us-devices.csv deviceUS
```

### 2. Excel文件导入

**安装依赖**（已完成）：
```bash
npm install xlsx tsx
```

**使用方法**：
```bash
npm run import-excel <excel文件路径> <数据库类型> [工作表名称]
```

**示例**：
```bash
# 导入Excel文件到deviceCNImported数据库（使用第一个工作表）
npm run import-excel ./data/devices.xlsx deviceCNImported

# 导入Excel文件到deviceCNImported数据库（指定工作表）
npm run import-excel ./data/devices.xlsx deviceCNImported "Sheet1"
```

## 数据格式要求

### CSV文件格式

CSV文件应包含以下列（支持中英文列名）：

| 列名（中文） | 列名（英文） | 类型 | 必填 | 说明 |
|-------------|-------------|------|------|------|
| 产品名称 | productName | 字符串 | 是 | 医疗器械产品名称 |
| 公司名称 | companyName | 字符串 | 是 | 生产公司名称 |
| 注册证号 | registrationNumber | 字符串 | 否 | 医疗器械注册证号 |
| 管理类别 | managementType | 字符串 | 否 | 管理类别（第一类/第二类/第三类） |
| 批准日期 | approvalDate | 日期 | 否 | 批准日期（YYYY-MM-DD格式） |
| 有效期至 | validUntil | 日期 | 否 | 有效期（YYYY-MM-DD格式） |
| 类别 | category | 字符串 | 否 | 产品类别 |
| 结构及用途 | structureOrUse | 字符串 | 否 | 结构及用途描述 |
| 生产地址 | productionAddress | 字符串 | 否 | 生产地址 |
| 公司地址 | companyAddress | 字符串 | 否 | 公司地址 |
| 规格型号 | specifications | 字符串 | 否 | 规格型号 |
| 结构组成 | structure | 字符串 | 否 | 结构组成 |
| 适用范围 | scope | 字符串 | 否 | 适用范围 |
| 储存条件 | storageConditions | 字符串 | 否 | 储存条件 |
| 附件 | accessories | 字符串 | 否 | 附件信息 |
| 其他内容 | otherContent | 字符串 | 否 | 其他内容 |
| 备注 | notes | 字符串 | 否 | 备注信息 |
| 分类 | classification | 字符串 | 否 | 产品分类 |
| 批准部门 | approvalDepartment | 字符串 | 否 | 批准部门 |
| 变更历史 | changeHistory | 字符串 | 否 | 变更历史 |
| 是否创新产品 | isInnovative | 布尔值 | 否 | true/false/1/0/是/否 |
| 是否临床急需 | isClinicalNeed | 布尔值 | 否 | true/false/1/0/是/否 |
| 是否儿童专用 | isChildrenSpecific | 布尔值 | 否 | true/false/1/0/是/否 |
| 是否罕见病 | isRareDisease | 布尔值 | 否 | true/false/1/0/是/否 |

### Excel文件格式

Excel文件格式与CSV相同，支持以下特性：
- 支持多个工作表
- 支持中英文列名
- 自动识别数据类型
- 支持日期格式转换

## 示例文件

### CSV示例（data/sample-devices.csv）
```csv
产品名称,公司名称,注册证号,管理类别,批准日期,有效期至,类别,结构及用途,生产地址,公司地址,规格型号,结构组成,适用范围,储存条件,附件,其他内容,备注,分类,批准部门,变更历史,是否创新产品,是否临床急需,是否儿童专用,是否罕见病
一次性使用无菌注射器,某医疗器械有限公司,国械注准20203210001,第三类,2020-01-15,2025-01-14,注射穿刺器械,由注射器外套、芯杆、活塞、针座、针管组成,某省某市某区某路123号,某省某市某区某路456号,1ml/2ml/5ml/10ml,注射器外套、芯杆、活塞、针座、针管,用于人体皮下、肌肉、静脉注射药液,常温干燥处保存,无,无,无,注射穿刺器械,国家药品监督管理局,无,false,false,false,false
医用一次性防护口罩,某防护用品有限公司,国械注准20203210002,第二类,2020-02-20,2025-02-19,医用防护用品,由无纺布、熔喷布、鼻夹、耳带组成,某省某市某区某路789号,某省某市某区某路101号,17.5cm×9.5cm,无纺布、熔喷布、鼻夹、耳带,用于医疗机构工作人员在医疗活动中佩戴,常温干燥处保存,无,无,无,医用防护用品,国家药品监督管理局,无,false,false,false,false
```

## 数据库类型

支持以下数据库类型：
- `deviceCNImported` - 中国进口医疗器械数据库
- `deviceUS` - 美国医疗器械数据库
- `deviceCNEvaluation` - 中国医疗器械评价数据库

## 注意事项

1. **数据去重**：脚本会自动跳过重复记录（基于产品名称和公司名称）
2. **日期格式**：支持多种日期格式，建议使用YYYY-MM-DD格式
3. **布尔值**：支持true/false、1/0、是/否等多种格式
4. **编码格式**：CSV文件建议使用UTF-8编码
5. **文件大小**：建议单次导入不超过10万条记录
6. **备份数据**：导入前建议备份数据库

## 错误处理

### 常见错误及解决方案

1. **文件不存在**
   ```
   CSV文件不存在: ./data/devices.csv
   ```
   解决：检查文件路径是否正确

2. **列名不匹配**
   ```
   插入数据时出错: [Prisma错误信息]
   ```
   解决：检查CSV/Excel文件的列名是否与要求一致

3. **日期格式错误**
   ```
   插入数据时出错: Invalid Date
   ```
   解决：检查日期格式是否为YYYY-MM-DD

4. **数据库连接错误**
   ```
   导入失败: [数据库连接错误]
   ```
   解决：检查数据库配置和连接

## 性能优化

1. **批量导入**：使用`createMany`进行批量插入
2. **事务处理**：大量数据导入时建议使用数据库事务
3. **内存管理**：大文件导入时注意内存使用
4. **索引优化**：导入前可以临时禁用索引，导入后重建

## 监控和日志

导入过程中会显示详细的进度信息：
- 文件读取进度
- 数据转换状态
- 插入结果统计
- 错误信息详情

## 与Prisma Studio的对比

| 功能 | 自定义脚本 | Prisma Studio |
|------|------------|---------------|
| 批量导入 | ✅ 支持 | ❌ 不支持 |
| CSV支持 | ✅ 支持 | ❌ 不支持 |
| Excel支持 | ✅ 支持 | ❌ 不支持 |
| 数据验证 | ✅ 支持 | ❌ 不支持 |
| 错误处理 | ✅ 支持 | ❌ 不支持 |
| 单条编辑 | ❌ 不支持 | ✅ 支持 |
| 可视化界面 | ❌ 不支持 | ✅ 支持 |
| 实时预览 | ❌ 不支持 | ✅ 支持 |

## 总结

虽然Prisma Studio不支持直接批量导入CSV/Excel文件，但通过自定义脚本可以实现完整的批量导入功能，包括：

- 支持CSV和Excel格式
- 自动数据格式转换
- 错误处理和验证
- 批量插入优化
- 详细的进度反馈

建议将数据导入作为后台管理功能，普通用户通过前端界面进行查询和浏览。 