# 🔧 防抖优化具体实现示例

## 📊 **当前问题**

用户选择一个筛选条件时，会触发 **11 个并发 API 请求**：

```typescript
// 当前的问题代码
useEffect(() => {
  if (Object.keys(filters).length > 0) {
    const filterableFields = config.fields.filter(f => 
      f.isFilterable && 
      (f.filterType === 'multi_select' || f.filterType === 'select' || f.filterType === 'checkbox')
    );

    // 🚨 问题：立即发出 11 个 API 请求
    filterableFields.forEach((fieldConfig) => {
      fetchDynamicCounts(fieldConfig.fieldName);
    });
  }
}, [filters]);
```

**结果**：用户每次操作都会发出 11 个 API 请求！

## ✅ **防抖优化方案**

### 方案1：基础防抖（简单实现）

```typescript
import { debounce } from 'lodash';

// 创建防抖函数
const debouncedFetchDynamicCounts = useMemo(
  () => debounce((fieldName: string) => {
    fetchDynamicCounts(fieldName);
  }, 300), // 300ms 延迟
  []
);

// 使用防抖函数
useEffect(() => {
  if (Object.keys(filters).length > 0) {
    const filterableFields = config.fields.filter(f => 
      f.isFilterable && 
      (f.filterType === 'multi_select' || f.filterType === 'select' || f.filterType === 'checkbox')
    );

    // ✅ 改进：使用防抖函数
    filterableFields.forEach((fieldConfig) => {
      debouncedFetchDynamicCounts(fieldConfig.fieldName);
    });
  }
}, [filters]);
```

**效果**：
- 用户快速操作时，只有最后一次操作后 300ms 才发出请求
- API 请求减少 80-90%

### 方案2：智能批量防抖（推荐实现）

```typescript
// 更智能的批量防抖实现
const useBatchedDynamicCounts = () => {
  const [pendingFields, setPendingFields] = useState<Set<string>>(new Set());
  
  const batchedUpdate = useMemo(
    () => debounce(() => {
      const fieldsToUpdate = Array.from(pendingFields);
      setPendingFields(new Set());
      
      // 批量发送请求
      fieldsToUpdate.forEach(fieldName => {
        fetchDynamicCounts(fieldName);
      });
    }, 300),
    [pendingFields]
  );

  const addFieldToUpdate = useCallback((fieldName: string) => {
    setPendingFields(prev => new Set([...prev, fieldName]));
    batchedUpdate();
  }, [batchedUpdate]);

  return { addFieldToUpdate };
};

// 在组件中使用
const { addFieldToUpdate } = useBatchedDynamicCounts();

useEffect(() => {
  if (Object.keys(filters).length > 0) {
    const filterableFields = config.fields.filter(f => 
      f.isFilterable && 
      (f.filterType === 'multi_select' || f.filterType === 'select' || f.filterType === 'checkbox')
    );

    // ✅ 批量收集需要更新的字段
    filterableFields.forEach((fieldConfig) => {
      addFieldToUpdate(fieldConfig.fieldName);
    });
  }
}, [filters, addFieldToUpdate]);
```

### 方案3：终极优化 - 单个批量 API

```typescript
// 最优方案：单个 API 获取所有字段的动态计数
const batchFetchAllDynamicCounts = async (
  fields: string[], 
  currentFilters: Record<string, any>
) => {
  try {
    const response = await fetch(`/api/meta/${database}/dynamic-counts-batch`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        fields,
        filters: currentFilters
      })
    });
    
    const result = await response.json();
    
    if (result.success) {
      // 一次性更新所有字段的动态计数
      setDynamicCounts(result.data);
    }
  } catch (error) {
    console.error('批量获取动态计数失败:', error);
  }
};

const debouncedBatchUpdate = useMemo(
  () => debounce((fields: string[], filters: Record<string, any>) => {
    batchFetchAllDynamicCounts(fields, filters);
  }, 300),
  []
);

useEffect(() => {
  if (Object.keys(filters).length > 0) {
    const filterableFields = config.fields.filter(f => 
      f.isFilterable && 
      (f.filterType === 'multi_select' || f.filterType === 'select' || f.filterType === 'checkbox')
    );

    const fieldNames = filterableFields.map(f => f.fieldName);
    
    // 🚀 终极优化：11 个请求 → 1 个请求
    debouncedBatchUpdate(fieldNames, filters);
  }
}, [filters, debouncedBatchUpdate]);
```

## 📈 **效果对比**

### 测试场景：用户快速连续操作

**用户行为**：
1. t=0ms: 选择 country_code = "DE"
2. t=100ms: 选择 decision = "Approved"  
3. t=200ms: 选择 type = "510(k)"
4. t=250ms: 取消 decision
5. t=300ms: 选择 expeditedreview = "Y"

### 结果对比

| 方案 | API 请求数 | 网络负载 | 用户体验 |
|------|------------|----------|----------|
| **当前实现** | 5 × 11 = 55 个 | 极高 | 卡顿、闪烁 |
| **基础防抖** | 11 个 | 减少 80% | 流畅 |
| **智能批量** | 11 个 | 减少 80% | 很流畅 |
| **批量 API** | 1 个 | 减少 98% | 极其流畅 |

## 🎯 **实施建议**

### 立即实施（本周）
```typescript
// 最简单的防抖实现 - 5分钟就能完成
const debouncedFetchDynamicCounts = useMemo(
  () => debounce(fetchDynamicCounts, 300),
  []
);
```

### 中期优化（1个月）
- 实现智能批量防抖
- 添加加载状态管理
- 优化用户反馈

### 长期规划（3个月）
- 开发批量 API 端点
- 实现服务端缓存
- 完善错误处理

## 💡 **关键要点**

1. **防抖的本质**：延迟执行，避免频繁调用
2. **减少请求的原理**：将多次操作合并为一次执行
3. **最佳延迟时间**：300ms（用户感知不到，但能有效合并操作）
4. **渐进式优化**：从简单防抖到批量 API，逐步提升性能

这就是防抖优化如何将 API 请求从 12 个降到 1-3 个的完整实现方案！
