# 智能同步系统使用指南

## 概述

本项目已升级为智能同步系统，支持多种导入模式、复杂规则配置、变更日志记录和完整的数据管理功能。

## 核心特性

### 🚀 智能同步引擎
- **Upsert模式**: 存在则更新，不存在则插入
- **Insert模式**: 仅插入新数据，跳过已存在的记录
- **Replace模式**: 全量替换，先软删除再插入新数据

### ⚙️ 可配置规则
- **业务唯一键**: 支持复杂规则（多字段组合、条件判断）
- **字段映射**: 支持中英文字段名自动映射
- **数据验证**: 必填字段检查、唯一性验证
- **表级别配置**: 每个表可独立配置规则

### 📝 变更追踪
- **完整日志**: 记录所有数据变更（创建、更新、删除）
- **审计信息**: 操作人、来源、时间、变更原因
- **数据版本**: 支持数据版本控制

### 🛠️ 数据管理
- **统计信息**: 表级别统计、数据库类型统计
- **数据验证**: 完整性检查、重复检测
- **清理工具**: 非激活数据清理

## 快速开始

### 1. 智能同步导入

#### CSV文件导入
```bash
# 智能同步模式（推荐）
npm run import-csv ./data/devices.csv deviceCNImported upsert

# 仅插入模式
npm run import-csv ./data/devices.csv deviceCNImported insert

# 全量替换模式
npm run import-csv ./data/devices.csv deviceCNImported replace
```

#### Excel文件导入
```bash
# 智能同步模式（推荐）
npm run import-excel ./data/devices.xlsx deviceCNImported "Sheet1" upsert

# 仅插入模式
npm run import-excel ./data/devices.xlsx deviceCNImported "Sheet1" insert

# 全量替换模式
npm run import-excel ./data/devices.xlsx deviceCNImported "Sheet1" replace
```

### 2. 数据管理

#### 查看统计信息
```bash
npm run data-manager stats MedicalDevice
```

#### 查看变更历史
```bash
# 查看所有变更历史
npm run data-manager history

# 查看特定记录的变更历史
npm run data-manager history "国械注进20253771234_deviceCNImported"

# 查看最近10条变更
npm run data-manager history "" 10
```

#### 验证数据完整性
```bash
npm run data-manager validate MedicalDevice
```

#### 查看表配置
```bash
npm run data-manager config MedicalDevice
```

#### 查看支持的表
```bash
npm run data-manager tables
```

## 配置详解

### 表配置 (`src/lib/uniqueKeyConfig.ts`)

```typescript
export const tableConfigs: Record<string, TableConfig> = {
  MedicalDevice: {
    // 业务唯一键生成规则
    uniqueKeyRule: (row) => {
      if (row.registrationNumber && row.registrationNumber.trim()) {
        return `${row.registrationNumber}_${row.database}`;
      } else {
        return `${row.productName}_${row.companyName}_${row.database}`;
      }
    },
    
    // 导入模式
    importMode: 'upsert',
    
    // 表描述
    description: '医疗器械数据表，支持智能同步更新',
    
    // 字段映射（支持中英文字段名）
    fieldMapping: {
      '产品名称': 'productName',
      '公司名称': 'companyName',
      // ... 更多映射
    },
    
    // 验证规则
    validationRules: {
      requiredFields: ['productName', 'companyName', 'database'],
      uniqueFields: ['businessKey'],
    },
  },
};
```

### 添加新表配置

1. 在 `tableConfigs` 中添加新表配置
2. 定义唯一键生成规则
3. 设置导入模式和验证规则
4. 配置字段映射（如需要）

## 导入模式详解

### Upsert模式（智能同步）
- **适用场景**: 日常数据同步、增量更新
- **行为**: 存在则更新，不存在则插入
- **优点**: 保证数据最新，避免重复
- **示例**: 定期从官方数据源同步最新信息

### Insert模式（仅插入）
- **适用场景**: 一次性导入、历史数据导入
- **行为**: 仅插入新数据，跳过已存在的记录
- **优点**: 安全，不会覆盖现有数据
- **示例**: 导入历史数据，避免覆盖已有记录

### Replace模式（全量替换）
- **适用场景**: 数据源完全替换、定期全量更新
- **行为**: 先软删除所有记录，再插入新数据
- **优点**: 保证数据一致性
- **示例**: 每月从官方数据源获取完整数据

## 业务唯一键规则

### 当前规则
```typescript
// 医疗器械表：优先使用注册证号，否则使用产品名称+公司名称
uniqueKeyRule: (row) => {
  if (row.registrationNumber && row.registrationNumber.trim()) {
    return `${row.registrationNumber}_${row.database}`;
  } else {
    return `${row.productName}_${row.companyName}_${row.database}`;
  }
}
```

### 自定义规则示例
```typescript
// 简单规则：使用单一字段
uniqueKeyRule: (row) => `${row.id}_${row.database}`

// 复杂规则：多字段组合
uniqueKeyRule: (row) => `${row.code}_${row.version}_${row.region}`

// 条件规则：根据字段值选择不同策略
uniqueKeyRule: (row) => {
  if (row.type === 'imported') {
    return `${row.foreignId}_${row.database}`;
  } else {
    return `${row.localId}_${row.database}`;
  }
}
```

## 变更日志

### 日志字段
- `businessKey`: 业务唯一键
- `operation`: 操作类型（CREATE/UPDATE/DELETE/DEACTIVATE）
- `oldData`: 变更前的数据（JSON格式）
- `newData`: 变更后的数据（JSON格式）
- `changeReason`: 变更原因
- `importedBy`: 操作人
- `importedFrom`: 数据来源
- `createdAt`: 操作时间

### 查询变更历史
```bash
# 查看所有变更
npm run data-manager history

# 查看特定记录的变更
npm run data-manager history "业务唯一键"

# 查看最近N条变更
npm run data-manager history "" 50
```

## 数据验证

### 自动验证
- **必填字段检查**: 确保关键字段不为空
- **唯一性验证**: 检查业务唯一键的唯一性
- **数据格式验证**: 日期、布尔值等格式转换

### 手动验证
```bash
# 验证数据完整性
npm run data-manager validate MedicalDevice
```

验证内容包括：
- 重复的businessKey检测
- 必填字段缺失检查
- 数据版本统计

## 性能优化

### 批量操作
- 使用事务处理大量数据
- 批量插入/更新优化
- 索引优化查询性能

### 内存管理
- 流式处理大文件
- 分批处理大量数据
- 及时释放数据库连接

## 错误处理

### 常见错误
1. **业务唯一键重复**: 检查唯一键生成规则
2. **必填字段缺失**: 检查数据源和字段映射
3. **数据格式错误**: 检查日期、布尔值格式
4. **数据库连接错误**: 检查数据库配置

### 错误报告
导入过程中会显示详细的错误信息：
- 错误行号
- 业务唯一键
- 具体错误信息
- 错误统计

## 最佳实践

### 1. 数据导入
- 优先使用 `upsert` 模式进行日常同步
- 使用 `insert` 模式导入历史数据
- 谨慎使用 `replace` 模式，确保数据备份

### 2. 配置管理
- 在 `uniqueKeyConfig.ts` 中集中管理所有表配置
- 为每个表配置清晰的描述和验证规则
- 定期检查和更新字段映射

### 3. 数据监控
- 定期运行数据验证
- 监控变更历史
- 及时处理数据异常

### 4. 备份策略
- 重要操作前备份数据
- 保留变更日志用于数据恢复
- 定期清理非激活数据

## 扩展开发

### 添加新表支持
1. 在Prisma schema中定义表结构
2. 在 `uniqueKeyConfig.ts` 中添加表配置
3. 在导入脚本中添加表名支持
4. 在数据管理工具中添加表支持

### 自定义验证规则
```typescript
validationRules: {
  requiredFields: ['field1', 'field2'],
  uniqueFields: ['businessKey', 'email'],
  customValidation: (row) => {
    // 自定义验证逻辑
    if (row.age < 0) return '年龄不能为负数';
    return null; // 验证通过
  }
}
```

### 自定义字段映射
```typescript
fieldMapping: {
  '中文字段名': 'englishFieldName',
  '旧字段名': 'newFieldName',
  // 支持复杂的映射逻辑
  '复合字段': (row) => `${row.field1}_${row.field2}`
}
```

## 总结

智能同步系统提供了完整的数据导入、同步和管理解决方案：

- ✅ **多种导入模式**：满足不同业务场景需求
- ✅ **可配置规则**：灵活的业务唯一键和验证规则
- ✅ **变更追踪**：完整的数据变更审计
- ✅ **数据管理**：统计、验证、清理工具
- ✅ **错误处理**：详细的错误报告和处理
- ✅ **性能优化**：批量操作和内存管理
- ✅ **扩展性**：易于添加新表和自定义规则

通过合理配置和使用这些功能，可以构建稳定、高效的数据同步和管理系统。 