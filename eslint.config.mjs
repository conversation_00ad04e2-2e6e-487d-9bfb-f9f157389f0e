import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    rules: {
      // TypeScript rules - optimized for production build
      "@typescript-eslint/no-unused-vars": ["error", {
        "argsIgnorePattern": "^_",
        "varsIgnorePattern": "^_",
        "caughtErrorsIgnorePattern": "^_",
        "destructuredArrayIgnorePattern": "^_"
      }],
      "@typescript-eslint/no-explicit-any": "off", // Disabled for gradual migration
      "@typescript-eslint/no-empty-object-type": "off", // Disabled for interface compatibility
      "@typescript-eslint/prefer-as-const": "warn",
      "@typescript-eslint/no-inferrable-types": "off",

      // React rules - optimized for production
      "react-hooks/exhaustive-deps": "off", // Disabled to prevent build failures
      "react-hooks/rules-of-hooks": "error", // Keep this as error - critical
      "react/no-unescaped-entities": "off",

      // Next.js rules
      "@next/next/no-img-element": "off",
      "@next/next/no-html-link-for-pages": "off",

      // General code quality
      "prefer-const": "warn",
      "no-var": "error",
      "no-console": "off", // Disabled for development flexibility

      // Import/Export rules
      "import/no-unused-modules": "off",
      "import/no-unresolved": "off",

      // Accessibility - disabled for now
      "jsx-a11y/alt-text": "off",
      "jsx-a11y/anchor-is-valid": "off",
      "jsx-a11y/click-events-have-key-events": "off",
      "jsx-a11y/no-static-element-interactions": "off",
    },
  },
];

export default eslintConfig;
