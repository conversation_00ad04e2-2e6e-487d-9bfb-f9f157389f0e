# 分类排序系统说明

## 🎯 系统概述

新的分类排序系统解决了导航栏中数据库分类和数据库排序的问题，支持：

1. **分类级别排序**：控制分类在导航栏中的显示顺序
2. **分类内排序**：控制同一分类下数据库的显示顺序
3. **可扩展性**：支持未来添加更多数据库和分类

## 📊 排序字段说明

### 1. categoryOrder (分类排序)
- **作用**：决定分类在导航栏中的显示顺序
- **存储位置**：`databaseconfig.exportConfig.categoryOrder`
- **数值越小，排序越靠前**

### 2. orderInCategory (分类内排序)
- **作用**：决定数据库在同一分类内的显示顺序
- **存储位置**：`databaseconfig.exportConfig.orderInCategory`
- **数值越小，排序越靠前**

### 3. sortOrder (全局排序，保留)
- **作用**：作为备选排序字段，向后兼容
- **存储位置**：`databaseconfig.sortOrder`

## 🏗️ 当前配置

### 分类优先级
```
1. Regulation (法规类) - categoryOrder: 1
   - 包含审批、分类等监管信息
   
2. Marked/Marketed (已上市) - categoryOrder: 2
   - 包含已获批上市的产品信息
   
3. 全球器械 - categoryOrder: 3
   - 全球医疗器械数据库
   
4. 药物研发 - categoryOrder: 4
   - 包含专利、临床试验等
```

### 当前数据库配置
```
📁 Regulation (categoryOrder: 1)
   🇺🇸 US Classfication (orderInCategory: 1)

📁 Marked (categoryOrder: 2)  
   🇺🇸 US Premarket Notification (orderInCategory: 1)
```

## 🔧 技术实现

### 1. 数据存储
排序信息存储在 `exportConfig` JSON 字段中：
```json
{
  "icon": "🇺🇸",
  "categoryOrder": 1,
  "orderInCategory": 1,
  "formats": ["csv", "excel", "json"],
  "description": "导出配置描述"
}
```

### 2. API 返回
`/api/config/databases` 现在返回：
```json
{
  "us_class": {
    "name": "US Classfication",
    "category": "Regulation",
    "categoryOrder": 1,
    "orderInCategory": 1,
    "icon": "🇺🇸"
  }
}
```

### 3. 导航栏排序逻辑
```typescript
// 1. 分类排序
const sortedCategories = categories.sort((a, b) => {
  const orderA = categoryToOrder.get(a) || 99;
  const orderB = categoryToOrder.get(b) || 99;
  return orderA - orderB;
});

// 2. 分类内数据库排序
const sortedDatabases = databases.sort((a, b) => {
  const orderA = a.orderInCategory || a.sortOrder || 0;
  const orderB = b.orderInCategory || b.sortOrder || 0;
  return orderA - orderB;
});
```

## 📝 管理工具

### 1. 查看当前排序状态
```bash
npx tsx scripts/manage-category-sorting.ts show
```

### 2. 更新分类排序配置
```bash
npx tsx scripts/manage-category-sorting.ts update
```

### 3. 测试导航栏排序
```bash
npx tsx scripts/test-navigation-sorting.ts
```

## 🚀 添加新数据库

### 步骤1：确定分类
选择合适的分类，或定义新分类：
- `Regulation` - 法规类
- `Marked` - 已上市
- `全球器械` - 全球器械
- `药物研发` - 药物研发

### 步骤2：设置排序
在 `exportConfig` 中设置：
```json
{
  "categoryOrder": 1,     // 分类优先级
  "orderInCategory": 2,   // 在分类内的排序
  "icon": "🇺🇸"          // 图标
}
```

### 步骤3：验证排序
运行测试脚本确认排序正确：
```bash
npx tsx scripts/test-navigation-sorting.ts
```

## 🔄 扩展性设计

### 1. 新分类支持
在 `scripts/manage-category-sorting.ts` 中的 `CATEGORY_CONFIGS` 添加新分类：
```typescript
const CATEGORY_CONFIGS = {
  '新分类': {
    name: '新分类',
    order: 5,
    description: '新分类描述'
  }
};
```

### 2. 批量管理
可以通过脚本批量更新多个数据库的排序：
```typescript
// 批量设置分类内排序
await updateOrderInCategory('Regulation', [
  { code: 'us_class', order: 1 },
  { code: 'us_device', order: 2 }
]);
```

## ✅ 优势

1. **清晰的层级结构**：分类 → 数据库的两级排序
2. **灵活配置**：通过配置表动态调整排序
3. **向后兼容**：保留原有 `sortOrder` 字段作为备选
4. **易于管理**：提供管理工具和测试脚本
5. **可扩展**：支持未来添加更多分类和数据库

## 🎉 总结

新的分类排序系统完全解决了您提出的问题：

1. ✅ **分类排序**：`categoryOrder` 控制分类在导航栏中的顺序
2. ✅ **分类内排序**：`orderInCategory` 控制数据库在分类内的顺序  
3. ✅ **可扩展性**：支持一个分类下有多个数据库
4. ✅ **不破坏现有数据**：只添加新字段，不清空配置表
5. ✅ **完整的管理工具**：提供查看、更新、测试功能

现在导航栏会按照 `Regulation` → `Marked` 的顺序显示分类，每个分类内的数据库也会按照正确的顺序排列。
