-- 修复 us_pmn 数据库筛选字段的排序配置
-- 解决 Classification Advisory Committee 排在第一的问题

-- 当前问题：很多筛选字段的sortOrder都是0，导致排序不确定
-- 解决方案：为筛选字段配置合理的sortOrder

UPDATE "FieldConfig" 
SET "sortOrder" = 1
WHERE "databaseCode" = 'us_pmn' 
  AND "fieldName" = 'decision'
  AND "isActive" = true;

UPDATE "FieldConfig" 
SET "sortOrder" = 2  
WHERE "databaseCode" = 'us_pmn' 
  AND "fieldName" = 'type'
  AND "isActive" = true;

UPDATE "FieldConfig" 
SET "sortOrder" = 3
WHERE "databaseCode" = 'us_pmn' 
  AND "fieldName" = 'expeditedreview'
  AND "isActive" = true;

UPDATE "FieldConfig" 
SET "sortOrder" = 4
WHERE "databaseCode" = 'us_pmn' 
  AND "fieldName" = 'thirdparty'
  AND "isActive" = true;

UPDATE "FieldConfig" 
SET "sortOrder" = 5
WHERE "databaseCode" = 'us_pmn' 
  AND "fieldName" = 'sspindicator'
  AND "isActive" = true;

UPDATE "FieldConfig" 
SET "sortOrder" = 6
WHERE "databaseCode" = 'us_pmn' 
  AND "fieldName" = 'reviewadvisecomm'
  AND "isActive" = true;

UPDATE "FieldConfig" 
SET "sortOrder" = 7
WHERE "databaseCode" = 'us_pmn' 
  AND "fieldName" = 'classadvisecomm'
  AND "isActive" = true;

UPDATE "FieldConfig" 
SET "sortOrder" = 8
WHERE "databaseCode" = 'us_pmn' 
  AND "fieldName" = 'decision_year'
  AND "isActive" = true;

-- 验证更新结果
SELECT 
  "fieldName",
  "displayName", 
  "sortOrder",
  "listOrder",
  "isVisible",
  "isFilterable",
  "filterType"
FROM "FieldConfig" 
WHERE "databaseCode" = 'us_pmn' 
  AND "isFilterable" = true
  AND "isActive" = true
ORDER BY "sortOrder" ASC, "fieldName" ASC;
