#!/usr/bin/env node

/**
 * 修复最后的 ESLint 错误
 */

const fs = require('fs');

function fixSpecificErrors() {
  console.log('🔧 修复具体的 ESLint 错误...\n');
  
  const fixes = [
    {
      file: 'src/hooks/use-dynamic-layout.tsx',
      description: '修复 prev 参数',
      replacements: [
        { from: '(prev) =>', to: '(_prev) =>' }
      ]
    },
    {
      file: 'src/lib/enterprise-auth/jwt-manager.ts',
      description: '修复 sessionId 参数',
      replacements: [
        { from: 'sessionId', to: '_sessionId' }
      ]
    },
    {
      file: 'src/lib/syncEngine-refactored.ts',
      description: '修复 uniqueFields 变量',
      replacements: [
        { from: 'const uniqueFields = ', to: 'const _uniqueFields = ' }
      ]
    },
    {
      file: 'src/lib/syncEngine.ts',
      description: '修复 uniqueFields 变量',
      replacements: [
        { from: 'const uniqueFields = ', to: 'const _uniqueFields = ' }
      ]
    },
    {
      file: 'src/lib/uniqueKeyConfig.ts',
      description: '修复 context 参数',
      replacements: [
        { from: 'context', to: '_context' }
      ]
    },
    {
      file: 'src/middleware/enterprise-auth.ts',
      description: '修复 requiredPermission 参数',
      replacements: [
        { from: 'requiredPermission', to: '_requiredPermission' }
      ]
    }
  ];
  
  let totalFixed = 0;
  
  fixes.forEach(({ file, description, replacements }) => {
    console.log(`📁 ${description}: ${file}`);
    
    try {
      if (!fs.existsSync(file)) {
        console.log(`  ⚠️  文件不存在`);
        return;
      }
      
      let content = fs.readFileSync(file, 'utf-8');
      let changed = false;
      
      replacements.forEach(({ from, to }) => {
        const originalContent = content;
        content = content.replaceAll(from, to);
        if (content !== originalContent) {
          changed = true;
        }
      });
      
      if (changed) {
        fs.writeFileSync(file, content, 'utf-8');
        console.log(`  ✅ 修复成功`);
        totalFixed++;
      } else {
        console.log(`  ℹ️  无需修复`);
      }
      
    } catch (error) {
      console.log(`  ❌ 错误: ${error.message}`);
    }
  });
  
  // 清理 eslint-disable 注释
  const cleanupFiles = [
    'src/components/ui/sidebar.tsx',
    'src/hooks/useResizableColumns.ts'
  ];
  
  cleanupFiles.forEach(file => {
    console.log(`\n🧹 清理 eslint-disable 注释: ${file}`);
    
    try {
      if (!fs.existsSync(file)) {
        console.log(`  ⚠️  文件不存在`);
        return;
      }
      
      let content = fs.readFileSync(file, 'utf-8');
      const originalContent = content;
      
      // 移除未使用的 eslint-disable 注释
      content = content.replace(/^\s*\/\/ eslint-disable-next-line react-hooks\/exhaustive-deps\s*\n/gm, '');
      
      if (content !== originalContent) {
        fs.writeFileSync(file, content, 'utf-8');
        console.log(`  ✅ 清理成功`);
        totalFixed++;
      } else {
        console.log(`  ℹ️  无需清理`);
      }
      
    } catch (error) {
      console.log(`  ❌ 错误: ${error.message}`);
    }
  });
  
  console.log(`\n🎉 修复完成! 共处理 ${totalFixed} 个文件`);
}

fixSpecificErrors();
