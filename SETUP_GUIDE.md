# 🚀 项目设置指南

## 📋 功能完善状态

### ✅ **已完善的功能**

1. **Analytics 分析系统**
   - ✅ 事件追踪 API (`/api/analytics/track`)
   - ✅ 统计数据 API (`/api/analytics/stats`)
   - ✅ 分析仪表板 (`/admin/analytics`)
   - ✅ 用户行为追踪
   - ✅ 数据库访问统计

2. **用户认证系统**
   - ✅ 登录/注册功能
   - ✅ 会话管理
   - ✅ 权限控制
   - ✅ 用户状态管理

3. **权限检查优化**
   - ✅ 服务端权限预检查
   - ✅ 客户端/服务端分离
   - ✅ 配置缓存优化
   - ✅ 减少"检查访问权限中"显示时间

4. **系统健康检查**
   - ✅ 数据库连接检查
   - ✅ 环境变量验证
   - ✅ 功能状态监控

## 🔧 **环境变量设置**

### 必需的环境变量

创建 `.env.local` 文件并添加以下变量：

```bash
# 数据库连接
DATABASE_URL="postgresql://username:password@localhost:5432/database_name"

# 会话密钥 (至少32个字符)
SESSION_SECRET="your-super-secret-session-key-at-least-32-chars-long"

# 应用环境
NODE_ENV="development"
```

### 可选的环境变量

```bash
# 分析功能
ANALYTICS_ENABLED="true"

# 邮件服务 (如果需要邮件功能)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# 外部API密钥 (如果需要)
EXTERNAL_API_KEY="your-api-key"
```

## 🗄️ **数据库设置**

### 1. 安装 PostgreSQL

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# macOS (使用 Homebrew)
brew install postgresql
brew services start postgresql

# Windows
# 下载并安装 PostgreSQL 官方安装包
```

### 2. 创建数据库

```bash
# 连接到 PostgreSQL
sudo -u postgres psql

# 创建数据库和用户
CREATE DATABASE your_database_name;
CREATE USER your_username WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE your_database_name TO your_username;
\q
```

### 3. 运行数据库迁移

```bash
# 生成 Prisma 客户端
npx prisma generate

# 运行数据库迁移
npx prisma db push

# (可选) 查看数据库
npx prisma studio
```

## 🚀 **启动应用**

### 1. 安装依赖

```bash
npm install
```

### 2. 设置环境变量

```bash
cp .env.example .env.local
# 编辑 .env.local 文件，填入正确的值
```

### 3. 初始化数据库

```bash
npx prisma generate
npx prisma db push
```

### 4. 启动开发服务器

```bash
npm run dev
```

### 5. 验证设置

访问 `http://localhost:3000/api/health` 检查系统状态。

## 📊 **Analytics 功能使用**

### 访问分析仪表板

1. 登录系统
2. 访问 `/admin/analytics`
3. 查看网站访问统计

### 自定义事件追踪

```javascript
// 在客户端代码中
fetch('/api/analytics/track', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    event: 'custom_event',
    data: {
      path: window.location.pathname,
      customData: 'your_data',
    },
  }),
});
```

## 🔐 **用户管理**

### 创建管理员用户

```bash
# 使用 Prisma Studio
npx prisma studio

# 或者通过注册页面
# 访问 /register 创建账户
# 然后在数据库中将 membershipType 改为 'enterprise'
```

### 权限级别

- `free`: 免费用户，访问基础功能
- `premium`: 高级用户，访问更多数据库
- `enterprise`: 企业用户，访问所有功能

## 🛠️ **故障排除**

### 常见问题

1. **"检查访问权限中"一直显示**
   - 检查 SESSION_SECRET 是否设置
   - 确认数据库连接正常
   - 查看浏览器控制台错误

2. **Analytics API 404错误**
   - ✅ 已修复：创建了 `/api/analytics/track` 端点

3. **用户认证401错误**
   - 检查 SESSION_SECRET 环境变量
   - 确认用户已正确登录
   - 检查会话是否过期

4. **数据库连接失败**
   - 验证 DATABASE_URL 格式
   - 确认数据库服务运行中
   - 检查网络连接

### 健康检查

访问 `/api/health` 获取系统状态报告：

```json
{
  "status": "healthy",
  "healthy": true,
  "checks": {
    "database": true,
    "session": true,
    "environment": true,
    "analytics": true
  },
  "errors": [],
  "warnings": []
}
```

## 📈 **性能优化建议**

1. **数据库优化**
   - 为常用查询字段添加索引
   - 定期清理旧的活动日志
   - 使用连接池

2. **缓存优化**
   - 配置缓存已优化（10分钟TTL）
   - 考虑使用 Redis 进行分布式缓存

3. **前端优化**
   - 权限检查已优化
   - 减少不必要的API调用
   - 使用适当的加载状态

## 🔄 **更新和维护**

### 定期任务

```bash
# 清理旧的活动日志 (保留90天)
npm run cleanup-logs

# 分析流量数据
npm run analyze-traffic

# 备份数据库
pg_dump your_database_name > backup_$(date +%Y%m%d).sql
```

### 监控建议

- 设置数据库监控
- 监控API响应时间
- 跟踪错误率和用户活动

---

## 🎉 **完成！**

现在您的系统应该完全正常运行，包括：

- ✅ 完整的用户认证系统
- ✅ 功能完善的Analytics分析
- ✅ 优化的权限检查
- ✅ 系统健康监控

如有任何问题，请检查 `/api/health` 端点或查看应用日志。
