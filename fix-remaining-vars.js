#!/usr/bin/env node

/**
 * 修复剩余的未使用变量问题
 */

const fs = require('fs');

const fixes = [
  {
    file: 'src/components/CollapsibleStatsPanel.tsx',
    replacements: [
      { from: /onToggle/, to: '_onToggle' }
    ]
  },
  {
    file: 'src/components/ConfigurableStatsPanel.tsx',
    replacements: [
      { from: /onToggle/, to: '_onToggle' }
    ]
  },
  {
    file: 'src/hooks/use-dynamic-layout.tsx',
    replacements: [
      { from: /\(prev\) =>/, to: '(_prev) =>' }
    ]
  },
  {
    file: 'src/lib/enterprise-auth/jwt-manager.ts',
    replacements: [
      { from: /sessionId/, to: '_sessionId' }
    ]
  },
  {
    file: 'src/lib/syncEngine-refactored.ts',
    replacements: [
      { from: /const uniqueFields = /, to: 'const _uniqueFields = ' }
    ]
  },
  {
    file: 'src/lib/syncEngine.ts',
    replacements: [
      { from: /const uniqueFields = /, to: 'const _uniqueFields = ' }
    ]
  },
  {
    file: 'src/lib/uniqueKeyConfig.ts',
    replacements: [
      { from: /context/, to: '_context' }
    ]
  },
  {
    file: 'src/middleware/enterprise-auth.ts',
    replacements: [
      { from: /requiredPermission/, to: '_requiredPermission' }
    ]
  }
];

// 清理未使用的 eslint-disable 注释
const cleanupFiles = [
  'src/app/data/list/[database]/DatabasePageContent.tsx',
  'src/components/ui/sidebar.tsx',
  'src/hooks/useResizableColumns.ts',
  'src/lib/permissions.ts'
];

function applyFixes() {
  console.log('🔧 修复剩余的未使用变量...');
  
  let totalFixed = 0;
  
  // 应用变量重命名修复
  fixes.forEach(({ file, replacements }) => {
    try {
      if (!fs.existsSync(file)) {
        console.log(`⚠️  文件不存在: ${file}`);
        return;
      }
      
      let content = fs.readFileSync(file, 'utf-8');
      let changed = false;
      
      replacements.forEach(({ from, to }) => {
        if (from instanceof RegExp) {
          if (from.test(content)) {
            content = content.replace(from, to);
            changed = true;
          }
        } else {
          if (content.includes(from)) {
            content = content.replace(new RegExp(from, 'g'), to);
            changed = true;
          }
        }
      });
      
      if (changed) {
        fs.writeFileSync(file, content, 'utf-8');
        console.log(`✅ 修复变量: ${file}`);
        totalFixed++;
      }
      
    } catch (error) {
      console.log(`❌ 错误处理 ${file}: ${error.message}`);
    }
  });
  
  // 清理未使用的 eslint-disable 注释
  cleanupFiles.forEach(file => {
    try {
      if (!fs.existsSync(file)) return;
      
      let content = fs.readFileSync(file, 'utf-8');
      const originalContent = content;
      
      // 移除各种未使用的 eslint-disable 注释
      content = content.replace(/^\s*\/\/ eslint-disable-next-line react-hooks\/exhaustive-deps\s*\n/gm, '');
      content = content.replace(/^\s*\/\/ eslint-disable-next-line @typescript-eslint\/no-explicit-any\s*\n/gm, '');
      content = content.replace(/^\/\* eslint-disable @typescript-eslint\/no-explicit-any \*\/\s*\n/gm, '');
      
      if (content !== originalContent) {
        fs.writeFileSync(file, content, 'utf-8');
        console.log(`🧹 清理注释: ${file}`);
        totalFixed++;
      }
      
    } catch (error) {
      console.log(`❌ 清理错误 ${file}: ${error.message}`);
    }
  });
  
  console.log(`\n🎉 修复完成! 共处理 ${totalFixed} 个文件`);
}

applyFixes();
