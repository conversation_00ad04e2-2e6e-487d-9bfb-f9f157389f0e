// 测试所有数据库API的简单脚本
const databases = [
  'deviceCNImported',
  'deviceCNEvaluation', 
  'deviceUS',
  'deviceHK',
  'deviceJP',
  'deviceUK',
  'deviceSG',
  'freePat',
  'subjectNewdrug',
  'subjectLicenseout',
  'subjectVbp'
];

async function testDatabase(database) {
  try {
    console.log(`\n🧪 测试数据库: ${database}`);
    
    // 测试meta API
    const metaResponse = await fetch(`http://localhost:3000/api/meta/${database}`);
    const metaStatus = metaResponse.status;
    console.log(`  📊 Meta API: ${metaStatus === 200 ? '✅' : '❌'} (${metaStatus})`);
    
    // 测试data API
    const dataResponse = await fetch(`http://localhost:3000/api/data/${database}?page=1&limit=5`);
    const dataStatus = dataResponse.status;
    console.log(`  📋 Data API: ${dataStatus === 200 ? '✅' : '❌'} (${dataStatus})`);
    
    if (dataStatus === 200) {
      const data = await dataResponse.json();
      console.log(`  📈 数据条数: ${data.pagination?.totalCount || 0}`);
    }
    
    return { database, metaStatus, dataStatus };
  } catch (error) {
    console.log(`  ❌ 错误: ${error.message}`);
    return { database, error: error.message };
  }
}

async function testAllDatabases() {
  console.log('🚀 开始测试所有数据库API...\n');
  
  const results = [];
  for (const database of databases) {
    const result = await testDatabase(database);
    results.push(result);
    
    // 添加小延迟避免过快请求
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log('\n📊 测试结果汇总:');
  console.log('='.repeat(50));
  
  const successful = results.filter(r => r.metaStatus === 200 && r.dataStatus === 200);
  const failed = results.filter(r => r.metaStatus !== 200 || r.dataStatus !== 200 || r.error);
  
  console.log(`✅ 成功: ${successful.length}/${databases.length}`);
  console.log(`❌ 失败: ${failed.length}/${databases.length}`);
  
  if (failed.length > 0) {
    console.log('\n❌ 失败的数据库:');
    failed.forEach(r => {
      console.log(`  - ${r.database}: ${r.error || `Meta:${r.metaStatus} Data:${r.dataStatus}`}`);
    });
  }
  
  if (successful.length === databases.length) {
    console.log('\n🎉 所有数据库API测试通过！');
  } else {
    console.log('\n⚠️  部分数据库需要修复');
  }
}

// 运行测试
testAllDatabases().catch(console.error);
