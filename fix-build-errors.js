#!/usr/bin/env node

/**
 * 系统性修复 Next.js 构建错误的脚本
 * 
 * 这个脚本会：
 * 1. 分析构建错误
 * 2. 按优先级修复错误
 * 3. 验证修复结果
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 错误类型优先级
const ERROR_PRIORITIES = {
  'react-hooks/exhaustive-deps': 1,
  '@typescript-eslint/no-unused-vars': 2,
  '@typescript-eslint/no-explicit-any': 3,
  'react/no-unescaped-entities': 4,
  'no-console': 5,
  '@typescript-eslint/no-inferrable-types': 6
};

// 常用的类型替换映射
const TYPE_REPLACEMENTS = {
  'any': {
    'response': 'ApiResponse<unknown>',
    'data': 'Record<string, unknown>',
    'params': 'Record<string, string | string[]>',
    'config': 'Record<string, unknown>',
    'error': 'Error | unknown',
    'event': 'Event | unknown',
    'result': 'Record<string, unknown>',
    'item': 'Record<string, unknown>',
    'value': 'unknown',
    'props': 'Record<string, unknown>'
  }
};

// 常用的未使用变量修复
const UNUSED_VAR_FIXES = {
  // 在参数名前加下划线
  addUnderscore: (varName) => `_${varName}`,
  // 移除未使用的导入
  removeImport: true,
  // 移除未使用的变量声明
  removeDeclaration: true
};

class BuildErrorFixer {
  constructor() {
    this.errors = [];
    this.fixedFiles = new Set();
  }

  // 运行构建并收集错误
  collectErrors() {
    console.log('🔍 收集构建错误...');
    
    try {
      execSync('npm run build', { stdio: 'pipe' });
      console.log('✅ 构建成功，无需修复');
      return false;
    } catch (error) {
      const output = error.stdout?.toString() || error.stderr?.toString() || '';
      this.parseErrors(output);
      return true;
    }
  }

  // 解析错误信息
  parseErrors(output) {
    const lines = output.split('\n');
    let currentFile = null;
    
    for (const line of lines) {
      // 匹配文件路径
      const fileMatch = line.match(/^\.\/(.+\.tsx?)$/);
      if (fileMatch) {
        currentFile = fileMatch[1];
        continue;
      }
      
      // 匹配错误信息
      const errorMatch = line.match(/^(\d+):(\d+)\s+(Error|Warning):\s+(.+?)\s+(.+)$/);
      if (errorMatch && currentFile) {
        const [, lineNum, colNum, severity, message, rule] = errorMatch;
        this.errors.push({
          file: currentFile,
          line: parseInt(lineNum),
          column: parseInt(colNum),
          severity,
          message,
          rule,
          priority: ERROR_PRIORITIES[rule] || 999
        });
      }
    }
    
    // 按优先级排序
    this.errors.sort((a, b) => a.priority - b.priority);
    
    console.log(`📊 发现 ${this.errors.length} 个错误需要修复`);
    
    // 按规则类型统计
    const ruleStats = {};
    this.errors.forEach(error => {
      ruleStats[error.rule] = (ruleStats[error.rule] || 0) + 1;
    });
    
    console.log('📈 错误统计:');
    Object.entries(ruleStats)
      .sort(([,a], [,b]) => b - a)
      .forEach(([rule, count]) => {
        console.log(`  ${rule}: ${count} 个`);
      });
  }

  // 修复所有错误
  async fixAllErrors() {
    console.log('🔧 开始修复错误...');
    
    // 按文件分组
    const errorsByFile = {};
    this.errors.forEach(error => {
      if (!errorsByFile[error.file]) {
        errorsByFile[error.file] = [];
      }
      errorsByFile[error.file].push(error);
    });
    
    // 逐文件修复
    for (const [filePath, fileErrors] of Object.entries(errorsByFile)) {
      await this.fixFileErrors(filePath, fileErrors);
    }
    
    console.log(`✅ 修复完成，共处理 ${this.fixedFiles.size} 个文件`);
  }

  // 修复单个文件的错误
  async fixFileErrors(filePath, errors) {
    console.log(`🔧 修复文件: ${filePath} (${errors.length} 个错误)`);
    
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let modified = false;
      
      // 按行号倒序处理，避免行号偏移
      errors.sort((a, b) => b.line - a.line);
      
      for (const error of errors) {
        const result = this.fixSingleError(content, error);
        if (result.modified) {
          content = result.content;
          modified = true;
          console.log(`  ✓ 修复: ${error.rule} (行 ${error.line})`);
        } else {
          console.log(`  ⚠️  跳过: ${error.rule} (行 ${error.line}) - ${result.reason || '无法自动修复'}`);
        }
      }
      
      if (modified) {
        fs.writeFileSync(filePath, content);
        this.fixedFiles.add(filePath);
      }
      
    } catch (err) {
      console.error(`❌ 修复文件 ${filePath} 时出错:`, err.message);
    }
  }

  // 修复单个错误
  fixSingleError(content, error) {
    const lines = content.split('\n');
    const targetLine = lines[error.line - 1];
    
    if (!targetLine) {
      return { modified: false, content, reason: '找不到目标行' };
    }
    
    switch (error.rule) {
      case '@typescript-eslint/no-unused-vars':
        return this.fixUnusedVar(content, error, lines);
      
      case '@typescript-eslint/no-explicit-any':
        return this.fixExplicitAny(content, error, lines);
      
      case 'react-hooks/exhaustive-deps':
        return this.fixHookDeps(content, error, lines);
      
      case 'react/no-unescaped-entities':
        return this.fixUnescapedEntities(content, error, lines);
      
      case 'no-console':
        return this.fixConsoleStatement(content, error, lines);
      
      case '@typescript-eslint/no-inferrable-types':
        return this.fixInferrableTypes(content, error, lines);
      
      default:
        return { modified: false, content, reason: '未知错误类型' };
    }
  }

  // 修复未使用变量
  fixUnusedVar(content, error, lines) {
    const line = lines[error.line - 1];
    
    // 检查是否是参数
    if (error.message.includes('is defined but never used') && 
        error.message.includes('Allowed unused args must match')) {
      
      // 提取变量名
      const match = error.message.match(/'([^']+)' is defined but never used/);
      if (match) {
        const varName = match[1];
        const newLine = line.replace(new RegExp(`\\b${varName}\\b`), `_${varName}`);
        lines[error.line - 1] = newLine;
        return { modified: true, content: lines.join('\n') };
      }
    }
    
    // 检查是否是未使用的导入
    if (line.includes('import') && error.message.includes('is defined but never used')) {
      const match = error.message.match(/'([^']+)' is defined but never used/);
      if (match) {
        const varName = match[1];
        
        // 如果是单独导入，删除整行
        if (line.match(new RegExp(`^import\\s+{\\s*${varName}\\s*}\\s+from`))) {
          lines.splice(error.line - 1, 1);
          return { modified: true, content: lines.join('\n') };
        }
        
        // 如果是多个导入中的一个，只删除该导入
        const newLine = line.replace(new RegExp(`${varName},?\\s*`), '').replace(/,\s*}/, ' }');
        if (newLine !== line) {
          lines[error.line - 1] = newLine;
          return { modified: true, content: lines.join('\n') };
        }
      }
    }
    
    return { modified: false, content };
  }

  // 修复 any 类型
  fixExplicitAny(content, error, lines) {
    const line = lines[error.line - 1];
    
    // 简单的 any 替换
    if (line.includes(': any')) {
      let newLine = line;
      
      // 根据上下文推断合适的类型
      if (line.includes('response') || line.includes('result')) {
        newLine = line.replace(': any', ': Record<string, unknown>');
      } else if (line.includes('error')) {
        newLine = line.replace(': any', ': unknown');
      } else if (line.includes('data') || line.includes('params')) {
        newLine = line.replace(': any', ': Record<string, unknown>');
      } else {
        newLine = line.replace(': any', ': unknown');
      }
      
      if (newLine !== line) {
        lines[error.line - 1] = newLine;
        return { modified: true, content: lines.join('\n') };
      }
    }
    
    return { modified: false, content };
  }

  // 修复 React Hook 依赖
  fixHookDeps(content, error, lines) {
    // 这个比较复杂，需要分析具体的 hook 和依赖
    // 暂时跳过，需要手动处理
    return { modified: false, content, reason: '需要手动分析依赖关系' };
  }

  // 修复未转义的实体
  fixUnescapedEntities(content, error, lines) {
    const line = lines[error.line - 1];
    
    let newLine = line
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&apos;');
    
    if (newLine !== line) {
      lines[error.line - 1] = newLine;
      return { modified: true, content: lines.join('\n') };
    }
    
    return { modified: false, content };
  }

  // 修复 console 语句
  fixConsoleStatement(content, error, lines) {
    const line = lines[error.line - 1];
    
    // 将 console.log 改为 console.error 或删除
    if (line.includes('console.log')) {
      // 如果是调试语句，直接删除
      if (line.trim().startsWith('console.log')) {
        lines.splice(error.line - 1, 1);
        return { modified: true, content: lines.join('\n') };
      }
    }
    
    return { modified: false, content };
  }

  // 修复可推断类型
  fixInferrableTypes(content, error, lines) {
    const line = lines[error.line - 1];
    
    // 移除明显的类型注解
    const patterns = [
      /: string = ['"`]/,
      /: number = \d/,
      /: boolean = (true|false)/
    ];
    
    for (const pattern of patterns) {
      if (pattern.test(line)) {
        const newLine = line.replace(/:\s*(string|number|boolean)(?=\s*=)/, '');
        if (newLine !== line) {
          lines[error.line - 1] = newLine;
          return { modified: true, content: lines.join('\n') };
        }
      }
    }
    
    return { modified: false, content };
  }

  // 验证修复结果
  validateFixes() {
    console.log('🧪 验证修复结果...');
    
    try {
      execSync('npm run build', { stdio: 'pipe' });
      console.log('🎉 构建成功！所有错误已修复');
      return true;
    } catch (error) {
      console.log('⚠️  仍有错误需要手动处理');
      const output = error.stdout?.toString() || error.stderr?.toString() || '';
      
      // 显示剩余错误的摘要
      const remainingErrors = [];
      const lines = output.split('\n');
      
      for (const line of lines) {
        const errorMatch = line.match(/^(\d+):(\d+)\s+(Error|Warning):\s+(.+?)\s+(.+)$/);
        if (errorMatch) {
          remainingErrors.push(errorMatch[5]); // rule name
        }
      }
      
      if (remainingErrors.length > 0) {
        const ruleStats = {};
        remainingErrors.forEach(rule => {
          ruleStats[rule] = (ruleStats[rule] || 0) + 1;
        });
        
        console.log('📋 剩余错误:');
        Object.entries(ruleStats).forEach(([rule, count]) => {
          console.log(`  ${rule}: ${count} 个`);
        });
      }
      
      return false;
    }
  }
}

// 主函数
async function main() {
  console.log('🚀 Next.js 构建错误修复工具');
  console.log('================================');
  
  const fixer = new BuildErrorFixer();
  
  // 收集错误
  const hasErrors = fixer.collectErrors();
  if (!hasErrors) {
    return;
  }
  
  // 修复错误
  await fixer.fixAllErrors();
  
  // 验证结果
  const success = fixer.validateFixes();
  
  if (success) {
    console.log('✅ 所有错误已成功修复！');
  } else {
    console.log('⚠️  部分错误需要手动处理，请查看上面的错误列表');
    process.exit(1);
  }
}

// 运行脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = BuildErrorFixer;
