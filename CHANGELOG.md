# 变更日志

## [2025-01-12] - 表格滚动位置保持功能

### 新增功能 ✨

#### 🔒 固定第一列
- 实现第一列（产品名称）固定显示功能
- 使用 CSS `position: sticky` 技术，性能优异
- 添加视觉分隔边框，提升用户体验
- 第一列始终可见，方便用户定位和对比数据

#### 📍 滚动位置保持
- **排序操作保持位置**：点击任何列头排序后，水平滚动位置完全保持不变
- **翻页操作保持位置**：上一页/下一页操作后，滚动位置保持不变
- **筛选操作保持位置**：应用筛选条件后，滚动位置保持不变
- **高级搜索保持位置**：使用高级搜索功能后，滚动位置保持不变
- **智能重置行为**：重置筛选器时，滚动位置重置到最左侧（符合用户预期）

### 技术改进 🔧

#### 架构优化
- 采用三重保障机制：位置监听、状态标记、智能恢复
- 使用 `useRef` 替代 `useState`，避免重渲染循环
- 实现防竞态条件机制，解决数据重新渲染时的位置覆盖问题

#### 性能提升
- 使用 `requestAnimationFrame` 确保最佳 DOM 更新时机
- 最小化事件监听器的绑定/解绑操作
- 优化滚动事件处理，减少不必要的计算

### 用户体验改进 🎯

#### 解决的痛点
- ❌ **之前**：排序后需要重新滚动到目标位置
- ✅ **现在**：排序后位置完全保持，无需重新定位

- ❌ **之前**：翻页后丢失当前查看位置
- ✅ **现在**：翻页后位置保持，连续浏览体验

- ❌ **之前**：筛选后需要重新找到关注的列
- ✅ **现在**：筛选后位置保持，专注数据分析

### 文件变更 📁

#### 修改的文件
- `src/app/data/list/[database]/DatabasePageContent.tsx` - 主要功能实现

#### 新增的文件
- `docs/SCROLL_POSITION_FEATURE.md` - 功能详细文档

---

## [之前] - 配置统一化重构

## 概述
本次重构统一了数据库配置管理，解决了配置重复和筛选框重复显示的问题，将所有配置统一到 `DatabaseConfig` 表中。

## 主要变更

### 1. 删除的配置表和相关代码

#### 1.1 删除 FilterConfig 表
- **文件**: `prisma/schema.prisma`
- **删除内容**: `FilterConfig` 模型定义（第221-240行）
- **原因**: 与 `FieldConfig` 表功能重复，造成配置冗余

#### 1.2 删除 FilterConfig 相关接口和类型
- **文件**: `src/lib/configCache.ts`
- **删除内容**:
  - `DatabaseFilterConfig` 接口
  - `ConfigCacheService.getFilterConfigs()` 方法
  - `FILTER_CONFIG_KEY` 缓存键
  - `DatabaseConfig.filters` 属性

#### 1.3 删除硬编码的 DATABASE_ACCESS_LEVELS
- **文件**: `src/lib/permissions.ts`
- **删除内容**: `DATABASE_ACCESS_LEVELS` 常量对象
- **替换为**: 从 `DatabaseConfig` 表动态读取访问级别

### 2. 新增的功能

#### 2.1 统一配置获取函数
- **文件**: `src/lib/permissions.ts`
- **新增函数**:
  - `getDatabaseConfigs()`: 从数据库获取完整配置
  - `getDatabaseAccessLevels()`: 获取访问级别映射
  - `canAccessDatabaseSync()`: 同步版本的权限检查

#### 2.2 数据库配置更新脚本
- **文件**: `scripts/update-database-access-levels.ts`
- **功能**: 更新 `DatabaseConfig` 表中的访问级别配置
- **配置内容**:
  ```
  免费数据库: freePat, deviceCNEvaluation, deviceCNImported
  高级数据库: deviceHK, deviceUS, deviceJP, deviceUK, deviceSG, subjectNewdrug
  企业数据库: subjectLicenseout, subjectVbp
  ```

### 3. 修改的文件

#### 3.1 筛选器生成逻辑
- **文件**: `src/app/data/list/[database]/DatabasePageContent.tsx`
- **变更**:
  - 删除硬编码筛选框（第522-617行）
  - 修改 `renderDynamicFilters()` 基于 `FieldConfig.isFilterable` 生成
  - 移除对 `DatabaseFilterConfig` 的引用

#### 3.2 导航栏动态配置
- **文件**: `src/components/Navigation.tsx`
- **变更**:
  - 使用 `getDatabaseConfigs()` 动态获取配置
  - 实现 `renderDatabaseLink()` 函数统一渲染逻辑
  - 支持免费/高级/企业三级访问标签

#### 3.3 首页数据库列表
- **文件**: `src/app/page.tsx`
- **变更**:
  - 使用动态配置替换硬编码 `DATABASE_CONFIGS`
  - 保持搜索结果数量显示功能

#### 3.4 全局搜索API
- **文件**: `src/app/api/global-search/route.ts`
- **变更**:
  - 使用 `getDatabaseConfigs()` 获取数据库列表
  - 暂时禁用ES，使用Prisma搜索确保稳定性

### 4. 配置缓存优化

#### 4.1 缓存机制
- **TTL**: 5分钟缓存
- **回退机制**: 数据库失败时使用硬编码配置
- **自动刷新**: 配置变更时自动更新缓存

#### 4.2 性能优化
- 减少数据库查询次数
- 统一配置获取接口
- 避免重复配置读取

### 5. 数据库变更

#### 5.1 访问级别更新
执行脚本更新了以下数据库的访问级别：
```
freePat: free
deviceCNEvaluation: free  
deviceCNImported: free
deviceHK: premium
deviceUS: premium
deviceJP: premium
deviceUK: premium
deviceSG: premium
subjectNewdrug: premium
subjectLicenseout: enterprise
subjectVbp: enterprise
```

#### 5.2 表结构变更
- 删除 `FilterConfig` 表（包含11行数据）
- 保留 `FieldConfig` 表作为唯一配置源

### 6. 修复的问题

#### 6.1 筛选框重复显示
- **问题**: Product Name 等筛选框出现两次
- **原因**: 硬编码筛选框 + 动态筛选框重复
- **解决**: 移除硬编码，统一使用动态生成

#### 6.2 配置不一致
- **问题**: 导航栏显示"高级"但实际是免费数据库
- **原因**: 硬编码权限与数据库配置不一致
- **解决**: 统一从 `DatabaseConfig` 表读取

#### 6.3 搜索结果数量显示
- **问题**: 首页搜索后数据库名称后没有显示结果数量
- **原因**: API使用硬编码配置导致错误
- **解决**: 修复API使用动态配置

### 7. 向后兼容性

#### 7.1 API接口保持不变
- 权限检查函数签名保持兼容
- 配置获取接口保持一致
- 前端组件接口无变化

#### 7.2 数据结构兼容
- `DatabaseConfig` 接口简化但向后兼容
- 移除 `filters` 属性但不影响现有功能
- 保持 `fields` 属性完整性

### 8. 测试验证

#### 8.1 功能验证
- ✅ 筛选框不再重复显示
- ✅ 导航栏访问级别标签正确
- ✅ 首页搜索结果数量正常显示
- ✅ 权限检查准确无误

#### 8.2 性能验证
- ✅ 配置加载速度正常
- ✅ 缓存机制工作正常
- ✅ 搜索响应时间稳定

## 总结

本次重构成功实现了：
1. **配置统一**: 所有配置来源统一到 `DatabaseConfig` 表
2. **消除重复**: 删除重复的筛选框和配置
3. **提升维护性**: 配置修改只需在数据库中操作
4. **保持稳定性**: 向后兼容，不影响现有功能
5. **性能优化**: 缓存机制减少数据库查询

系统现在使用单一、可靠的配置源，易于维护和扩展。
