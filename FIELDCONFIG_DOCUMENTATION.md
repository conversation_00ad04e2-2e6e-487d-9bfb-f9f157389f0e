# FieldConfig 表字段配置文档

## 📋 表概述

`FieldConfig` 表是系统的核心配置表，统一管理所有数据库字段的显示、搜索、筛选、排序、导出等功能配置。

## 🔧 字段详细说明

### 基础标识字段

| 字段名 | 类型 | 作用 | 示例 |
|--------|------|------|------|
| `id` | String | 主键 - 字段配置的唯一标识符 | `uuid` |
| `databaseCode` | String | 数据库代码 - 关联到 DatabaseConfig.code | `us_pmn`, `us_class` |
| `fieldName` | String | 字段名称 - 数据库表中的实际字段名 | `knumber`, `devicename` |
| `displayName` | String | 显示名称 - 在用户界面中显示的友好名称 | `K Number`, `Device Name` |
| `fieldType` | Enum | 字段类型 - 影响显示格式和验证 | `text`, `date`, `number` |

### 显示控制字段

| 字段名 | 类型 | 作用 | 说明 |
|--------|------|------|------|
| `isVisible` | Boolean | **列表页可见性** | `true`: 在数据列表页显示，`false`: 隐藏 |
| `listOrder` | Int | **列表显示顺序** | 控制字段在列表页中的列顺序，数值越小越靠前 |
| `detailOrder` | Int | **详情页显示顺序** | `0`: 不在详情页显示，`>0`: 按数值排序显示 |

### 功能控制字段

| 字段名 | 类型 | 作用 | 配置选项 |
|--------|------|------|----------|
| `isSearchable` | Boolean | **可搜索性** | 是否参与全文搜索 |
| `searchType` | Enum | **搜索类型** | `exact`, `contains`, `range`, `date_range` |
| `isFilterable` | Boolean | **可筛选性** | 是否在筛选面板显示 |
| `filterType` | Enum | **筛选器类型** | `select`, `input`, `date_range`, `multi_select` |
| `isSortable` | Boolean | **可排序性** | 列表页是否可点击排序 |
| `sortOrder` | Int | **排序优先级** | 配置查询排序，默认排序字段选择 |

### 特殊功能字段

| 字段名 | 类型 | 作用 | 说明 |
|--------|------|------|------|
| `todetail` | Boolean | **详情页链接** | `true`: 显示为链接，点击跳转详情页 |
| `validationRules` | JSON | **验证规则** | 存储字段验证规则，如长度限制 |
| `options` | JSON | **选项配置** | 存储字段选项，如下拉框选项列表 |

### 统计功能字段

| 字段名 | 类型 | 作用 | 配置选项 |
|--------|------|------|----------|
| `isStatisticsEnabled` | Boolean | **统计功能启用** | 是否生成统计图表 |
| `statisticsOrder` | Int | **统计显示顺序** | 统计页面中的显示顺序 |
| `statisticsType` | Enum | **统计类型** | `count`, `sum`, `avg`, `group_by` |
| `statisticsDisplayName` | String | **统计显示名称** | 统计图表中的名称 |
| `statisticsConfig` | JSON | **统计配置** | 图表类型、颜色等配置 |
| `statisticsSortOrder` | String | **统计排序方向** | `asc`(正序), `desc`(倒序) |
| `statisticsDefaultLimit` | Int | **统计默认限制** | 默认显示条目数 |
| `statisticsMaxLimit` | Int | **统计最大限制** | 最多显示条目数 |

### 导出功能字段

| 字段名 | 类型 | 作用 | 说明 |
|--------|------|------|------|
| `isExportable` | Boolean | **可导出性** | 是否包含在数据导出中 |
| `exportOrder` | Int | **导出顺序** | 导出文件中的列顺序，`0`表示不导出 |
| `exportDisplayName` | String | **导出列名** | 导出文件中的列标题 |

### 状态和时间戳

| 字段名 | 类型 | 作用 | 说明 |
|--------|------|------|------|
| `isActive` | Boolean | **激活状态** | `true`: 配置生效，`false`: 禁用 |
| `createdAt` | DateTime | **创建时间** | 配置创建的时间戳 |
| `updatedAt` | DateTime | **更新时间** | 配置最后修改的时间戳 |

## 🎯 关键字段作用总结

### ❓ **回答你的问题：哪个字段控制详情页显示？**

**答案：`detailOrder` 字段**

- `detailOrder = 0` → 不在详情页显示
- `detailOrder > 0` → 在详情页显示，按数值排序
- 数值越小，显示位置越靠前

### 📊 **各功能对应的字段**

1. **列表页显示** → `isVisible` + `listOrder`
2. **详情页显示** → `detailOrder`
3. **搜索功能** → `isSearchable` + `searchType`
4. **筛选功能** → `isFilterable` + `filterType`
5. **排序功能** → `isSortable` + `sortOrder`
6. **统计功能** → `isStatisticsEnabled` + `statisticsOrder`
7. **导出功能** → `isExportable` + `exportOrder`
8. **详情链接** → `todetail`

## 💡 最佳实践

1. **统一管理**: 所有字段（包括`id`）都应该有对应的 FieldConfig 配置
2. **合理排序**: 
   - `listOrder`: 用户界面的列显示顺序
   - `detailOrder`: 详情页的字段显示顺序
   - `sortOrder`: 配置查询的优先级
3. **功能开关**: 通过布尔字段控制各种功能的启用/禁用
4. **灵活配置**: 使用 JSON 字段存储复杂配置，保持扩展性
