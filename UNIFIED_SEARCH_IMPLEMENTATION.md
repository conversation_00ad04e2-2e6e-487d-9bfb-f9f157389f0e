# 统一搜索系统实现文档

## 概述

本文档描述了新实现的ES + Prisma统一搜索架构，实现了「全站综合搜索」与「各数据库页内搜索」两套入口，统一落到Prisma结果展示，并且Prisma的筛选/排序是在ES命中的子集之上继续执行。

## 架构设计

### 核心原理

1. **ES作为搜索引擎**：负责全文检索，返回相关的`{table_code, id}`集合
2. **Prisma作为数据展示层**：基于ES返回的ID子集进行二次筛选、排序、分页，确保数据一致性
3. **两套入口统一逻辑**：全站搜索和页内搜索都走相同的ES→Prisma流程

### 数据流程

```
用户搜索 → ES检索 → 获取{table_code, id}集合 → Prisma回捞 → 二次筛选/排序/分页 → 展示结果
```

## 核心组件

### 1. ElasticsearchService (`src/lib/services/elasticsearchService.ts`)

负责ES查询逻辑，提供标准化的搜索接口：

- `search()`: 执行ES搜索，返回命中的ID列表
- `searchByDatabases()`: 按数据库分组统计搜索结果
- `checkHealth()`: 检查ES连接状态

**特性**：
- 支持多字段搜索（registration_no, product_combined, company_combined）
- 智能权重分配（精确匹配 > 包含匹配 > 通配符匹配）
- 支持单库和多库搜索

### 2. PrismaFetchService (`src/lib/services/prismaFetchService.ts`)

基于ES返回的ID集合批量查询Prisma，支持二次筛选和排序：

- `batchFetch()`: 批量回捞多个数据库的数据
- `singleFetch()`: 单个数据库回捞
- `logMissingIds()`: 记录缺失的ID，用于触发同步任务

**特性**：
- 支持动态模型获取
- 智能筛选条件应用
- 缺失ID检测和记录

### 3. UnifiedSearchService (`src/lib/services/unifiedSearchService.ts`)

整合ES搜索 + Prisma回捞，提供完整的搜索解决方案：

- `globalSearch()`: 全站综合搜索，返回各数据库统计
- `unifiedSearch()`: 统一搜索，支持全站和页内搜索
- `prismaOnlySearch()`: 纯Prisma搜索（当没有搜索词只有筛选条件时）

## API接口

### 1. 全站搜索API (`/api/unified-global-search`)

```typescript
GET /api/unified-global-search?q=<keyword>
```

**响应格式**：
```json
{
  "success": true,
  "data": [
    {
      "database": "us_class",
      "count": 90
    },
    {
      "database": "us_pmn", 
      "count": 3594
    }
  ],
  "search_info": {
    "query": "dental",
    "search_time": 80.64,
    "es_took": 0
  }
}
```

### 2. 数据库搜索API (`/api/unified-database-search/[database]`)

```typescript
GET /api/unified-database-search/[database]?q=<keyword>&page=1&limit=20&filters={}
```

**响应格式**：
```json
{
  "success": true,
  "data": [...], // Prisma回捞的实际数据
  "pagination": {
    "page": 1,
    "limit": 5,
    "total_pages": 18,
    "total_results": 90
  },
  "search_info": {
    "query": "dental",
    "search_time": 46.84,
    "es_took": 13,
    "missing_ids_count": 85,
    "es_total": 90,
    "prisma_total": 90
  }
}
```

## 前端集成

### 1. 全站搜索组件更新

- `src/hooks/use-global-search.tsx`: 更新API端点
- `src/components/MedicalSearchExample.tsx`: 更新API调用

### 2. 数据库页面搜索更新

- `src/app/data/list/[database]/DatabasePageContent.tsx`: 
  - 替换ES搜索逻辑为统一搜索API
  - 保持现有UI和交互不变
  - 增加搜索性能监控

## 测试验证

### 测试页面

访问 `/test-unified-search` 可以测试新的统一搜索功能：

- 全站搜索测试
- 数据库搜索测试
- 性能监控
- 调试信息展示

### 测试结果

以"dental"关键词为例：

1. **全站搜索**：
   - us_pmn: 3594条
   - us_class: 90条
   - 搜索耗时: ~80ms

2. **数据库搜索**（us_class）：
   - ES命中: 90条
   - Prisma回捞: 5条（分页）
   - 缺失ID: 85条
   - 搜索耗时: ~47ms

## 数据一致性问题

### 问题发现

测试中发现85个ID在ES中存在但在Prisma中缺失，这表明ES索引和数据库之间存在数据不一致。

### 解决方案

1. **实时监控**：`PrismaFetchService.logMissingIds()` 记录缺失ID
2. **同步任务**：可以基于缺失ID触发数据同步
3. **定期校验**：建议定期对比ES和数据库数据

## 性能优化

### ES搜索优化

1. **智能权重**：精确匹配权重最高，通配符匹配权重最低
2. **字段优化**：registration_no.raw 权重最高（5倍）
3. **分页优化**：ES获取大量ID，Prisma负责分页

### Prisma回捞优化

1. **批量查询**：支持多数据库并行回捞
2. **字段选择**：只返回可见字段，减少数据传输
3. **缓存机制**：配置信息缓存5分钟

## 兼容性

### 向后兼容

- 保持现有API响应格式不变
- 前端组件无需大幅修改
- 现有搜索功能平滑迁移

### 渐进式升级

- 新API与旧API并存
- 可以逐步迁移各个组件
- 支持A/B测试

## 监控和日志

### 搜索分析

- 搜索耗时监控
- ES vs Prisma性能对比
- 缺失ID统计
- 搜索成功率

### 错误处理

- ES连接失败自动回退到Prisma
- 详细错误日志记录
- 用户友好的错误提示

## 未来扩展

### 功能扩展

1. **智能推荐**：基于搜索历史推荐相关内容
2. **搜索分析**：用户搜索行为分析
3. **自动补全**：基于ES的搜索建议

### 技术优化

1. **缓存层**：Redis缓存热门搜索结果
2. **索引优化**：ES索引结构优化
3. **分布式搜索**：支持多ES集群

## 总结

新的统一搜索系统成功实现了ES与Prisma的深度整合，在保持搜索性能的同时确保了数据展示的一致性和完整性。系统具有良好的扩展性和兼容性，为后续功能扩展奠定了坚实基础。
