# 配置驱动功能使用指南

## 🎯 概述

配置驱动功能允许你通过简单的数据库配置来控制前端的筛选行为，无需修改任何代码即可实现多选筛选、单选筛选、文本搜索等功能。

## ✨ 核心特性

- 🔄 **零代码实现**：只需配置数据库即可
- 🎨 **多种筛选类型**：支持单选、多选、文本、日期范围等
- 🚀 **开箱即用**：前端自动渲染对应组件
- 🛡️ **配置验证**：自动验证配置正确性
- 📊 **模板系统**：快速创建标准配置

## 🛠️ 支持的筛选类型

| 筛选类型 | 前端组件 | 适用场景 | 示例 |
|----------|----------|----------|------|
| `select` | 单选下拉框 | 状态、级别等单选 | 审批状态 |
| `multi_select` | 多选复选框 | 分类、标签等多选 | 器械类别 ⭐ |
| `input` | 文本输入框 | 名称、描述等文本搜索 | 产品名称 |
| `date_range` | 日期范围选择器 | 时间筛选 | 批准日期 |
| `checkbox` | 复选框组 | 是/否选项 | 是否有效 |

## 🚀 快速开始

### 1. 为现有字段启用多选

```typescript
import { ConfigManager } from './scripts/config-manager';

// 为现有数据库的字段启用多选筛选
await ConfigManager.updateFilterTypes('your_database', [
  { fieldName: 'category', filterType: 'multi_select' },
  { fieldName: 'tags', filterType: 'multi_select' },
]);
```

### 2. 使用模板创建新数据库

```typescript
import { QUICK_SETUPS } from './scripts/quick-setup-database';

// 创建医疗器械数据库（自动包含多选字段）
await QUICK_SETUPS.createMedicalDeviceDB(
  'device_eu',
  '欧盟医疗器械数据库',
  'premium'
);

// 创建药品数据库
await QUICK_SETUPS.createDrugDB(
  'drug_cn',
  '中国药品数据库',
  'free'
);
```

### 3. 创建自定义数据库

```typescript
// 创建完全自定义的数据库配置
await QUICK_SETUPS.createCustomDB({
  code: 'clinical_trial',
  name: '临床试验数据库',
  category: '临床研究',
  accessLevel: 'premium',
  customFields: [
    { fieldName: 'phase', displayName: '试验阶段', filterType: 'multi_select' },
    { fieldName: 'indication', displayName: '适应症', filterType: 'multi_select' },
    { fieldName: 'status', displayName: '试验状态', filterType: 'select' },
  ],
});
```

## 📋 配置模板

### 医疗器械模板
自动包含以下多选字段：
- `deviceclass` - 器械类别
- `category` - 产品分类

### 药品模板
自动包含以下多选字段：
- `activeIngredient` - 活性成分
- `dosageForm` - 剂型
- `therapeuticArea` - 治疗领域

### 专利模板
自动包含以下多选字段：
- `patentType` - 专利类型

## 🔧 管理工具

### 配置验证
```bash
# 验证数据库配置
npx tsx -e "
import { ConfigManager } from './scripts/config-manager';
await ConfigManager.validateConfig('your_database');
"
```

### 列出所有配置
```bash
# 查看所有数据库配置
npx tsx -e "
import { ConfigManager } from './scripts/config-manager';
await ConfigManager.listConfigs();
"
```

### 导出配置
```bash
# 导出配置为 JSON 文件
npx tsx -e "
import { ConfigManager } from './scripts/config-manager';
await ConfigManager.exportConfig('your_database');
"
```

## 🎨 前端效果

### 多选筛选器
```typescript
// 前端自动渲染为多选组件
<MultiSelect
  options={[
    { value: 'I', label: 'I', count: 1250 },
    { value: 'II', label: 'II', count: 3420 },
    { value: 'III', label: 'III', count: 890 },
  ]}
  value={['I', 'II']}
  onValueChange={(value) => handleFilterChange('deviceclass', value)}
/>
```

### 后端查询
```sql
-- 自动生成的查询条件
WHERE deviceclass IN ('I', 'II')
```

## 🛡️ 配置验证规则

系统会自动验证以下规则：

1. **字段类型兼容性**
   - `date_range` 只能用于 `date` 字段
   - `multi_select` 不能用于 `boolean` 字段

2. **必填字段检查**
   - `fieldName` 和 `displayName` 必须填写

3. **重复字段检查**
   - 同一数据库内字段名不能重复

## 📊 实际案例

### 案例1：器械类别多选
```typescript
// 配置
{
  fieldName: 'deviceclass',
  displayName: '器械类别',
  filterType: 'multi_select',
  isFilterable: true
}

// 前端效果：用户可以同时选择 I、II、III 类器械
// 后端查询：WHERE deviceclass IN ('I', 'II', 'III')
```

### 案例2：治疗领域多选
```typescript
// 配置
{
  fieldName: 'therapeuticArea',
  displayName: '治疗领域',
  filterType: 'multi_select',
  isFilterable: true
}

// 前端效果：用户可以同时选择多个治疗领域
// 后端查询：WHERE therapeuticArea IN ('心血管', '肿瘤', '神经')
```

## 🚨 注意事项

1. **数据库字段名匹配**
   - 确保 `fieldName` 与数据库实际字段名一致
   - 注意大小写敏感

2. **缓存更新**
   - 配置更新后需要清除缓存
   - 系统会自动处理缓存刷新

3. **权限控制**
   - 配置不影响数据库访问权限
   - 权限由 `DatabaseConfig.accessLevel` 控制

## 🔄 更新现有配置

### 批量更新筛选类型
```typescript
await ConfigManager.updateFilterTypes('your_database', [
  { fieldName: 'category', filterType: 'multi_select' },
  { fieldName: 'status', filterType: 'select' },
  { fieldName: 'description', filterType: 'input' },
]);
```

### 单个字段更新
```sql
UPDATE "FieldConfig" 
SET "filterType" = 'multi_select', "updatedAt" = NOW()
WHERE "databaseCode" = 'your_database' 
  AND "fieldName" = 'your_field';
```

## 🎯 最佳实践

1. **使用模板**：优先使用预定义模板创建标准配置
2. **配置验证**：创建后立即验证配置正确性
3. **渐进更新**：先在测试环境验证，再应用到生产环境
4. **文档记录**：记录自定义配置的业务逻辑

## 🆘 故障排除

### 问题1：筛选器不显示
- 检查 `isFilterable` 是否为 `true`
- 检查 `isActive` 是否为 `true`
- 清除缓存重试

### 问题2：多选不生效
- 确认 `filterType` 为 `multi_select`
- 检查字段名是否匹配数据库字段
- 验证配置是否通过

### 问题3：API 错误
- 检查数据库模型名是否正确
- 确认字段在数据库中存在
- 查看服务器日志获取详细错误

## 📞 技术支持

如有问题，请：
1. 运行配置验证工具
2. 查看系统日志
3. 检查数据库字段映射
4. 联系技术团队
