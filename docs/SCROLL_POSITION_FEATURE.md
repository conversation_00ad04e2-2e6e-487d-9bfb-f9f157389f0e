# 表格滚动位置保持功能

## 功能概述

实现了数据表格的滚动位置保持功能，确保用户在执行排序、翻页、筛选等操作后，表格的水平滚动位置保持不变，提供更好的用户体验。

## 核心特性

### 🔒 固定第一列
- 第一列（产品名称）使用 CSS `position: sticky` 固定在左侧
- 无论如何滚动，重要信息始终可见
- 视觉上有边框分隔，层次清晰

### 📍 滚动位置保持
- **排序操作**：点击任何列头排序后，滚动位置保持不变
- **翻页操作**：上一页/下一页后，滚动位置保持不变
- **筛选操作**：应用筛选条件后，滚动位置保持不变
- **高级搜索**：使用高级搜索后，滚动位置保持不变

### 🎯 智能重置
- **重置筛选器**：点击 "Clear" 按钮时，滚动位置重置到最左侧（符合用户预期）

## 技术实现

### 架构设计

采用三重保障机制：

1. **位置监听与保存**：实时监听滚动位置并保存
2. **状态标记管理**：标记何时需要恢复位置
3. **智能恢复机制**：DOM 更新后精确恢复位置

### 核心代码结构

```typescript
// 状态管理
const scrollPositionRef = useRef<number>(0);           // 保存滚动位置
const shouldRestoreScrollRef = useRef<boolean>(false); // 是否需要恢复
const isRestoringScrollRef = useRef<boolean>(false);   // 是否正在恢复
const isDataLoadingRef = useRef<boolean>(false);       // 是否正在加载数据

// 滚动同步与位置保存
useEffect(() => {
  const handleBodyScroll = () => {
    const scrollLeft = bodyEl.scrollLeft;
    headerEl.scrollLeft = scrollLeft; // 表头同步
    
    // 只有在非恢复状态且非数据加载状态时才保存滚动位置
    if (!isRestoringScrollRef.current && !isDataLoadingRef.current) {
      scrollPositionRef.current = scrollLeft;
    }
  };
  
  bodyEl.addEventListener('scroll', handleBodyScroll);
  return () => bodyEl.removeEventListener('scroll', handleBodyScroll);
}, [data, loading]);

// 滚动位置恢复
useEffect(() => {
  if (!loading && data.length > 0 && shouldRestoreScrollRef.current) {
    requestAnimationFrame(() => {
      isRestoringScrollRef.current = true;
      
      const targetScrollLeft = scrollPositionRef.current;
      bodyEl.scrollLeft = targetScrollLeft;
      headerEl.scrollLeft = targetScrollLeft;
      
      setTimeout(() => {
        isRestoringScrollRef.current = false;
        shouldRestoreScrollRef.current = false;
        isDataLoadingRef.current = false;
      }, 100);
    });
  }
}, [loading, data.length]);
```

### 关键技术点

1. **防竞态条件**：使用 `isDataLoadingRef` 防止数据重新渲染时覆盖保存的滚动位置
2. **防循环依赖**：使用 `useRef` 而非 `useState` 避免重渲染循环
3. **DOM 时机控制**：使用 `requestAnimationFrame` 确保 DOM 完全渲染后恢复位置
4. **状态隔离**：恢复过程中暂停位置监听，避免干扰

## 使用场景

### 典型用户流程

1. 用户滚动表格到右侧查看某个字段（如 "Approval Date"）
2. 用户点击该字段进行排序
3. 数据重新排序，但滚动位置保持在原来的位置
4. 用户可以继续查看排序后的数据，无需重新滚动定位

### 支持的操作

| 操作类型 | 滚动位置行为 | 说明 |
|---------|-------------|------|
| 列排序 | ✅ 保持 | 点击可排序列头时保持滚动位置 |
| 翻页 | ✅ 保持 | 上一页/下一页时保持滚动位置 |
| 应用筛选器 | ✅ 保持 | 筛选数据时保持滚动位置 |
| 高级搜索 | ✅ 保持 | 高级搜索时保持滚动位置 |
| 清除高级搜索 | ✅ 保持 | 清除高级搜索条件时保持滚动位置 |
| 重置筛选器 | 🔄 重置 | 重置时回到最左侧（符合用户预期） |

## 配置说明

### 固定列配置

固定列通过 CSS 实现，可以通过修改以下代码调整：

```typescript
// 检查是否为第一列（需要固定）
const isFirstColumn = (index: number) => index === 0;

// 在渲染时应用 sticky 样式
className={`... ${
  isFirst ? 'sticky left-0 bg-white z-10 border-r border-gray-300' : ''
}`}
```

### 滚动位置保持配置

可以通过修改以下函数来控制哪些操作需要保持滚动位置：

```typescript
// 在需要保持滚动位置的操作中添加：
const bodyEl = bodyScrollRef.current;
if (bodyEl) {
  scrollPositionRef.current = bodyEl.scrollLeft;
  shouldRestoreScrollRef.current = true;
  isDataLoadingRef.current = true;
}
```

## 浏览器兼容性

- **CSS Sticky 支持**：现代浏览器全面支持
- **requestAnimationFrame**：IE10+ 支持
- **推荐浏览器**：Chrome 56+, Firefox 52+, Safari 10.1+, Edge 16+

## 性能考虑

1. **最小化重渲染**：使用 `useRef` 避免不必要的组件重渲染
2. **事件优化**：滚动事件监听器只在必要时绑定/解绑
3. **DOM 操作优化**：使用 `requestAnimationFrame` 确保最佳渲染时机
4. **内存管理**：及时清理事件监听器，避免内存泄漏

## 故障排除

### 常见问题

1. **滚动位置没有保持**
   - 检查 `shouldRestoreScrollRef.current` 是否为 true
   - 确认 `isDataLoadingRef.current` 在数据加载时被正确设置

2. **表头和表体不同步**
   - 检查滚动事件监听器是否正确绑定
   - 确认 `headerScrollRef` 和 `bodyScrollRef` 都指向正确的元素

3. **固定列显示异常**
   - 检查 CSS sticky 支持
   - 确认 z-index 层级设置正确

### 调试方法

可以临时添加调试日志来排查问题：

```typescript
console.log('保存滚动位置:', scrollPositionRef.current);
console.log('是否需要恢复:', shouldRestoreScrollRef.current);
console.log('是否正在加载:', isDataLoadingRef.current);
```

## 更新历史

- **2025-01-12**：实现固定第一列功能
- **2025-01-12**：实现滚动位置保持功能
- **2025-01-12**：修复竞态条件问题，确保功能稳定运行
