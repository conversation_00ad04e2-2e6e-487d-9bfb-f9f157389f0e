# 分类和数据库排序配置系统

本文档说明如何配置导航栏中分类的显示顺序以及同一分类内数据库的排序。

## 概述

导航栏中的排序分为两个层级：

1. **分类级别排序**：不同分类之间的顺序（如 Marketed vs Regular）
2. **数据库级别排序**：同一分类内数据库之间的顺序

两个层级都通过数据库中 `DatabaseConfig` 表的 `sortOrder` 字段来控制，数字越小越靠前。

## 当前配置

目前的排序配置：

### 分类顺序
1. **Marketed** (sortOrder: 101-199) - 已上市产品数据
2. **Regular** (sortOrder: 201-299) - 常规数据

### 数据库排序
- **Marketed 分类**：
  - us_pmn: US Premarket Notification (sortOrder: 101)

- **Regular 分类**：
  - us_class: US Classification (sortOrder: 201)

## 排序规则

### 分类基础 sortOrder 配置
```typescript
const CATEGORY_BASE_SORT_ORDER = {
  'Marketed': 100,     // 101-199 范围
  'Regular': 200,      // 201-299 范围
  '参考数据': 300,      // 301-399 范围
  '全球器械': 400,      // 401-499 范围
  'Regulation': 500,   // 501-599 范围
  '药物研发': 600,      // 601-699 范围
};
```

### 数据库 sortOrder 分配
- 每个分类的第一个数据库：基础值 + 1 (如 Marketed 的第一个数据库是 101)
- 后续数据库：递增 +1 (如 102, 103, 104...)
- 这样确保同一分类的数据库连续排列，不会与其他分类混合

## 管理命令

我们提供了两个管理工具：

### 1. 分类顺序管理器 (`category-order`)

用于管理分类之间的顺序：

```bash
# 查看当前分类顺序
npm run category-order show

# 设置默认分类顺序
npm run category-order set

# 交换两个分类的顺序
npm run category-order swap Marketed Regular

# 快捷命令：让 Marketed 排在 Regular 前面
npm run category-order marketed-first
```

### 2. 数据库配置助手 (`db-helper`)

用于管理数据库的添加和排序：

```bash
# 显示所有分类和数据库
npm run db-helper show

# 预览添加新数据库（不实际创建）
npm run db-helper preview Marketed eu_marketed "欧盟已上市器械"

# 生成创建数据库配置的代码模板
npm run db-helper generate Marketed eu_marketed "欧盟已上市器械" "欧盟CE认证的医疗器械"

# 重新排序分类内的数据库
npm run db-helper reorder Marketed
```

## 技术实现

### 1. 数据库配置

分类顺序通过 `DatabaseConfig` 表的 `sortOrder` 字段控制：

```sql
SELECT code, name, category, "sortOrder" 
FROM "DatabaseConfig" 
WHERE "isActive" = true 
ORDER BY "sortOrder";
```

### 2. API 接口

`/api/config/databases` 接口返回包含 `sortOrder` 信息的配置数据：

```typescript
{
  "success": true,
  "data": {
    "us_pmn": {
      "name": "US Premarket Notification",
      "category": "Marketed",
      "sortOrder": 100,
      // ...
    },
    "us_class": {
      "name": "US Classfication", 
      "category": "Regular",
      "sortOrder": 200,
      // ...
    }
  }
}
```

### 3. 前端组件

`Navigation.tsx` 组件会：

1. 获取数据库配置数据
2. 按分类分组
3. 根据每个分类的最小 `sortOrder` 值排序
4. 动态生成导航菜单

关键代码片段：

```typescript
// 创建分类到最小 sortOrder 的映射
const categoryToMinSortOrder = new Map<string, number>();
Object.values(databaseConfigs).forEach((config: any) => {
  const currentMin = categoryToMinSortOrder.get(config.category) || Infinity;
  categoryToMinSortOrder.set(config.category, Math.min(currentMin, config.sortOrder || 0));
});

// 按 sortOrder 排序分类
const sortedCategories = Array.from(new Set(Object.values(databaseConfigs).map((config: any) => config.category)))
  .sort((a, b) => {
    const sortOrderA = categoryToMinSortOrder.get(a) || 0;
    const sortOrderB = categoryToMinSortOrder.get(b) || 0;
    return sortOrderA - sortOrderB;
  });
```

## 添加新数据库

### 步骤 1：预览配置

使用助手工具预览新数据库的配置：

```bash
npm run db-helper preview <分类名> <数据库代码> <数据库名称>
```

例如：
```bash
npm run db-helper preview Marketed eu_marketed "欧盟已上市器械"
```

### 步骤 2：生成代码模板

生成创建数据库配置的代码：

```bash
npm run db-helper generate <分类名> <数据库代码> <数据库名称> [描述]
```

### 步骤 3：执行代码

将生成的代码放入脚本文件中执行，例如：

```typescript
// scripts/add-eu-marketed.ts
import { db } from '../src/lib/prisma';

async function addEuMarketed() {
  const databaseConfig = await db.databaseConfig.upsert({
    where: { code: 'eu_marketed' },
    update: {
      name: '欧盟已上市器械',
      category: 'Marketed',
      sortOrder: 102,
      isActive: true,
    },
    create: {
      code: 'eu_marketed',
      name: '欧盟已上市器械',
      category: 'Marketed',
      description: '欧盟CE认证的医疗器械',
      accessLevel: 'free',
      sortOrder: 102,
      tableName: 'eu_marketed',
      modelName: 'eu_marketed',
      isActive: true,
    },
  });

  console.log('✅ 数据库配置已创建:', databaseConfig.name);
}

addEuMarketed().finally(() => db.$disconnect());
```

然后执行：
```bash
npx tsx scripts/add-eu-marketed.ts
```

## 添加新分类

如果需要添加全新的分类：

1. 更新 `scripts/database-helper.ts` 中的 `CATEGORY_BASE_SORT_ORDER` 配置
2. 为新分类分配一个基础 sortOrder 值（建议以100为间隔）
3. 创建该分类下的第一个数据库配置

## 默认分类顺序

```typescript
const DEFAULT_CATEGORY_ORDER = {
  'Marketed': 100,     // 已上市产品数据
  'Regular': 200,      // 常规数据
  '参考数据': 300,      // 参考数据
  '全球器械': 400,      // 全球器械数据
  'Regulation': 500,   // 监管数据
  '药物研发': 600,      // 药物研发数据
};
```

## 注意事项

1. **缓存清理**：修改分类顺序后，API 有缓存机制，可能需要等待缓存过期或重启服务器
2. **数据一致性**：确保同一分类下的所有数据库配置使用相同的 `sortOrder` 值
3. **备份**：在进行大量修改前，建议备份数据库配置

## 故障排除

### 分类顺序没有生效

1. 检查数据库中的 `sortOrder` 值是否正确设置
2. 清除浏览器缓存
3. 重启开发服务器
4. 检查 API 返回的数据是否包含正确的 `sortOrder` 信息

### 验证配置

使用测试脚本验证 API 返回的数据：

```bash
npx tsx scripts/test-api-order.ts
```

这个脚本会显示当前的分类顺序并验证 Marketed 是否排在 Regular 前面。
