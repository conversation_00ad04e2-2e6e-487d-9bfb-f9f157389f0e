# 统计排序配置功能实现

## 📊 功能概述

实现了可配置的统计排序功能，允许通过数据库配置来控制统计结果的排序方向，而不是硬编码在代码中。

## 🔧 实现内容

### 1. 数据库字段扩展

在 `FieldConfig` 表中添加了新字段：
- `statisticsSortOrder`: VARCHAR(10) - 控制统计排序方向 ('asc' 或 'desc')
- 默认值: 'desc' (保持向后兼容)

### 2. 配置示例

为 `us_class` 数据库配置了以下统计：

| 字段名 | 显示名称 | 排序方向 | 说明 |
|--------|----------|----------|------|
| deviceclass | 器械类别分布 | desc 📉 | 按数量倒序，显示最多的类别在前 |
| productcode | 产品代码统计 | asc 📈 | 按字母正序，便于查找特定代码 |
| medicalspecialty | 医学专科分布 | desc 📉 | 按数量倒序，显示最多的专科在前 |
| regulationnumber | 法规编号统计 | desc 📉 | 按数量倒序，显示最多的法规在前 |

### 3. API 更新

#### 可配置统计 API
- 路径: `/api/stats/[database]/configurable`
- 自动读取 `fieldConfig` 中的 `statisticsSortOrder` 配置
- 支持动态排序方向

#### 传统统计 API
- 路径: `/api/stats/[database]`
- 也更新为支持配置化排序
- 向后兼容现有功能

### 4. 类型定义更新

```typescript
export interface DatabaseFieldConfig {
  // ... 其他字段
  statisticsSortOrder?: 'asc' | 'desc';
  // ... 其他统计配置
}
```

## 🚀 使用方法

### 1. 配置统计字段

```typescript
// 通过脚本配置
npx tsx scripts/configure-us-class-statistics.ts

// 或直接在数据库中更新
UPDATE "FieldConfig" 
SET 
  "isStatisticsEnabled" = true,
  "statisticsType" = 'group_by',
  "statisticsDisplayName" = '器械类别分布',
  "statisticsSortOrder" = 'desc'
WHERE 
  "databaseCode" = 'us_class' 
  AND "fieldName" = 'deviceclass';
```

### 2. 前端使用

```typescript
// 使用可配置统计面板
import ConfigurableStatsPanel from '@/components/ConfigurableStatsPanel';

<ConfigurableStatsPanel 
  database="us_class"
  isOpen={isStatsOpen}
  onToggle={() => setIsStatsOpen(!isStatsOpen)}
  filters={currentFilters}
/>
```

### 3. API 调用

```javascript
// 获取可配置统计数据
const response = await fetch('/api/stats/us_class/configurable');
const data = await response.json();

// 返回格式
{
  "success": true,
  "data": {
    "basic": {
      "total": 1000,
      "active": 950,
      "inactive": 50
    },
    "statistics": [
      {
        "fieldName": "deviceclass",
        "displayName": "器械类别分布",
        "statisticsType": "group_by",
        "data": {
          "type": "group_by",
          "items": [
            { "name": "Class II", "count": 500 },
            { "name": "Class I", "count": 300 },
            { "name": "Class III", "count": 200 }
          ]
        },
        "order": 1
      }
    ]
  }
}
```

## 📈 配置优势

### 1. 灵活性
- ✅ 无需修改代码即可调整排序
- ✅ 支持不同字段使用不同排序方向
- ✅ 可以随时通过数据库配置调整

### 2. 用户体验
- ✅ 器械类别按数量倒序 - 快速看到主要类别
- ✅ 产品代码按字母正序 - 便于查找特定代码
- ✅ 统计结果更符合业务需求

### 3. 维护性
- ✅ 配置与代码分离
- ✅ 向后兼容现有功能
- ✅ 统一的配置管理

## 🔄 排序逻辑对比

### 之前（硬编码）
```typescript
// 所有统计都是倒序
orderBy: { _count: { [fieldName]: 'desc' } }
```

### 现在（可配置）
```typescript
// 根据配置动态排序
const sortOrder = fieldConfig.statisticsSortOrder || 'desc';
orderBy: { _count: { [fieldName]: sortOrder } }
```

## 🎯 实际效果

### 器械类别分布 (倒序 📉)
```
Class II    (500) ████████████████████
Class I     (300) ████████████
Class III   (200) ████████
```

### 产品代码统计 (正序 📈)
```
AAA         (15)  ███
BBB         (25)  █████
CCC         (10)  ██
DDD         (30)  ██████
```

## 🛠️ 相关文件

- `scripts/add-statistics-sort-config.ts` - 添加排序字段
- `scripts/configure-us-class-statistics.ts` - 配置 us_class 统计
- `src/app/api/stats/[database]/configurable/route.ts` - 可配置统计 API
- `src/lib/configCache.ts` - 配置缓存和类型定义
- `src/components/ConfigurableStatsPanel.tsx` - 前端统计面板

## 📝 总结

通过这次实现，统计排序从硬编码变为完全可配置，提高了系统的灵活性和用户体验。现在可以根据不同字段的业务特点，选择最合适的排序方向，让统计结果更有意义。
