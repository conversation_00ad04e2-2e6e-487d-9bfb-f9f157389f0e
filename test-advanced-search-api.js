#!/usr/bin/env node

/**
 * Test Advanced Search API with Sample Conditions
 * 
 * This script tests the advanced search API with the exact conditions
 * that would be generated from the filter-to-advanced-search conversion.
 */

const fetch = require('node-fetch');

async function testAdvancedSearchAPI() {
  console.log('🧪 Testing Advanced Search API');
  console.log('='.repeat(40));

  // Sample conditions that would be generated from filters:
  // devicename: "catheter", applicant: "medtronic"
  const testConditions = [
    {
      id: "filter_devicename_1",
      field: "devicename",
      operator: "contains",
      value: "catheter"
    },
    {
      id: "filter_applicant_2", 
      field: "applicant",
      operator: "contains",
      value: "medtronic",
      logic: "AND"
    }
  ];

  const requestBody = {
    conditions: testConditions,
    page: 1,
    limit: 20,
    sortOrder: "desc"
  };

  console.log('\n📤 Request Body:');
  console.log(JSON.stringify(requestBody, null, 2));

  try {
    console.log('\n🔄 Making API call to /api/advanced-search/us_pmn...');
    
    const response = await fetch('http://localhost:3000/api/advanced-search/us_pmn', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    console.log(`\n📊 Response Status: ${response.status} ${response.statusText}`);

    const responseData = await response.json();
    
    if (response.ok) {
      console.log('\n✅ API Call Successful!');
      console.log(`📈 Results: ${responseData.data?.length || 0} records found`);
      console.log(`📄 Pagination: Page ${responseData.pagination?.page || 1} of ${responseData.pagination?.totalPages || 1}`);
      
      if (responseData.data && responseData.data.length > 0) {
        console.log('\n🔍 Sample Result:');
        const sample = responseData.data[0];
        console.log(`   ID: ${sample.id}`);
        console.log(`   Device Name: ${sample.devicename || 'N/A'}`);
        console.log(`   Applicant: ${sample.applicant || 'N/A'}`);
      }
    } else {
      console.log('\n❌ API Call Failed!');
      console.log('📋 Error Response:');
      console.log(JSON.stringify(responseData, null, 2));
    }

  } catch (error) {
    console.log('\n💥 Network Error:');
    console.log(error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n🚨 Server not running! Please start with: npm run dev');
    }
  }

  console.log('\n' + '='.repeat(40));
}

// Run the test
testAdvancedSearchAPI().catch(console.error);
