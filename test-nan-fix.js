// Test script to verify NaN fix
console.log('🧪 Testing NaN fix for pagination...');

// Test cases that could cause NaN
const testCases = [
  { totalPages: 0, maxPages: 100, description: 'totalPages is 0' },
  { totalPages: undefined, maxPages: 100, description: 'totalPages is undefined' },
  { totalPages: null, maxPages: 100, description: 'totalPages is null' },
  { totalPages: NaN, maxPages: 100, description: 'totalPages is NaN' },
  { totalPages: 50, maxPages: undefined, description: 'maxPages is undefined' },
  { totalPages: 50, maxPages: null, description: 'maxPages is null' },
  { totalPages: 50, maxPages: NaN, description: 'maxPages is NaN' },
  { totalPages: 50, maxPages: 100, description: 'Both values are valid' },
];

console.log('\n📋 Test Results:');
console.log('================');

testCases.forEach((testCase, index) => {
  // Original problematic code
  const originalResult = Math.min(testCase.totalPages, testCase.maxPages);
  
  // Fixed code
  const fixedResult = Math.min(testCase.totalPages || 1, testCase.maxPages || 100);
  
  const originalIsNaN = isNaN(originalResult);
  const fixedIsNaN = isNaN(fixedResult);
  
  console.log(`\nTest ${index + 1}: ${testCase.description}`);
  console.log(`  Input: totalPages=${testCase.totalPages}, maxPages=${testCase.maxPages}`);
  console.log(`  Original: ${originalResult} ${originalIsNaN ? '❌ (NaN!)' : '✅'}`);
  console.log(`  Fixed:    ${fixedResult} ${fixedIsNaN ? '❌ (NaN!)' : '✅'}`);
  console.log(`  Status:   ${originalIsNaN && !fixedIsNaN ? '🎉 FIXED!' : !originalIsNaN && !fixedIsNaN ? '✅ OK' : '❌ STILL BROKEN'}`);
});

console.log('\n🎯 Summary:');
console.log('===========');

const originalFailures = testCases.filter(tc => isNaN(Math.min(tc.totalPages, tc.maxPages))).length;
const fixedFailures = testCases.filter(tc => isNaN(Math.min(tc.totalPages || 1, tc.maxPages || 100))).length;

console.log(`Original code: ${originalFailures}/${testCases.length} tests failed (produced NaN)`);
console.log(`Fixed code:    ${fixedFailures}/${testCases.length} tests failed (produced NaN)`);

if (fixedFailures === 0) {
  console.log('🎉 All tests pass! NaN issue is fixed.');
} else {
  console.log('❌ Some tests still fail. Fix needs improvement.');
}

// Test the specific error scenario
console.log('\n🔍 Specific Error Scenario Test:');
console.log('=================================');

// Simulate the exact scenario that caused the error
const pagination = {
  totalPages: 0,  // Initial state before API response
  maxPages: 100
};

const problematicMax = Math.min(pagination.totalPages, pagination.maxPages);
const fixedMax = Math.min(pagination.totalPages || 1, pagination.maxPages || 100);

console.log(`Problematic max attribute: ${problematicMax} ${isNaN(problematicMax) ? '❌ (Would cause React error!)' : '✅'}`);
console.log(`Fixed max attribute:       ${fixedMax} ${isNaN(fixedMax) ? '❌' : '✅'}`);

if (!isNaN(fixedMax)) {
  console.log('✅ The specific error "Received NaN for the `max` attribute" should be resolved!');
} else {
  console.log('❌ The error would still occur.');
}
