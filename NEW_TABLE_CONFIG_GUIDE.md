# 新表配置指南

本指南详细说明如何为新的数据库和表添加字段映射配置，使系统支持任意新表的导入功能。

## 概述

系统现在支持动态表配置，您可以通过修改 `src/lib/uniqueKeyConfig.ts` 文件来添加新表的支持，而无需修改核心导入代码。

## 配置步骤

### 1. 在 Prisma Schema 中定义新表

首先在 `prisma/schema.prisma` 中定义您的数据表：

```prisma
model YourNewTable {
  id                 String   @id @default(cuid())
  // 您的字段定义...
  field1             String
  field2             String?
  field3             DateTime?
  database           String   @db.VarChar(50)
  businessKey        String   @unique @db.VarChar(200)
  businessKeyHash    String?  @unique
  dataVersion        Int      @default(1)
  isActive           Boolean  @default(true)
  importedAt         DateTime @default(now())
  updatedAt          DateTime @updatedAt
  createdAt          DateTime @default(now())
  
  @@index([database])
  @@index([businessKey])
  @@index([businessKeyHash])
}
```

### 2. 在 uniqueKeyConfig.ts 中添加表配置

在 `src/lib/uniqueKeyConfig.ts` 文件的 `tableConfigs` 对象中添加新表配置：

```typescript
// 在 tableConfigs 对象中添加
YourNewTable: {
  uniqueKeyRule: (row) => {
    // 定义业务唯一键生成规则
    // 例如：使用字段1+字段2+数据库类型
    return `${row.field1}_${row.field2}_${row.database}`;
  },
  importMode: 'upsert', // 导入模式：insert | upsert | replace
  description: '您的表描述',
  hashConfig: {
    enabled: true, // 是否启用Hash
    algorithm: 'md5', // Hash算法：md5 | sha256 | none
    useHashForIndex: true, // 是否使用Hash作为主要索引
  },
  fieldMapping: {
    // 中英文字段名映射
    '中文字段名1': 'field1',
    '中文字段名2': 'field2',
    '中文字段名3': 'field3',
    // 可以添加更多映射...
  },
  validationRules: {
    requiredFields: ['field1', 'field2'], // 必填字段
    uniqueFields: ['businessKey', 'businessKeyHash'], // 唯一字段
  },
},
```

### 3. 生成数据库迁移

```bash
npm run prisma:migrate
```

### 4. 使用新表进行导入

```bash
# CSV导入
npm run import-csv ./data/your-data.csv yourDatabaseType YourNewTable

# Excel导入
npm run import-excel ./data/your-data.xlsx yourDatabaseType YourNewTable "Sheet1"
```

## 配置详解

### uniqueKeyRule（业务唯一键生成规则）

定义如何生成业务唯一键，用于判断数据是否重复：

```typescript
// 示例1：使用单个字段
uniqueKeyRule: (row) => `${row.id}_${row.database}`,

// 示例2：使用多个字段组合
uniqueKeyRule: (row) => `${row.field1}_${row.field2}_${row.database}`,

// 示例3：条件判断
uniqueKeyRule: (row) => {
  if (row.primaryKey && row.primaryKey.trim()) {
    return `${row.primaryKey}_${row.database}`;
  } else {
    return `${row.field1}_${row.field2}_${row.database}`;
  }
},
```

### fieldMapping（字段映射）

定义CSV/Excel中的字段名与数据库字段名的映射关系：

```typescript
fieldMapping: {
  // 支持多种格式
  '产品名称': 'productName',           // 中文 → 英文
  'Product Name': 'productName',       // 英文 → 英文
  'product_name': 'productName',       // 下划线 → 驼峰
  'PRODUCT_NAME': 'productName',       // 大写 → 驼峰
},
```

### validationRules（验证规则）

定义数据验证规则：

```typescript
validationRules: {
  requiredFields: ['field1', 'field2'], // 必填字段
  uniqueFields: ['businessKey'],        // 唯一字段
},
```

### hashConfig（Hash配置）

配置Hash相关选项：

```typescript
hashConfig: {
  enabled: true,           // 是否启用Hash
  algorithm: 'md5',        // Hash算法：md5 | sha256 | none
  useHashForIndex: true,   // 是否使用Hash作为主要索引
},
```

## 实际示例

### 医疗器械表（MedicalDevice）

```typescript
MedicalDevice: {
  uniqueKeyRule: (row) => {
    if (row.registrationNumber && row.registrationNumber.trim()) {
      return `${row.registrationNumber}_${row.database}`;
    } else {
      return `${row.productName}_${row.companyName}_${row.database}`;
    }
  },
  importMode: 'upsert',
  description: '医疗器械数据表，支持智能同步更新和Hash优化',
  hashConfig: {
    enabled: true,
    algorithm: 'md5',
    useHashForIndex: true,
  },
  fieldMapping: {
    '产品名称': 'productName',
    '公司名称': 'companyName',
    '注册证号': 'registrationNumber',
    // ... 更多字段映射
  },
  validationRules: {
    requiredFields: ['productName', 'companyName', 'database'],
    uniqueFields: ['businessKey', 'businessKeyHash'],
  },
},
```

### 公司表（Company）

```typescript
Company: {
  uniqueKeyRule: (row) => {
    if (row.companyCode && row.companyCode.trim()) {
      return `${row.companyCode}_${row.region || 'CN'}`;
    } else {
      return `${row.companyName}_${row.region || 'CN'}`;
    }
  },
  importMode: 'upsert',
  description: '公司信息表，支持公司代码+地区唯一性',
  hashConfig: {
    enabled: true,
    algorithm: 'md5',
    useHashForIndex: true,
  },
  fieldMapping: {
    '公司名称': 'companyName',
    '公司代码': 'companyCode',
    '地区': 'region',
    // ... 更多字段映射
  },
  validationRules: {
    requiredFields: ['companyName', 'region'],
    uniqueFields: ['businessKey', 'businessKeyHash'],
  },
},
```

## 导入模式说明

- **insert**: 仅插入新数据，如果数据已存在则跳过
- **upsert**: 智能同步，如果数据已存在则更新，否则插入
- **replace**: 全量替换，先删除所有数据，再插入新数据

## 注意事项

1. **字段映射**: 确保CSV/Excel中的字段名与映射配置中的源字段名完全匹配
2. **必填字段**: 在validationRules中定义的必填字段必须在数据中存在且不为空
3. **唯一键规则**: 确保uniqueKeyRule生成的键在数据中是唯一的
4. **数据库字段**: 确保Prisma schema中的字段名与fieldMapping中的目标字段名一致

## 故障排除

### 常见错误

1. **"不支持的表"**: 检查表名是否正确，是否已在tableConfigs中配置
2. **"字段映射错误"**: 检查fieldMapping配置是否正确
3. **"必填字段缺失"**: 检查数据中是否包含所有必填字段
4. **"业务唯一键重复"**: 检查uniqueKeyRule生成的键是否唯一

### 调试技巧

1. 使用 `npm run import-csv --help` 查看详细使用说明
2. 检查导入过程中的数据样本输出
3. 查看字段映射配置是否正确显示
4. 使用Prisma Studio查看数据库中的数据

## 扩展功能

系统还提供了以下扩展功能：

- **数据验证**: 自动验证必填字段和数据类型
- **数据清理**: 自动去除空格和空值
- **类型转换**: 支持自动类型转换（字符串→数字/布尔值/日期）
- **变更日志**: 自动记录数据变更历史

通过这些配置，您可以轻松地为任何新表添加导入支持，而无需修改核心代码！ 