#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 为 FieldConfig 表添加导出相关字段
 * 注意：这个脚本只添加字段，不删除任何现有数据
 */

async function addExportFields() {
  console.log('🔧 为 FieldConfig 表添加导出相关字段...\n');

  try {
    // 使用原生SQL添加字段，避免Prisma迁移删除数据
    console.log('📋 添加导出相关字段...');
    
    // 1. 添加 isExportable 字段
    try {
      await db.$executeRaw`
        ALTER TABLE "FieldConfig" 
        ADD COLUMN IF NOT EXISTS "isExportable" BOOLEAN DEFAULT true;
      `;
      console.log('  ✅ 添加 isExportable 字段成功');
    } catch (error) {
      console.log('  ℹ️  isExportable 字段可能已存在');
    }

    // 2. 添加 exportOrder 字段
    try {
      await db.$executeRaw`
        ALTER TABLE "FieldConfig" 
        ADD COLUMN IF NOT EXISTS "exportOrder" INTEGER DEFAULT 0;
      `;
      console.log('  ✅ 添加 exportOrder 字段成功');
    } catch (error) {
      console.log('  ℹ️  exportOrder 字段可能已存在');
    }

    // 3. 添加 exportDisplayName 字段
    try {
      await db.$executeRaw`
        ALTER TABLE "FieldConfig" 
        ADD COLUMN IF NOT EXISTS "exportDisplayName" VARCHAR(100);
      `;
      console.log('  ✅ 添加 exportDisplayName 字段成功');
    } catch (error) {
      console.log('  ℹ️  exportDisplayName 字段可能已存在');
    }

    // 4. 验证字段添加结果
    console.log('\n🔍 验证字段添加结果...');
    
    // 查询表结构
    const tableInfo = await db.$queryRaw`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'FieldConfig' 
      AND column_name IN ('isExportable', 'exportOrder', 'exportDisplayName')
      ORDER BY column_name;
    ` as any[];

    if (tableInfo.length > 0) {
      console.log('  📋 新增字段信息:');
      tableInfo.forEach((col: any) => {
        console.log(`    - ${col.column_name}: ${col.data_type} (默认值: ${col.column_default || 'NULL'})`);
      });
    } else {
      console.log('  ❌ 未找到新增字段，可能添加失败');
    }

    // 5. 更新现有记录的导出配置
    console.log('\n🔧 初始化现有记录的导出配置...');
    
    // 为所有现有记录设置默认的导出配置
    const updateResult = await db.$executeRaw`
      UPDATE "FieldConfig" 
      SET 
        "isExportable" = COALESCE("isExportable", "isVisible"),
        "exportOrder" = COALESCE("exportOrder", "listOrder"),
        "exportDisplayName" = COALESCE("exportDisplayName", "displayName")
      WHERE "isExportable" IS NULL OR "exportOrder" IS NULL OR "exportDisplayName" IS NULL;
    `;

    console.log(`  ✅ 更新了现有记录的导出配置`);

    // 6. 验证配置结果
    console.log('\n📊 验证配置结果...');
    
    const usPmnCount = await db.fieldConfig.count({
      where: { databaseCode: 'us_pmn', isActive: true }
    });
    
    const usClassCount = await db.fieldConfig.count({
      where: { databaseCode: 'us_class', isActive: true }
    });

    console.log(`  - us_pmn 字段配置数量: ${usPmnCount}`);
    console.log(`  - us_class 字段配置数量: ${usClassCount}`);

    // 显示一些示例配置
    const sampleConfigs = await db.fieldConfig.findMany({
      where: { 
        databaseCode: { in: ['us_pmn', 'us_class'] },
        isActive: true 
      },
      select: {
        databaseCode: true,
        fieldName: true,
        displayName: true,
        isVisible: true,
        isExportable: true,
        exportOrder: true,
        exportDisplayName: true,
      },
      take: 5,
      orderBy: { databaseCode: 'asc' }
    });

    console.log('\n📋 示例配置:');
    sampleConfigs.forEach((config, index) => {
      console.log(`  ${index + 1}. ${config.databaseCode}.${config.fieldName}`);
      console.log(`     显示: ${config.isVisible} | 导出: ${config.isExportable}`);
      console.log(`     导出名称: ${config.exportDisplayName}`);
    });

    console.log('\n✅ 导出字段添加完成！');
    console.log('\n📋 后续步骤:');
    console.log('  1. 重新生成 Prisma 客户端: npx prisma generate');
    console.log('  2. 配置具体的导出字段设置');
    console.log('  3. 更新导出API使用新字段');

  } catch (error) {
    console.error('❌ 添加导出字段时出错:', error);
  } finally {
    await db.$disconnect();
  }
}

// 运行脚本
if (require.main === module) {
  addExportFields();
}
