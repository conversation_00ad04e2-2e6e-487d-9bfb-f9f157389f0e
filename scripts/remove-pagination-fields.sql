-- 删除 DatabaseConfig 表中的翻页相关字段
-- 注意：这个脚本只删除字段，不会删除或修改其他数据

-- 1. 删除翻页配置字段
-- 使用 IF EXISTS 避免字段不存在时的错误

-- 删除 defaultPageSize 字段
ALTER TABLE "DatabaseConfig" 
DROP COLUMN IF EXISTS "defaultPageSize";

-- 删除 maxPageSize 字段  
ALTER TABLE "DatabaseConfig" 
DROP COLUMN IF EXISTS "maxPageSize";

-- 删除 maxPages 字段
ALTER TABLE "DatabaseConfig" 
DROP COLUMN IF EXISTS "maxPages";

-- 2. 验证删除结果
-- 查看表结构确认字段已删除
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'DatabaseConfig' 
  AND table_schema = 'public'
ORDER BY ordinal_position;

-- 3. 验证现有数据完整性
-- 确认删除字段后其他数据没有受影响
SELECT 
  "code",
  "name", 
  "category",
  "description",
  "accessLevel",
  "isActive",
  "createdAt",
  "updatedAt"
FROM "DatabaseConfig" 
WHERE "isActive" = true
ORDER BY "code";
