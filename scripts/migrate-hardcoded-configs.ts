#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 将硬编码配置迁移到数据库配置表的脚本
 * 这个脚本会：
 * 1. 检查现有的硬编码配置
 * 2. 将缺失的配置添加到 DatabaseConfig 表
 * 3. 不会覆盖已有的配置数据
 */

// 硬编码的会员权益配置 -> 迁移到配置表
const MEMBERSHIP_BENEFITS_CONFIG = {
  free: {
    queryLimit: 50,
    exportLimit: 0,
    accessDatabases: ['deviceCNImported', 'freePat', 'deviceCNEvaluation']
  },
  premium: {
    queryLimit: -1, // 无限制
    exportLimit: 1000,
    accessDatabases: [
      'deviceCNImported', 'freePat', 'deviceCNEvaluation',
      'deviceHK', 'deviceUS', 'deviceJP', 'deviceUK', 'us_pmn', 'subjectNewdrug'
    ]
  },
  enterprise: {
    queryLimit: -1,
    exportLimit: -1,
    accessDatabases: [
      'deviceCNImported', 'freePat', 'deviceCNEvaluation',
      'deviceHK', 'deviceUS', 'deviceJP', 'deviceUK', 'us_pmn', 'subjectNewdrug',
      'subjectLicenseout', 'subjectVbp'
    ]
  }
};

// 硬编码的数据库图标配置 -> 迁移到 exportConfig 字段
const DATABASE_ICONS = {
  'deviceCNImported': '🇨🇳',
  'deviceCNEvaluation': '🇨🇳',
  'deviceHK': '🇭🇰',
  'deviceUS': '🇺🇸',
  'deviceJP': '🇯🇵',
  'deviceUK': '🇬🇧',
  'deviceSG': '🇸🇬',
  'us_pmn': '🇺🇸',
  'us_class': '🇺🇸',
  'freePat': '💊',
  'subjectNewdrug': '🌍',
  'subjectLicenseout': '📄',
  'subjectVbp': '🏥'
};

// 硬编码的 API URL 配置 -> 迁移到环境变量或配置表
const API_ENDPOINTS = {
  elasticsearch: process.env.ELASTICSEARCH_URL || 'http://localhost:9200',
  redis: process.env.REDIS_URL || 'redis://localhost:6379',
  database: process.env.DATABASE_URL || 'postgresql://localhost:5432/medicaldb'
};

async function migrateHardcodedConfigs() {
  console.log('🚀 开始迁移硬编码配置到数据库配置表...\n');

  try {
    // 1. 迁移数据库访问级别配置
    console.log('📋 1. 迁移数据库访问级别配置...');
    await migrateDatabaseAccessLevels();

    // 2. 迁移数据库图标配置
    console.log('\n🎨 2. 迁移数据库图标配置...');
    await migrateDatabaseIcons();

    // 3. 检查环境变量配置
    console.log('\n🔧 3. 检查环境变量配置...');
    await checkEnvironmentVariables();

    // 4. 生成配置清理建议
    console.log('\n🧹 4. 生成配置清理建议...');
    await generateCleanupSuggestions();

    console.log('\n✅ 硬编码配置迁移完成！');

  } catch (error) {
    console.error('\n❌ 迁移失败:', error);
    throw error;
  } finally {
    await db.$disconnect();
  }
}

async function migrateDatabaseAccessLevels() {
  let updatedCount = 0;

  for (const [accessLevel, config] of Object.entries(MEMBERSHIP_BENEFITS_CONFIG)) {
    for (const dbCode of config.accessDatabases) {
      try {
        // 检查数据库配置是否存在
        const existingConfig = await db.databaseConfig.findUnique({
          where: { code: dbCode },
          select: { id: true, code: true, accessLevel: true }
        });

        if (!existingConfig) {
          console.log(`⚠️  [${dbCode}] 数据库配置不存在，跳过...`);
          continue;
        }

        // 如果访问级别未设置或不正确，则更新
        if (!existingConfig.accessLevel || existingConfig.accessLevel !== accessLevel) {
          await db.databaseConfig.update({
            where: { code: dbCode },
            data: { accessLevel: accessLevel as 'free' | 'premium' | 'enterprise' }
          });

          console.log(`✅ [${dbCode}] 访问级别更新为: ${accessLevel}`);
          updatedCount++;
        } else {
          console.log(`ℹ️  [${dbCode}] 访问级别已正确: ${accessLevel}`);
        }

      } catch (error) {
        console.error(`❌ [${dbCode}] 更新访问级别失败:`, error);
      }
    }
  }

  console.log(`📊 访问级别配置更新完成，共更新 ${updatedCount} 个数据库`);
}

async function migrateDatabaseIcons() {
  let updatedCount = 0;

  for (const [dbCode, icon] of Object.entries(DATABASE_ICONS)) {
    try {
      // 检查数据库配置是否存在
      const existingConfig = await db.databaseConfig.findUnique({
        where: { code: dbCode },
        select: { id: true, code: true, exportConfig: true }
      });

      if (!existingConfig) {
        console.log(`⚠️  [${dbCode}] 数据库配置不存在，跳过...`);
        continue;
      }

      // 解析现有的 exportConfig
      let exportConfig: any = {};
      if (existingConfig.exportConfig) {
        try {
          exportConfig = typeof existingConfig.exportConfig === 'string' 
            ? JSON.parse(existingConfig.exportConfig)
            : existingConfig.exportConfig;
        } catch (e) {
          console.warn(`[${dbCode}] 解析 exportConfig 失败，使用空对象`);
        }
      }

      // 如果图标未设置，则添加
      if (!exportConfig.icon) {
        exportConfig.icon = icon;

        await db.databaseConfig.update({
          where: { code: dbCode },
          data: { exportConfig: exportConfig }
        });

        console.log(`✅ [${dbCode}] 图标设置为: ${icon}`);
        updatedCount++;
      } else {
        console.log(`ℹ️  [${dbCode}] 图标已存在: ${exportConfig.icon}`);
      }

    } catch (error) {
      console.error(`❌ [${dbCode}] 更新图标失败:`, error);
    }
  }

  console.log(`📊 图标配置更新完成，共更新 ${updatedCount} 个数据库`);
}

async function checkEnvironmentVariables() {
  console.log('🔍 检查关键环境变量配置:');
  
  for (const [key, defaultValue] of Object.entries(API_ENDPOINTS)) {
    const envVar = key.toUpperCase() + '_URL';
    const currentValue = process.env[envVar];
    
    if (!currentValue) {
      console.log(`⚠️  ${envVar} 未设置，使用默认值: ${defaultValue}`);
    } else {
      console.log(`✅ ${envVar} 已设置: ${currentValue}`);
    }
  }
}

async function generateCleanupSuggestions() {
  console.log('📝 配置清理建议:');
  console.log('');
  console.log('1. 可以删除的硬编码配置:');
  console.log('   - src/lib/permissions.ts 中的 MEMBERSHIP_BENEFITS 常量');
  console.log('   - src/lib/dynamicTableMapping.ts 中的 DEPRECATED_DATABASE_TABLE_MAPPING');
  console.log('   - 各种硬编码的图标映射');
  console.log('');
  console.log('2. 需要迁移到环境变量的配置:');
  console.log('   - API 端点 URL');
  console.log('   - 数据库连接字符串');
  console.log('   - 第三方服务密钥');
  console.log('');
  console.log('3. 建议使用配置表管理的内容:');
  console.log('   - 数据库访问权限');
  console.log('   - 字段显示配置');
  console.log('   - 筛选器配置');
  console.log('   - 统计配置');
}

// 运行迁移
if (require.main === module) {
  migrateHardcodedConfigs().catch(console.error);
}

export { migrateHardcodedConfigs };
