#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 清除配置缓存并验证数据库配置
 * 这个脚本会：
 * 1. 直接查询数据库验证 us_pmn 的配置
 * 2. 提供清除缓存的建议
 */

async function clearConfigCache() {
  console.log('🔄 开始验证数据库配置...');

  try {
    // 直接查询数据库获取 us_pmn 配置
    const usPmnConfig = await db.databaseConfig.findUnique({
      where: { code: 'us_pmn' },
      select: { code: true, name: true, accessLevel: true, isActive: true }
    });

    if (!usPmnConfig) {
      console.log('❌ 未找到 us_pmn 数据库配置');
      return;
    }

    console.log('📋 数据库中的 us_pmn 配置:');
    console.log(`   代码: ${usPmnConfig.code}`);
    console.log(`   名称: ${usPmnConfig.name}`);
    console.log(`   访问级别: ${usPmnConfig.accessLevel}`);
    console.log(`   是否激活: ${usPmnConfig.isActive}`);

    // 查询所有数据库配置
    const allConfigs = await db.databaseConfig.findMany({
      where: { isActive: true },
      select: { code: true, name: true, accessLevel: true },
      orderBy: { sortOrder: 'asc' }
    });

    console.log('\n📊 所有激活的数据库配置:');
    allConfigs.forEach(config => {
      console.log(`   ${config.code}: ${config.name} (${config.accessLevel})`);
    });

    // 检查是否有缓存问题
    if (usPmnConfig.accessLevel === 'free') {
      console.log('\n✅ 数据库配置正确: us_pmn 访问级别为 free');
      console.log('\n🔧 如果 API 仍然显示 premium，可能是缓存问题。建议：');
      console.log('   1. 重启开发服务器: npm run dev');
      console.log('   2. 或者等待 10 分钟让缓存自动过期');
      console.log('   3. 或者清除浏览器缓存');
    } else {
      console.log('\n❌ 数据库配置不正确: us_pmn 访问级别不是 free');
      console.log('   请运行 update-us-pmn-access-level.ts 脚本更新配置');
    }

  } catch (error) {
    console.error('❌ 查询失败:', error);
  } finally {
    await db.$disconnect();
  }
}

// 执行验证
clearConfigCache()
  .then(() => {
    console.log('\n✨ 验证完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 验证失败:', error);
    process.exit(1);
  }); 