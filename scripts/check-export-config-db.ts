#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 直接检查数据库中的导出配置
 */

async function checkExportConfigInDB() {
  console.log('🔍 直接检查数据库中的导出配置...\n');

  try {
    // 1. 检查us_pmn的导出配置
    console.log('📋 us_pmn 导出配置 (数据库直查):');
    
    const usPmnFields = await db.fieldConfig.findMany({
      where: { 
        databaseCode: 'us_pmn',
        isActive: true 
      },
      select: {
        fieldName: true,
        displayName: true,
        isVisible: true,
        isExportable: true,
        exportOrder: true,
        exportDisplayName: true,
      },
      orderBy: { exportOrder: 'asc' }
    });

    console.log(`  总字段数: ${usPmnFields.length}`);
    console.log('  前10个字段 (按exportOrder排序):');
    usPmnFields.slice(0, 10).forEach((field, index) => {
      const visible = field.isVisible ? '显示' : '隐藏';
      const exportable = field.isExportable ? '可导出' : '不导出';
      console.log(`    ${index + 1}. ${field.fieldName} - ${visible}, ${exportable}, 顺序: ${field.exportOrder}`);
      console.log(`       导出名: ${field.exportDisplayName || '未设置'}`);
    });

    // 2. 检查us_class的导出配置
    console.log('\n📋 us_class 导出配置 (数据库直查):');
    
    const usClassFields = await db.fieldConfig.findMany({
      where: { 
        databaseCode: 'us_class',
        isActive: true 
      },
      select: {
        fieldName: true,
        displayName: true,
        isVisible: true,
        isExportable: true,
        exportOrder: true,
        exportDisplayName: true,
      },
      orderBy: { exportOrder: 'asc' }
    });

    console.log(`  总字段数: ${usClassFields.length}`);
    console.log('  所有字段 (按exportOrder排序):');
    usClassFields.forEach((field, index) => {
      const visible = field.isVisible ? '显示' : '隐藏';
      const exportable = field.isExportable ? '可导出' : '不导出';
      console.log(`    ${index + 1}. ${field.fieldName} - ${visible}, ${exportable}, 顺序: ${field.exportOrder}`);
      console.log(`       导出名: ${field.exportDisplayName || '未设置'}`);
    });

    // 3. 统计导出配置
    console.log('\n📊 导出配置统计:');
    
    const usPmnStats = await db.fieldConfig.groupBy({
      by: ['isExportable'],
      where: { databaseCode: 'us_pmn', isActive: true },
      _count: true
    });
    
    console.log('  us_pmn:');
    usPmnStats.forEach(stat => {
      const status = stat.isExportable ? '可导出' : '不导出';
      console.log(`    ${status}: ${stat._count} 个字段`);
    });

    const usClassStats = await db.fieldConfig.groupBy({
      by: ['isExportable'],
      where: { databaseCode: 'us_class', isActive: true },
      _count: true
    });
    
    console.log('  us_class:');
    usClassStats.forEach(stat => {
      const status = stat.isExportable ? '可导出' : '不导出';
      console.log(`    ${status}: ${stat._count} 个字段`);
    });

    // 4. 检查exportOrder不为0的字段
    console.log('\n🔍 检查exportOrder不为0的字段:');
    
    const nonZeroExportOrder = await db.fieldConfig.findMany({
      where: {
        databaseCode: { in: ['us_pmn', 'us_class'] },
        isActive: true,
        exportOrder: { not: 0 }
      },
      select: {
        databaseCode: true,
        fieldName: true,
        exportOrder: true,
        exportDisplayName: true,
      },
      orderBy: [
        { databaseCode: 'asc' },
        { exportOrder: 'asc' }
      ]
    });

    if (nonZeroExportOrder.length > 0) {
      console.log(`  找到 ${nonZeroExportOrder.length} 个设置了exportOrder的字段:`);
      nonZeroExportOrder.forEach(field => {
        console.log(`    ${field.databaseCode}.${field.fieldName} - 顺序: ${field.exportOrder}, 导出名: ${field.exportDisplayName}`);
      });
    } else {
      console.log('  ❌ 没有找到exportOrder不为0的字段！');
      console.log('  💡 这说明配置更新可能没有成功');
    }

    console.log('\n✅ 数据库检查完成！');

  } catch (error) {
    console.error('❌ 检查过程中出错:', error);
  } finally {
    await db.$disconnect();
  }
}

// 运行检查
if (require.main === module) {
  checkExportConfigInDB();
}
