#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 检查数据库配置
 */

async function checkDatabaseConfig() {
  console.log('🔍 检查数据库配置...');

  try {
    // 查询所有数据库配置
    const configs = await db.databaseConfig.findMany({
      where: { isActive: true },
      select: {
        code: true,
        name: true,
        category: true,
        tableName: true,
        modelName: true,
        accessLevel: true,
        isActive: true,
      },
      orderBy: { code: 'asc' },
    });

    console.log(`\n📋 找到 ${configs.length} 个数据库配置:`);
    configs.forEach(config => {
      console.log(`\n   数据库代码: ${config.code}`);
      console.log(`   名称: ${config.name}`);
      console.log(`   分类: ${config.category}`);
      console.log(`   表名: ${config.tableName || 'N/A'}`);
      console.log(`   模型名: ${config.modelName || 'N/A'}`);
      console.log(`   访问级别: ${config.accessLevel}`);
      console.log(`   是否激活: ${config.isActive}`);
      console.log('   ---');
    });

    // 检查 us_class 的具体配置
    const usClassConfig = configs.find(c => c.code === 'us_class');
    if (usClassConfig) {
      console.log(`\n🎯 us_class 配置详情:`);
      console.log(`   表名: ${usClassConfig.tableName}`);
      console.log(`   模型名: ${usClassConfig.modelName}`);
      
      // 检查模型是否存在
      try {
        const model = (db as any)[usClassConfig.modelName || 'USClass'];
        if (model && typeof model.findMany === 'function') {
          console.log(`   ✅ 模型 ${usClassConfig.modelName || 'USClass'} 存在且可用`);
          
          // 尝试查询数据
          const count = await model.count();
          console.log(`   📊 数据记录数: ${count}`);
        } else {
          console.log(`   ❌ 模型 ${usClassConfig.modelName || 'USClass'} 不存在或不可用`);
        }
      } catch (error) {
        console.log(`   ❌ 模型访问失败: ${error}`);
      }
    }

  } catch (error) {
    console.error('❌ 查询失败:', error);
  } finally {
    await db.$disconnect();
  }
}

// 执行检查
checkDatabaseConfig()
  .then(() => {
    console.log('\n✨ 检查完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 检查失败:', error);
    process.exit(1);
  });
