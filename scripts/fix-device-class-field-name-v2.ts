#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 修复 deviceClass 字段名称问题
 * 删除旧的 deviceClass 配置，创建新的 deviceclass 配置
 */

async function fixDeviceClassFieldName() {
  console.log('🔧 修复 deviceClass 字段名称...');

  try {
    // 1. 删除旧的 deviceClass 配置
    const deletedCount = await db.fieldConfig.deleteMany({
      where: {
        databaseCode: 'us_class',
        fieldName: 'deviceClass',
      },
    });

    console.log(`📋 删除了 ${deletedCount.count} 个旧的 deviceClass 配置`);

    // 2. 创建新的 deviceclass 配置
    console.log('📋 创建新的 deviceclass 配置...');
    
    const newConfig = await db.fieldConfig.create({
      data: {
        databaseCode: 'us_class',
        fieldName: 'deviceclass', // 使用小写，匹配数据库字段
        displayName: '器械类别',
        fieldType: 'select',
        isVisible: true,
        isSearchable: false,
        isFilterable: true,
        isSortable: true,
        sortOrder: 5,
        listOrder: 5,
        detailOrder: 5,
        searchType: 'exact',
        filterType: 'multi_select',
        isActive: true,
      },
    });
    
    console.log('✅ 已创建新的 deviceclass 配置');

    // 3. 验证配置
    console.log('\n🔍 验证配置结果...');
    const finalConfig = await db.fieldConfig.findFirst({
      where: {
        databaseCode: 'us_class',
        fieldName: 'deviceclass',
        isActive: true,
      },
      select: {
        fieldName: true,
        displayName: true,
        filterType: true,
        isFilterable: true,
        createdAt: true,
      },
    });

    if (finalConfig) {
      console.log(`\n📋 最终配置:`);
      console.log(`   字段名: ${finalConfig.fieldName}`);
      console.log(`   显示名: ${finalConfig.displayName}`);
      console.log(`   筛选类型: ${finalConfig.filterType}`);
      console.log(`   可筛选: ${finalConfig.isFilterable}`);
      console.log(`   创建时间: ${finalConfig.createdAt.toISOString()}`);
    }

    // 4. 检查数据库中的实际数据
    console.log('\n📊 检查数据库中的 deviceclass 字段数据...');
    const distinctValues = await (db as any).uSClass.findMany({
      select: { deviceclass: true },
      where: { 
        deviceclass: {
          not: null,
          notIn: ['']
        }
      },
      distinct: ['deviceclass'],
      take: 10,
    });

    console.log('   不同的器械类别值:');
    distinctValues.forEach((item: any, index: number) => {
      console.log(`   ${index + 1}. ${item.deviceclass}`);
    });

    // 5. 统计每个类别的数量
    const classCounts = await (db as any).uSClass.groupBy({
      by: ['deviceclass'],
      _count: { deviceclass: true },
      where: { 
        deviceclass: {
          not: null,
          notIn: ['']
        }
      },
      orderBy: { _count: { deviceclass: 'desc' } },
      take: 5,
    });

    console.log('\n📈 器械类别统计（前5名）:');
    classCounts.forEach((item: any, index: number) => {
      console.log(`   ${index + 1}. ${item.deviceclass}: ${item._count.deviceclass} 条记录`);
    });

  } catch (error) {
    console.error('❌ 操作失败:', error);
  } finally {
    await db.$disconnect();
  }
}

// 执行修复
fixDeviceClassFieldName()
  .then(() => {
    console.log('\n✨ 修复完成');
    console.log('\n🔄 请清除缓存以使配置生效');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 修复失败:', error);
    process.exit(1);
  });
