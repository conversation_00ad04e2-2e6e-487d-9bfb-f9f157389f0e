#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

async function testFilterFix() {
  console.log('🧪 测试筛选器联动修复效果...');
  
  try {
    // 1. 验证修复前后的逻辑差异
    console.log('\n📋 1. 修复内容总结...');
    console.log('✅ 修复前: 只有 multi_select 类型字段支持联动更新');
    console.log('✅ 修复后: select, multi_select, checkbox 类型都支持联动更新');
    console.log('');
    console.log('具体修复:');
    console.log('  1. useEffect 中的筛选字段过滤条件');
    console.log('  2. multi_select 组件的 onValueChange 联动逻辑');
    console.log('  3. checkbox 组件的 onValueChange 联动逻辑');
    console.log('  4. select 组件添加 onValueChange 联动逻辑');
    
    // 2. 验证测试场景
    console.log('\n🔍 2. 验证测试场景数据...');
    
    // 场景：选择 country_code = 'PH'，查看 expeditedreview 的分布
    const phData = await db.uSPremarketNotification.findMany({
      where: { country_code: 'PH' },
      select: { expeditedreview: true }
    });
    
    console.log(`PH 总记录数: ${phData.length}`);
    
    // 统计 expeditedreview 分布
    const expeditedStats = phData.reduce((acc: any, item) => {
      const value = item.expeditedreview || 'N/A';
      acc[value] = (acc[value] || 0) + 1;
      return acc;
    }, {});
    
    console.log('PH 数据中的 expeditedreview 分布:');
    Object.entries(expeditedStats).forEach(([key, count]) => {
      console.log(`  ${key}: ${count} 条`);
    });
    
    // 3. 验证字段配置
    console.log('\n🔧 3. 验证字段配置...');
    
    const fieldConfigs = await db.fieldConfig.findMany({
      where: {
        databaseCode: 'us_pmn',
        fieldName: { in: ['country_code', 'expeditedreview'] },
        isActive: true
      },
      select: {
        fieldName: true,
        displayName: true,
        filterType: true,
        isFilterable: true
      }
    });
    
    fieldConfigs.forEach(field => {
      console.log(`${field.fieldName} (${field.displayName}):`);
      console.log(`  筛选类型: ${field.filterType}`);
      console.log(`  可筛选: ${field.isFilterable}`);
      console.log(`  联动支持: ${['select', 'multi_select', 'checkbox'].includes(field.filterType) ? '✅ 是' : '❌ 否'}`);
    });
    
    // 4. 模拟 API 测试
    console.log('\n🌐 4. 模拟 dynamic-counts API 测试...');
    
    // 手动构建查询条件，模拟 API 逻辑
    const filters = { country_code: 'PH' };
    const expeditedCounts = await db.uSPremarketNotification.groupBy({
      by: ['expeditedreview'],
      where: filters,
      _count: { expeditedreview: true },
      orderBy: { _count: { expeditedreview: 'desc' } }
    });
    
    console.log('模拟 API 返回的 expeditedreview 计数:');
    expeditedCounts.forEach(item => {
      const value = item.expeditedreview || 'N/A';
      console.log(`  ${value}: ${item._count.expeditedreview} 条`);
    });
    
    // 5. 对比其他数据库
    console.log('\n📊 5. 对比其他数据库的筛选器类型...');
    
    const usClassFilters = await db.fieldConfig.findMany({
      where: {
        databaseCode: 'us_class',
        isFilterable: true,
        isActive: true
      },
      select: {
        fieldName: true,
        filterType: true
      }
    });
    
    const usClassFilterTypes = usClassFilters.reduce((acc: any, field) => {
      acc[field.filterType] = (acc[field.filterType] || 0) + 1;
      return acc;
    }, {});
    
    console.log('us_class 筛选器类型分布:');
    Object.entries(usClassFilterTypes).forEach(([type, count]) => {
      console.log(`  ${type}: ${count} 个字段`);
    });
    
    const usPmnFilters = await db.fieldConfig.findMany({
      where: {
        databaseCode: 'us_pmn',
        isFilterable: true,
        isActive: true
      },
      select: {
        fieldName: true,
        filterType: true
      }
    });
    
    const usPmnFilterTypes = usPmnFilters.reduce((acc: any, field) => {
      acc[field.filterType] = (acc[field.filterType] || 0) + 1;
      return acc;
    }, {});
    
    console.log('us_pmn 筛选器类型分布:');
    Object.entries(usPmnFilterTypes).forEach(([type, count]) => {
      console.log(`  ${type}: ${count} 个字段`);
    });
    
    // 6. 总结
    console.log('\n🎯 6. 修复效果总结...');
    
    const supportedTypes = ['select', 'multi_select', 'checkbox'];
    const usPmnSupportedCount = usPmnFilters.filter(f => supportedTypes.includes(f.filterType)).length;
    const usPmnTotalCount = usPmnFilters.length;
    
    console.log(`us_pmn 支持联动的筛选器: ${usPmnSupportedCount}/${usPmnTotalCount} 个`);
    console.log('');
    console.log('✅ 修复后的预期效果:');
    console.log('  1. 选择 Country Code = PH 后');
    console.log('  2. Expedited Review 的选项计数会立即更新');
    console.log('  3. 显示基于 PH 数据的真实计数');
    console.log('  4. 所有 select/multi_select/checkbox 类型都支持联动');
    
    console.log('\n🔗 测试步骤:');
    console.log('  1. 访问: http://localhost:3001/data/list/us_pmn');
    console.log('  2. 选择 Country Code = PH');
    console.log('  3. 观察 Expedited Review 的计数是否更新');
    console.log('  4. 验证其他筛选器的联动效果');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await db.$disconnect();
  }
}

testFilterFix().catch(console.error);
