# 跳转到页功能测试指南

## 🎯 功能概述

新增的"跳转到页"功能允许用户快速跳转到指定页面，特别适用于大数据集的快速导航。

## 🔧 功能特性

### 1. 输入验证
- ✅ 只接受数字输入
- ✅ 最小值：1
- ✅ 最大值：min(totalPages, maxPages)
- ✅ 空值检查

### 2. 限制检查
- ✅ 页面限制：最大100页
- ✅ 数据限制：不超过实际总页数
- ✅ 友好的错误提示

### 3. 用户体验
- ✅ 回车键快速跳转
- ✅ 跳转后清空输入框
- ✅ 保持水平滚动位置
- ✅ 加载状态禁用

## 🧪 测试用例

### 测试环境
访问：`http://localhost:3000/data/list/us_pmn`

### 基础功能测试

#### 1. 正常跳转
```
输入: 5
预期: 跳转到第5页
验证: URL更新，数据刷新，页码显示正确
```

#### 2. 边界值测试
```
输入: 1
预期: 跳转到第1页
验证: Previous按钮禁用

输入: 100
预期: 跳转到第100页（如果数据足够）
验证: 显示翻页限制提示
```

#### 3. 超限测试
```
输入: 101
预期: 显示错误提示 "Page limit is 100"
验证: 不跳转，保持当前页

输入: 99999
预期: 显示相应的限制提示
验证: 不跳转，保持当前页
```

#### 4. 无效输入测试
```
输入: 0
预期: 显示错误提示 "Please enter a valid page number (minimum 1)"

输入: -5
预期: 显示错误提示 "Please enter a valid page number (minimum 1)"

输入: abc
预期: 显示错误提示 "Please enter a valid page number (minimum 1)"

输入: 空值
预期: Go按钮禁用
```

### 交互测试

#### 1. 回车键测试
```
操作: 输入页码后按回车
预期: 直接跳转，无需点击Go按钮
```

#### 2. 加载状态测试
```
操作: 跳转过程中再次操作
预期: 输入框和按钮都被禁用
```

#### 3. 滚动位置测试
```
操作: 水平滚动表格后跳转页面
预期: 跳转后保持相同的水平滚动位置
```

## 📱 响应式测试

### 桌面端
- ✅ 输入框宽度：16px (w-16)
- ✅ 按钮间距：space-x-4
- ✅ 文字大小：text-sm

### 移动端
- 需要测试在小屏幕上的显示效果
- 确保输入框不会被挤压

## 🎨 UI/UX 验证

### 视觉设计
```
布局: [Previous] [Page X of Y] [Go to: [输入框] [Go]] [Next]
间距: 合理的space-x-4间距
对齐: 垂直居中对齐
```

### 状态反馈
```
正常状态: 白色背景，蓝色边框聚焦
禁用状态: 灰色背景，禁用交互
错误状态: 弹窗提示，清晰的错误信息
```

## 🔍 错误提示文案

### 英文提示信息
```
无效页码: "Please enter a valid page number (minimum 1)"
超过页面限制: "Page limit is 100. Please enter a page number between 1 and 100."
超过数据限制: "Maximum page is X. Please enter a page number between 1 and X."
```

## 📊 性能测试

### 跳转性能
- 跳转响应时间应与正常翻页一致
- 保持水平滚动位置的恢复速度
- 大页码跳转的数据加载时间

### 内存使用
- 输入状态管理不应造成内存泄漏
- 事件监听器正确清理

## 🎯 用户场景测试

### 场景1：快速定位
```
用户需求: 查看第50页的数据
操作流程: 输入50 → 回车 → 查看数据
预期结果: 快速跳转，比连续点击Next快得多
```

### 场景2：边界探索
```
用户需求: 查看最后几页的数据
操作流程: 输入接近最大页码的数字
预期结果: 正确处理边界情况，给出合适提示
```

### 场景3：错误恢复
```
用户需求: 输入错误后重新输入
操作流程: 输入无效值 → 看到提示 → 输入正确值
预期结果: 错误提示清晰，用户能快速纠正
```

## ✅ 验收标准

### 功能完整性
- [x] 输入验证完整
- [x] 边界检查正确
- [x] 错误提示友好
- [x] 回车键支持
- [x] 滚动位置保持

### 用户体验
- [x] 操作直观简单
- [x] 反馈及时准确
- [x] 视觉设计一致
- [x] 响应式适配

### 性能表现
- [x] 跳转速度快
- [x] 内存使用合理
- [x] 不影响其他功能

## 🚀 使用建议

### 用户指导
1. 在页码显示区域旁边就能看到"Go to:"输入框
2. 输入目标页码，按回车或点击Go按钮
3. 支持1-100页范围内的快速跳转
4. 超出范围会有友好的提示信息

### 最佳实践
- 大数据集浏览时优先使用跳转功能
- 结合搜索功能定位特定数据
- 注意100页的翻页限制提示
