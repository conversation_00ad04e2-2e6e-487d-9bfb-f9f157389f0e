#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 数据库配置助手
 * 帮助添加新数据库并自动计算正确的排序值
 */

// 分类基础排序值配置
const CATEGORY_BASE_SORT_ORDER = {
  'Marketed': 100,
  'Regular': 200,
  '参考数据': 300,
  '全球器械': 400,
  'Regulation': 500,
  '药物研发': 600,
} as const;

/**
 * 获取分类的下一个可用 sortOrder
 */
async function getNextSortOrderForCategory(categoryName: string): Promise<number> {
  // 获取该分类下所有数据库的 sortOrder
  const existingDbs = await db.databaseConfig.findMany({
    where: { 
      category: categoryName,
      isActive: true 
    },
    select: { sortOrder: true },
    orderBy: { sortOrder: 'desc' },
  });

  if (existingDbs.length === 0) {
    // 如果分类下没有数据库，使用基础 sortOrder + 1
    const baseSortOrder = CATEGORY_BASE_SORT_ORDER[categoryName as keyof typeof CATEGORY_BASE_SORT_ORDER];
    return baseSortOrder ? baseSortOrder + 1 : 1001;
  } else {
    // 使用该分类下最大 sortOrder + 1
    return existingDbs[0].sortOrder + 1;
  }
}

/**
 * 显示所有分类和其数据库
 */
async function showAllCategories() {
  console.log('📋 所有分类和数据库:\n');

  const configs = await db.databaseConfig.findMany({
    where: { isActive: true },
    select: {
      code: true,
      name: true,
      category: true,
      sortOrder: true,
    },
    orderBy: { sortOrder: 'asc' },
  });

  // 按分类分组
  const categoriesMap = new Map<string, any[]>();
  configs.forEach(config => {
    if (!categoriesMap.has(config.category)) {
      categoriesMap.set(config.category, []);
    }
    categoriesMap.get(config.category)!.push(config);
  });

  // 按分类的最小 sortOrder 排序
  const sortedCategories = Array.from(categoriesMap.entries())
    .sort(([, dbsA], [, dbsB]) => {
      const minSortA = Math.min(...dbsA.map(db => db.sortOrder));
      const minSortB = Math.min(...dbsB.map(db => db.sortOrder));
      return minSortA - minSortB;
    });

  sortedCategories.forEach(([category, databases], index) => {
    console.log(`${index + 1}. 📂 ${category}`);
    
    // 按 sortOrder 排序数据库
    databases.sort((a, b) => a.sortOrder - b.sortOrder);
    databases.forEach((db, dbIndex) => {
      console.log(`   ${dbIndex + 1}. ${db.code}: ${db.name} (sortOrder: ${db.sortOrder})`);
    });
    console.log('');
  });
}

/**
 * 预览添加新数据库的 sortOrder
 */
async function previewNewDatabase(categoryName: string, dbCode: string, dbName: string) {
  console.log(`🔍 预览添加数据库到分类 "${categoryName}":\n`);

  // 检查数据库代码是否已存在
  const existingDb = await db.databaseConfig.findUnique({
    where: { code: dbCode }
  });

  if (existingDb) {
    console.log(`❌ 数据库代码 "${dbCode}" 已存在！`);
    console.log(`   现有数据库: ${existingDb.name} (分类: ${existingDb.category})`);
    return;
  }

  // 获取建议的 sortOrder
  const suggestedSortOrder = await getNextSortOrderForCategory(categoryName);

  console.log(`📊 建议配置:`);
  console.log(`   数据库代码: ${dbCode}`);
  console.log(`   数据库名称: ${dbName}`);
  console.log(`   分类: ${categoryName}`);
  console.log(`   建议 sortOrder: ${suggestedSortOrder}`);
  console.log('');

  // 显示该分类下现有数据库
  const existingDbs = await db.databaseConfig.findMany({
    where: { 
      category: categoryName,
      isActive: true 
    },
    select: {
      code: true,
      name: true,
      sortOrder: true,
    },
    orderBy: { sortOrder: 'asc' },
  });

  if (existingDbs.length > 0) {
    console.log(`📂 "${categoryName}" 分类下现有数据库:`);
    existingDbs.forEach((db, index) => {
      console.log(`   ${index + 1}. ${db.code}: ${db.name} (sortOrder: ${db.sortOrder})`);
    });
    console.log(`   ${existingDbs.length + 1}. ${dbCode}: ${dbName} (sortOrder: ${suggestedSortOrder}) ← 新增`);
  } else {
    console.log(`📂 "${categoryName}" 分类下暂无数据库，这将是第一个`);
  }
  console.log('');
}

/**
 * 创建新数据库配置的模板代码
 */
async function generateDatabaseConfigCode(categoryName: string, dbCode: string, dbName: string, description?: string) {
  const suggestedSortOrder = await getNextSortOrderForCategory(categoryName);

  console.log(`📝 创建数据库配置的代码模板:\n`);

  console.log(`// 使用 Next.js 代码创建数据库配置`);
  console.log(`const newDatabaseConfig = await db.databaseConfig.create({`);
  console.log(`  data: {`);
  console.log(`    code: '${dbCode}',`);
  console.log(`    name: '${dbName}',`);
  console.log(`    category: '${categoryName}',`);
  console.log(`    description: '${description || '数据库描述'}',`);
  console.log(`    accessLevel: 'free', // 或 'premium', 'enterprise'`);
  console.log(`    sortOrder: ${suggestedSortOrder},`);
  console.log(`    tableName: '${dbCode.toLowerCase()}', // PostgreSQL 表名`);
  console.log(`    modelName: '${dbCode}', // Prisma 模型名`);
  console.log(`    isActive: true,`);
  console.log(`  },`);
  console.log(`});`);
  console.log('');

  console.log(`// 或者使用 upsert 方式（推荐）`);
  console.log(`const databaseConfig = await db.databaseConfig.upsert({`);
  console.log(`  where: { code: '${dbCode}' },`);
  console.log(`  update: {`);
  console.log(`    name: '${dbName}',`);
  console.log(`    category: '${categoryName}',`);
  console.log(`    sortOrder: ${suggestedSortOrder},`);
  console.log(`    isActive: true,`);
  console.log(`  },`);
  console.log(`  create: {`);
  console.log(`    code: '${dbCode}',`);
  console.log(`    name: '${dbName}',`);
  console.log(`    category: '${categoryName}',`);
  console.log(`    description: '${description || '数据库描述'}',`);
  console.log(`    accessLevel: 'free',`);
  console.log(`    sortOrder: ${suggestedSortOrder},`);
  console.log(`    tableName: '${dbCode.toLowerCase()}',`);
  console.log(`    modelName: '${dbCode}',`);
  console.log(`    isActive: true,`);
  console.log(`  },`);
  console.log(`});`);
}

/**
 * 重新排序分类内的数据库
 */
async function reorderDatabasesInCategory(categoryName: string) {
  console.log(`🔄 重新排序 "${categoryName}" 分类下的数据库...\n`);

  const databases = await db.databaseConfig.findMany({
    where: { 
      category: categoryName,
      isActive: true 
    },
    select: {
      id: true,
      code: true,
      name: true,
      sortOrder: true,
    },
    orderBy: { sortOrder: 'asc' },
  });

  if (databases.length === 0) {
    console.log(`⚠️  分类 "${categoryName}" 下没有数据库`);
    return;
  }

  // 获取基础 sortOrder
  const baseSortOrder = CATEGORY_BASE_SORT_ORDER[categoryName as keyof typeof CATEGORY_BASE_SORT_ORDER] || 1000;

  console.log(`📊 重新分配 sortOrder (基础值: ${baseSortOrder}):`);

  for (let i = 0; i < databases.length; i++) {
    const newSortOrder = baseSortOrder + i + 1;
    const database = databases[i];

    if (database.sortOrder !== newSortOrder) {
      await db.databaseConfig.update({
        where: { id: database.id },
        data: { sortOrder: newSortOrder },
      });
      
      console.log(`   ✅ ${database.code}: ${database.sortOrder} → ${newSortOrder}`);
    } else {
      console.log(`   ⏭️  ${database.code}: 保持 ${database.sortOrder}`);
    }
  }

  console.log('\n✅ 重新排序完成！');
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  try {
    switch (command) {
      case 'show':
        await showAllCategories();
        break;
        
      case 'preview':
        if (args.length < 4) {
          console.log('❌ 用法: npm run db-helper preview <分类名> <数据库代码> <数据库名称> [描述]');
          return;
        }
        await previewNewDatabase(args[1], args[2], args[3]);
        break;
        
      case 'generate':
        if (args.length < 4) {
          console.log('❌ 用法: npm run db-helper generate <分类名> <数据库代码> <数据库名称> [描述]');
          return;
        }
        await generateDatabaseConfigCode(args[1], args[2], args[3], args[4]);
        break;
        
      case 'reorder':
        if (args.length < 2) {
          console.log('❌ 用法: npm run db-helper reorder <分类名>');
          return;
        }
        await reorderDatabasesInCategory(args[1]);
        break;
        
      default:
        console.log('📖 数据库配置助手使用说明:');
        console.log('');
        console.log('命令:');
        console.log('  show                                    - 显示所有分类和数据库');
        console.log('  preview <分类> <代码> <名称> [描述]        - 预览添加新数据库');
        console.log('  generate <分类> <代码> <名称> [描述]       - 生成创建数据库的代码');
        console.log('  reorder <分类>                          - 重新排序分类内的数据库');
        console.log('');
        console.log('示例:');
        console.log('  npm run db-helper show');
        console.log('  npm run db-helper preview Marketed eu_marketed "欧盟已上市器械"');
        console.log('  npm run db-helper generate Marketed eu_marketed "欧盟已上市器械" "欧盟CE认证的医疗器械"');
        console.log('  npm run db-helper reorder Marketed');
        console.log('');
        console.log('支持的分类:');
        Object.entries(CATEGORY_BASE_SORT_ORDER).forEach(([category, sortOrder]) => {
          console.log(`  - ${category} (基础 sortOrder: ${sortOrder})`);
        });
        break;
    }
  } catch (error) {
    console.error('❌ 操作失败:', error);
  } finally {
    await db.$disconnect();
  }
}

main();
