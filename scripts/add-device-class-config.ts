#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 为所有相关数据库添加 deviceClass 字段配置
 */

async function addDeviceClassConfig() {
  console.log('🔧 添加 deviceClass 字段配置...');

  try {
    // 获取所有激活的数据库
    const databases = await db.databaseConfig.findMany({
      where: { isActive: true },
      select: { code: true, name: true },
    });

    console.log(`\n📊 找到 ${databases.length} 个激活的数据库:`);
    databases.forEach(db => {
      console.log(`   ${db.code}: ${db.name}`);
    });

    // 为每个数据库添加 deviceClass 配置
    for (const database of databases) {
      const databaseCode = database.code;
      
      console.log(`\n🔧 为数据库 ${databaseCode} 添加 deviceClass 配置...`);
      
      // 检查是否已存在
      const existing = await db.fieldConfig.findFirst({
        where: {
          databaseCode,
          fieldName: 'deviceClass',
        },
      });

      if (existing) {
        console.log(`   ⚠️ 配置已存在，更新为 multi_select...`);
        await db.fieldConfig.update({
          where: { id: existing.id },
          data: {
            filterType: 'multi_select',
            isFilterable: true,
            displayName: '器械类别',
            fieldType: 'select',
            isVisible: true,
            isSortable: true,
            sortOrder: 5,
            listOrder: 5,
            detailOrder: 5,
            searchType: 'exact',
            updatedAt: new Date(),
          },
        });
        console.log(`   ✅ 已更新 ${databaseCode} 的 deviceClass 配置`);
      } else {
        console.log(`   ➕ 创建新的 deviceClass 配置...`);
        await db.fieldConfig.create({
          data: {
            databaseCode,
            fieldName: 'deviceClass',
            displayName: '器械类别',
            fieldType: 'select',
            isVisible: true,
            isSearchable: false,
            isFilterable: true,
            isSortable: true,
            sortOrder: 5,
            listOrder: 5,
            detailOrder: 5,
            searchType: 'exact',
            filterType: 'multi_select', // 设置为多选
            isActive: true,
          },
        });
        console.log(`   ✅ 已创建 ${databaseCode} 的 deviceClass 配置`);
      }
    }

    // 验证配置
    console.log('\n🔍 验证配置结果...');
    const allDeviceClassConfigs = await db.fieldConfig.findMany({
      where: {
        fieldName: 'deviceClass',
        isActive: true,
      },
      select: {
        databaseCode: true,
        fieldName: true,
        displayName: true,
        filterType: true,
        isFilterable: true,
        updatedAt: true,
      },
      orderBy: {
        databaseCode: 'asc',
      },
    });

    console.log(`\n📋 最终配置结果 (${allDeviceClassConfigs.length} 个):`);
    allDeviceClassConfigs.forEach(config => {
      console.log(`   ${config.databaseCode}: ${config.displayName} - ${config.filterType} (可筛选: ${config.isFilterable})`);
    });

  } catch (error) {
    console.error('❌ 操作失败:', error);
  } finally {
    await db.$disconnect();
  }
}

// 执行添加
addDeviceClassConfig()
  .then(() => {
    console.log('\n✨ 配置完成');
    console.log('\n🔄 请重启应用或清除缓存以使配置生效');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 操作失败:', error);
    process.exit(1);
  });
