#!/usr/bin/env tsx

import { ConfigManager } from './config-manager';
import { CONFIG_TEMPLATES } from '../src/lib/config-templates';

/**
 * 快速设置数据库配置脚本
 * 提供交互式命令行界面来创建数据库配置
 */

interface QuickSetupOptions {
  code: string;
  name: string;
  category: string;
  description?: string;
  tableName?: string;
  modelName?: string;
  accessLevel: 'free' | 'premium' | 'enterprise';
  template?: keyof typeof CONFIG_TEMPLATES;
  customFields?: Array<{
    fieldName: string;
    displayName: string;
    filterType: 'select' | 'multi_select' | 'input' | 'date_range' | 'checkbox';
  }>;
}

/**
 * 快速设置函数
 */
async function quickSetup(options: QuickSetupOptions): Promise<void> {
  console.log(`🚀 快速设置数据库配置: ${options.code}`);
  console.log(`📋 配置信息:`);
  console.log(`   代码: ${options.code}`);
  console.log(`   名称: ${options.name}`);
  console.log(`   分类: ${options.category}`);
  console.log(`   访问级别: ${options.accessLevel}`);
  console.log(`   模板: ${options.template || '无'}`);

  try {
    if (options.template) {
      // 使用模板创建
      await ConfigManager.createFromTemplate(options.template, {
        code: options.code,
        name: options.name,
        category: options.category,
        description: options.description,
        tableName: options.tableName,
        modelName: options.modelName,
        accessLevel: options.accessLevel,
      });
    } else {
      // 自定义字段创建
      const fields = options.customFields?.map((field, index) => {
        // 根据 filterType 推断合适的 fieldType
        let fieldType: 'text' | 'date' | 'number' | 'boolean' | 'select' | 'json' = 'text';
        if (field.filterType === 'date_range') {
          fieldType = 'date';
        } else if (field.filterType === 'select' || field.filterType === 'multi_select') {
          fieldType = 'select';
        }

        return {
          fieldName: field.fieldName,
          displayName: field.displayName,
          fieldType,
          isVisible: true,
          isSearchable: field.filterType === 'input',
          isFilterable: true,
          isSortable: true,
          sortOrder: index + 1,
          listOrder: index + 1,
          detailOrder: index + 1,
          searchType: field.filterType === 'input' ? 'contains' as const : 'exact' as const,
          filterType: field.filterType,
          isActive: true,
        };
      }) || [];

      await ConfigManager.createDatabaseConfig({
        code: options.code,
        name: options.name,
        category: options.category,
        description: options.description,
        tableName: options.tableName,
        modelName: options.modelName,
        accessLevel: options.accessLevel,
        fields,
      });
    }

    console.log(`✅ 数据库配置创建完成!`);
    
    // 验证配置
    await ConfigManager.validateConfig(options.code);

  } catch (error) {
    console.error(`❌ 快速设置失败:`, error);
    throw error;
  }
}

/**
 * 预定义的快速设置方案
 */
const QUICK_SETUPS = {
  
  /**
   * 示例：创建新的医疗器械数据库
   */
  async createMedicalDeviceDB(code: string, name: string, accessLevel: 'free' | 'premium' | 'enterprise' = 'premium') {
    await quickSetup({
      code,
      name,
      category: '医疗器械',
      description: `${name} - 医疗器械注册信息数据库`,
      accessLevel,
      template: 'medicalDevice',
    });
  },

  /**
   * 示例：创建药品数据库
   */
  async createDrugDB(code: string, name: string, accessLevel: 'free' | 'premium' | 'enterprise' = 'premium') {
    await quickSetup({
      code,
      name,
      category: '药品',
      description: `${name} - 药品注册信息数据库`,
      accessLevel,
      template: 'drug',
    });
  },

  /**
   * 示例：创建专利数据库
   */
  async createPatentDB(code: string, name: string, accessLevel: 'free' | 'premium' | 'enterprise' = 'free') {
    await quickSetup({
      code,
      name,
      category: '专利',
      description: `${name} - 专利信息数据库`,
      accessLevel,
      template: 'patent',
    });
  },

  /**
   * 示例：创建自定义数据库
   */
  async createCustomDB(options: QuickSetupOptions) {
    await quickSetup(options);
  },

  /**
   * 批量更新现有数据库的筛选类型
   */
  async enableMultiSelectForDatabase(databaseCode: string, fieldNames: string[]) {
    console.log(`🔄 为 ${databaseCode} 启用多选筛选功能`);
    
    const updates = fieldNames.map(fieldName => ({
      fieldName,
      filterType: 'multi_select',
    }));

    await ConfigManager.updateFilterTypes(databaseCode, updates);
    console.log(`✅ 已为 ${fieldNames.join(', ')} 启用多选筛选`);
  },

  /**
   * 为现有数据库添加常用字段
   */
  async addCommonFields(databaseCode: string) {
    console.log(`➕ 为 ${databaseCode} 添加常用字段`);
    
    // 这里可以添加常用字段的逻辑
    // 例如：状态、分类、日期等字段
    
    const commonFields = [
      {
        fieldName: 'status',
        displayName: '状态',
        filterType: 'select' as const,
      },
      {
        fieldName: 'category',
        displayName: '分类',
        filterType: 'multi_select' as const,
      },
      {
        fieldName: 'tags',
        displayName: '标签',
        filterType: 'multi_select' as const,
      },
    ];

    // 这里需要实现添加字段的逻辑
    console.log(`📋 计划添加字段: ${commonFields.map(f => f.displayName).join(', ')}`);
    console.log(`⚠️  请手动实现字段添加逻辑`);
  },
};

/**
 * 示例使用方法
 */
async function examples() {
  console.log(`
📚 快速设置示例:

// 1. 创建医疗器械数据库
await QUICK_SETUPS.createMedicalDeviceDB('device_eu', '欧盟医疗器械数据库', 'premium');

// 2. 创建药品数据库  
await QUICK_SETUPS.createDrugDB('drug_cn', '中国药品数据库', 'free');

// 3. 创建专利数据库
await QUICK_SETUPS.createPatentDB('patent_global', '全球专利数据库', 'enterprise');

// 4. 为现有数据库启用多选
await QUICK_SETUPS.enableMultiSelectForDatabase('us_class', ['deviceclass', 'category']);

// 5. 创建自定义数据库
await QUICK_SETUPS.createCustomDB({
  code: 'custom_db',
  name: '自定义数据库',
  category: '其他',
  accessLevel: 'free',
  customFields: [
    { fieldName: 'title', displayName: '标题', filterType: 'input' },
    { fieldName: 'type', displayName: '类型', filterType: 'multi_select' },
    { fieldName: 'date', displayName: '日期', filterType: 'date_range' },
  ],
});
`);
}

// 导出工具
export { quickSetup, QUICK_SETUPS };

// 如果直接运行此脚本，显示示例
if (require.main === module) {
  examples().then(() => {
    console.log(`
🛠️  快速设置工具已准备就绪!

使用方法:
  import { QUICK_SETUPS } from './scripts/quick-setup-database';
  
或者直接在此文件中添加你的设置代码并运行:
  npx tsx scripts/quick-setup-database.ts
`);
  });
}

// 实际执行示例（取消注释来运行）
async function runExample() {
  try {
    // 示例：为现有数据库启用多选功能
    // await QUICK_SETUPS.enableMultiSelectForDatabase('us_class', ['deviceclass']);
    
    // 示例：创建新数据库
    // await QUICK_SETUPS.createMedicalDeviceDB('device_test', '测试医疗器械数据库', 'free');
    
    console.log('✅ 示例执行完成');
  } catch (error) {
    console.error('❌ 示例执行失败:', error);
  }
}

// 取消注释来运行示例
// runExample();
