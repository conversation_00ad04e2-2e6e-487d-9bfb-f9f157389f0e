#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';
import { ConfigCacheService } from '../src/lib/configCache';

/**
 * 修复 us_pmn 筛选器配置问题
 * 确保 devicename 字段显示为输入框而不是选择框
 */

async function fixUsPmnFilters() {
  console.log('🔧 修复 us_pmn 筛选器配置...\n');

  try {
    // 1. 检查当前配置
    console.log('📊 1. 检查当前 devicename 配置:');
    const currentConfig = await db.fieldConfig.findFirst({
      where: {
        databaseCode: 'us_pmn',
        fieldName: 'devicename',
        isActive: true
      }
    });

    if (!currentConfig) {
      console.log('❌ 未找到 devicename 配置，需要创建');
      
      // 创建配置
      await db.fieldConfig.create({
        data: {
          databaseCode: 'us_pmn',
          fieldName: 'devicename',
          displayName: 'Device Name',
          fieldType: 'text',
          isVisible: true,
          isSearchable: true,
          isFilterable: true,
          isSortable: true,
          sortOrder: 2,
          listOrder: 2,
          detailOrder: 23,
          searchType: 'contains',
          filterType: 'input',
          isActive: true
        }
      });
      
      console.log('✅ 已创建 devicename 配置');
    } else {
      console.log('📋 当前配置:');
      console.log(`   - filterType: ${currentConfig.filterType}`);
      console.log(`   - isFilterable: ${currentConfig.isFilterable}`);
      
      // 确保配置正确
      if (currentConfig.filterType !== 'input' || !currentConfig.isFilterable) {
        console.log('🔧 更新配置...');
        
        await db.fieldConfig.update({
          where: { id: currentConfig.id },
          data: {
            filterType: 'input',
            isFilterable: true,
            updatedAt: new Date()
          }
        });
        
        console.log('✅ 配置已更新');
      } else {
        console.log('✅ 配置已正确');
      }
    }

    // 2. 清除缓存
    console.log('\n🧹 2. 清除配置缓存...');
    await ConfigCacheService.clearDatabaseCache('us_pmn');
    await ConfigCacheService.clearAllCache();
    console.log('✅ 缓存已清除');

    // 3. 验证配置
    console.log('\n🔍 3. 验证配置...');
    
    // 检查数据库
    const dbConfig = await db.fieldConfig.findFirst({
      where: {
        databaseCode: 'us_pmn',
        fieldName: 'devicename',
        isActive: true
      },
      select: {
        fieldName: true,
        displayName: true,
        filterType: true,
        isFilterable: true
      }
    });
    
    console.log('📊 数据库配置:');
    console.table([dbConfig]);

    // 检查API
    try {
      const response = await fetch('http://localhost:3000/api/meta/us_pmn');
      const result = await response.json();
      
      if (result.success) {
        const apiField = result.config.fields.find((f: any) => f.fieldName === 'devicename');
        console.log('\n🌐 API 配置:');
        console.table([{
          fieldName: apiField?.fieldName,
          displayName: apiField?.displayName,
          filterType: apiField?.filterType,
          isFilterable: apiField?.isFilterable
        }]);
        
        // 验证结果
        if (apiField?.filterType === 'input' && apiField?.isFilterable) {
          console.log('\n✅ 修复成功！');
          console.log('🎯 现在请在浏览器中:');
          console.log('   1. 访问 http://localhost:3000/data/list/us_pmn');
          console.log('   2. 强制刷新页面 (Ctrl+F5 或 Cmd+Shift+R)');
          console.log('   3. 检查筛选器面板中的 "Device Name" 是否显示为输入框');
          console.log('   4. 如果仍有问题，请清除浏览器缓存');
        } else {
          console.log('\n❌ 修复失败，API 返回的配置仍不正确');
        }
      } else {
        console.error('❌ API 测试失败:', result.error);
      }
    } catch (error) {
      console.error('❌ API 测试失败:', error);
    }

    // 4. 显示所有筛选字段
    console.log('\n📋 4. 所有可筛选字段:');
    const allFilterable = await db.fieldConfig.findMany({
      where: {
        databaseCode: 'us_pmn',
        isFilterable: true,
        isActive: true
      },
      select: {
        fieldName: true,
        displayName: true,
        filterType: true,
        sortOrder: true
      },
      orderBy: { sortOrder: 'asc' }
    });
    
    console.table(allFilterable);

    // 5. 生成测试命令
    console.log('\n🧪 5. 测试命令:');
    console.log('# 验证配置');
    console.log('npx tsx scripts/test-us-pmn-config.ts');
    console.log('');
    console.log('# 测试前端');
    console.log('npx tsx scripts/test-frontend-config.ts');
    console.log('');
    console.log('# 清除缓存');
    console.log('npx tsx scripts/clear-redis-cache.ts');

  } catch (error) {
    console.error('❌ 修复失败:', error);
    throw error;
  }
}

// 执行修复
fixUsPmnFilters()
  .then(() => {
    console.log('\n✨ 修复完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 修复失败:', error);
    process.exit(1);
  });
