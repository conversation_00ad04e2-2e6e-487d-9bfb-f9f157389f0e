#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 更新 us_pmn 数据库的日期字段配置
 * 修复高级搜索中日期字段的操作符问题
 */

async function updateUsPmnDateFields() {
  console.log('🔧 更新 us_pmn 数据库的日期字段配置...');

  try {
    // 更新 datereceived 字段配置
    const dateReceivedResult = await db.fieldConfig.updateMany({
      where: {
        databaseCode: 'us_pmn',
        fieldName: 'datereceived',
        isActive: true
      },
      data: {
        isSearchable: true,
        isAdvancedSearchable: true,
        searchType: 'date_range',
        filterType: 'date_range'
      }
    });

    console.log(`✅ 更新了 datereceived 字段配置: ${dateReceivedResult.count} 条记录`);

    // 更新 decisiondate 字段配置
    const decisionDateResult = await db.fieldConfig.updateMany({
      where: {
        databaseCode: 'us_pmn',
        fieldName: 'decisiondate',
        isActive: true
      },
      data: {
        isSearchable: true,
        isAdvancedSearchable: true,
        searchType: 'date_range',
        filterType: 'date_range'
      }
    });

    console.log(`✅ 更新了 decisiondate 字段配置: ${decisionDateResult.count} 条记录`);

    // 验证更新结果
    const updatedFields = await db.fieldConfig.findMany({
      where: {
        databaseCode: 'us_pmn',
        fieldName: {
          in: ['datereceived', 'decisiondate']
        },
        isActive: true
      },
      select: {
        fieldName: true,
        displayName: true,
        isSearchable: true,
        isAdvancedSearchable: true,
        searchType: true,
        filterType: true
      }
    });

    console.log('\n📋 更新后的字段配置:');
    updatedFields.forEach(field => {
      console.log(`  - ${field.displayName} (${field.fieldName}): searchable=${field.isSearchable}, advancedSearchable=${field.isAdvancedSearchable}, searchType=${field.searchType}`);
    });

    console.log('\n🎉 us_pmn 日期字段配置更新完成！');
    
  } catch (error) {
    console.error('❌ 更新字段配置时出错:', error);
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}

// 运行更新
updateUsPmnDateFields().catch(console.error);
