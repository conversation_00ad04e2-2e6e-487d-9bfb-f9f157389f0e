import { PrismaClient } from '@prisma/client';
import { readFileSync } from 'fs';
import { join } from 'path';

const db = new PrismaClient();

async function runPaginationConfigUpdate() {
  console.log('🔧 开始为 DatabaseConfig 表新增翻页配置字段...\n');

  try {
    // 读取SQL文件
    const sqlPath = join(__dirname, 'add-pagination-config.sql');
    const sqlContent = readFileSync(sqlPath, 'utf-8');
    
    // 分割SQL语句（按分号分割，过滤空语句）
    const sqlStatements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt && !stmt.startsWith('--'));

    console.log(`📝 准备执行 ${sqlStatements.length} 条SQL语句...\n`);

    // 逐条执行SQL语句
    for (let i = 0; i < sqlStatements.length; i++) {
      const statement = sqlStatements[i];
      if (statement) {
        console.log(`⚡ 执行第 ${i + 1} 条语句...`);
        try {
          await db.$executeRawUnsafe(statement);
          console.log(`✅ 第 ${i + 1} 条语句执行成功`);
        } catch (error) {
          console.log(`⚠️  第 ${i + 1} 条语句执行失败 (可能字段已存在):`, (error as Error).message);
        }
      }
    }

    console.log('\n🎯 验证配置结果...');
    
    // 验证结果
    // const configs = await db.databaseConfig.findMany({
      // where: { isActive: true },
      // select: {
        // code: true,
        // name: true,
        // defaultPageSize: true,
        // maxPageSize: true,
        // maxPages: true,
        // accessLevel: true,
      // },
      // orderBy: { code: 'asc' },
    // });

    console.log('\n📊 当前数据库翻页配置:');
    console.log('┌─────────────┬──────────────────────┬─────────┬─────────┬─────────┬─────────────┐');
    console.log('│ 数据库代码  │ 数据库名称           │ 默认条数│ 最大条数│ 最大页数│ 访问级别    │');
    console.log('├─────────────┼──────────────────────┼─────────┼─────────┼─────────┼─────────────┤');

    // configs.forEach(config => {
      // const code = config.code.padEnd(11);
      // const name = config.name.padEnd(20);
      // const defaultSize = String(config.defaultPageSize || 'N/A').padEnd(7);
      // const maxSize = String(config.maxPageSize || 'N/A').padEnd(7);
      // const maxPages = String(config.maxPages || 'N/A').padEnd(7);
      // const accessLevel = config.accessLevel.padEnd(11);

      // console.log(`│ ${code} │ ${name} │ ${defaultSize} │ ${maxSize} │ ${maxPages} │ ${accessLevel} │`);
    // });

    console.log('└─────────────┴──────────────────────┴─────────┴─────────┴─────────┴─────────────┘');

    console.log('\n✨ 翻页配置字段新增完成！');
    console.log('\n📋 配置说明:');
    console.log('  • us_class: 默认20条/页，最大50条/页，最多200页');
    console.log('  • us_pmn: 默认20条/页，最大100条/页，最多300页');
    console.log('  • 当用户翻页达到限制时，会显示英文提示信息');

  } catch (error) {
    console.error('❌ 执行失败:', error);
    throw error;
  } finally {
    await db.$disconnect();
  }
}

// 运行脚本
runPaginationConfigUpdate().catch(console.error);
