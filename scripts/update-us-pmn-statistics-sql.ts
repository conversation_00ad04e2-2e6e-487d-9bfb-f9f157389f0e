#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

async function updateUsPmnStatisticsSQL() {
  console.log('🚀 使用 SQL 安全更新 us_pmn 统计功能配置...');
  
  try {
    // 1. 查看当前状态
    console.log('\n📊 1. 查看当前 us_pmn 统计配置状态...');
    const currentStats = await db.$queryRaw`
      SELECT 
        "fieldName", 
        "displayName",
        "isStatisticsEnabled",
        "statisticsDisplayName",
        "statisticsOrder"
      FROM "FieldConfig" 
      WHERE "databaseCode" = 'us_pmn' 
        AND "isActive" = true
        AND "isStatisticsEnabled" = true
    ` as any[];
    
    console.log(`当前已启用统计的字段数: ${currentStats.length}`);
    if (currentStats.length > 0) {
      currentStats.forEach(field => {
        console.log(`  - ${field.fieldName}: ${field.statisticsDisplayName || field.displayName}`);
      });
    }
    
    // 2. 使用 SQL 更新统计配置
    console.log('\n🔧 2. 使用 SQL 更新 us_pmn 统计配置...');
    
    // 更新 decision 字段
    console.log('  更新 decision 字段...');
    await db.$executeRaw`
      UPDATE "FieldConfig" 
      SET 
        "isStatisticsEnabled" = true,
        "statisticsOrder" = 1,
        "statisticsDisplayName" = '审批决定分布',
        "statisticsDefaultLimit" = 5,
        "statisticsMaxLimit" = 15,
        "statisticsSortOrder" = 'desc',
        "updatedAt" = NOW()
      WHERE "databaseCode" = 'us_pmn' 
        AND "fieldName" = 'decision' 
        AND "isActive" = true
    `;
    console.log('    ✅ decision 字段已更新');
    
    // 更新 productcode 字段
    console.log('  更新 productcode 字段...');
    await db.$executeRaw`
      UPDATE "FieldConfig" 
      SET 
        "isStatisticsEnabled" = true,
        "statisticsOrder" = 2,
        "statisticsDisplayName" = '产品代码统计',
        "statisticsDefaultLimit" = 8,
        "statisticsMaxLimit" = 100,
        "statisticsSortOrder" = 'desc',
        "updatedAt" = NOW()
      WHERE "databaseCode" = 'us_pmn' 
        AND "fieldName" = 'productcode' 
        AND "isActive" = true
    `;
    console.log('    ✅ productcode 字段已更新');
    
    // 更新 applicant 字段
    console.log('  更新 applicant 字段...');
    await db.$executeRaw`
      UPDATE "FieldConfig" 
      SET 
        "isStatisticsEnabled" = true,
        "statisticsOrder" = 3,
        "statisticsDisplayName" = '申请人统计',
        "statisticsDefaultLimit" = 10,
        "statisticsMaxLimit" = 50,
        "statisticsSortOrder" = 'desc',
        "updatedAt" = NOW()
      WHERE "databaseCode" = 'us_pmn' 
        AND "fieldName" = 'applicant' 
        AND "isActive" = true
    `;
    console.log('    ✅ applicant 字段已更新');
    
    // 更新 country_code 字段
    console.log('  更新 country_code 字段...');
    await db.$executeRaw`
      UPDATE "FieldConfig" 
      SET 
        "isStatisticsEnabled" = true,
        "statisticsOrder" = 4,
        "statisticsDisplayName" = '国家分布',
        "statisticsDefaultLimit" = 10,
        "statisticsMaxLimit" = 30,
        "statisticsSortOrder" = 'desc',
        "updatedAt" = NOW()
      WHERE "databaseCode" = 'us_pmn' 
        AND "fieldName" = 'country_code' 
        AND "isActive" = true
    `;
    console.log('    ✅ country_code 字段已更新');
    
    // 更新 type 字段
    console.log('  更新 type 字段...');
    await db.$executeRaw`
      UPDATE "FieldConfig" 
      SET 
        "isStatisticsEnabled" = true,
        "statisticsOrder" = 5,
        "statisticsDisplayName" = '类型分布',
        "statisticsDefaultLimit" = 5,
        "statisticsMaxLimit" = 20,
        "statisticsSortOrder" = 'desc',
        "updatedAt" = NOW()
      WHERE "databaseCode" = 'us_pmn' 
        AND "fieldName" = 'type' 
        AND "isActive" = true
    `;
    console.log('    ✅ type 字段已更新');
    
    // 3. 验证更新结果
    console.log('\n🔍 3. 验证更新结果...');
    const updatedStats = await db.$queryRaw`
      SELECT 
        "fieldName", 
        "statisticsDisplayName",
        "statisticsOrder",
        "statisticsDefaultLimit",
        "statisticsMaxLimit"
      FROM "FieldConfig" 
      WHERE "databaseCode" = 'us_pmn' 
        AND "isActive" = true
        AND "isStatisticsEnabled" = true
      ORDER BY "statisticsOrder" ASC
    ` as any[];
    
    console.log(`\n✅ us_pmn 现在有 ${updatedStats.length} 个统计字段:`);
    updatedStats.forEach((field, index) => {
      console.log(`  ${index + 1}. ${field.statisticsDisplayName} (${field.fieldName})`);
      console.log(`     默认显示: ${field.statisticsDefaultLimit}, 最大: ${field.statisticsMaxLimit}`);
    });
    
    // 4. 对比 us_class 的统计配置
    console.log('\n📊 4. 对比 us_class 统计配置...');
    const usClassStats = await db.$queryRaw`
      SELECT COUNT(*) as count
      FROM "FieldConfig" 
      WHERE "databaseCode" = 'us_class' 
        AND "isActive" = true
        AND "isStatisticsEnabled" = true
    ` as any[];
    
    const usClassCount = usClassStats[0]?.count || 0;
    
    console.log('  统计字段数量对比:');
    console.log(`    us_pmn: ${updatedStats.length} 个统计字段`);
    console.log(`    us_class: ${usClassCount} 个统计字段`);
    
    if (updatedStats.length >= usClassCount) {
      console.log('  ✅ us_pmn 统计功能已达到或超过 us_class 水平！');
    }
    
    // 5. 测试统计 API
    console.log('\n🌐 5. 测试统计 API...');
    try {
      const response = await fetch('http://localhost:3001/api/stats/us_pmn/configurable');
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          console.log('✅ 可配置统计 API 测试成功');
          console.log(`   返回统计项数量: ${result.data?.length || 0}`);
        } else {
          console.log('❌ 统计 API 返回错误:', result.error);
        }
      } else {
        console.log('❌ 统计 API 请求失败:', response.status);
      }
    } catch (error) {
      console.log('⚠️  统计 API 测试跳过 (服务器未运行)');
    }
    
    console.log('\n🎉 SQL 更新完成！');
    console.log('\n💡 us_pmn 现在具备完整的统计功能:');
    console.log('  ✅ 审批决定分布图表');
    console.log('  ✅ 产品代码统计');
    console.log('  ✅ 申请人统计');
    console.log('  ✅ 国家分布图表');
    console.log('  ✅ 类型分布统计');
    
    console.log('\n🔗 查看效果:');
    console.log('  访问: http://localhost:3001/data/list/us_pmn');
    console.log('  在页面右侧查看统计图表');
    
    console.log('\n📋 总结:');
    console.log('  - 只更新了现有字段的统计配置');
    console.log('  - 没有删除任何数据');
    console.log('  - 没有新增任何字段');
    console.log('  - us_pmn 现在与 us_class 功能对等');
    
  } catch (error) {
    console.error('❌ SQL 更新失败:', error);
  } finally {
    await db.$disconnect();
  }
}

updateUsPmnStatisticsSQL().catch(console.error);
