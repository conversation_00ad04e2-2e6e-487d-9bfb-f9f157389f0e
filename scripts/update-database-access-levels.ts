#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// 数据库访问级别配置
const DATABASE_ACCESS_CONFIGS = {
  // 免费数据库
  freePat: { accessLevel: 'free' },
  deviceCNEvaluation: { accessLevel: 'free' },
  deviceCNImported: { accessLevel: 'free' },

  // 高级会员数据库
  deviceHK: { accessLevel: 'premium' },
  deviceUS: { accessLevel: 'premium' },
  deviceJP: { accessLevel: 'premium' },
  deviceUK: { accessLevel: 'premium' },
  deviceSG: { accessLevel: 'premium' },
  subjectNewdrug: { accessLevel: 'premium' },

  // 企业版数据库
  subjectLicenseout: { accessLevel: 'enterprise' },
  subjectVbp: { accessLevel: 'enterprise' },
} as const;

async function updateDatabaseAccessLevels() {
  console.log('🔄 开始更新数据库访问级别配置...');

  for (const [code, config] of Object.entries(DATABASE_ACCESS_CONFIGS)) {
    try {
      const result = await prisma.databaseConfig.upsert({
        where: { code },
        update: {
          accessLevel: config.accessLevel,
        },
        create: {
          code,
          name: getDatabaseName(code),
          category: getDatabaseCategory(code),
          description: getDatabaseDescription(code),
          accessLevel: config.accessLevel,
          isActive: true,
        },
      });

      console.log(`✅ [${code}] 访问级别已更新为: ${config.accessLevel}`);
    } catch (error) {
      console.error(`❌ [${code}] 更新失败:`, error);
    }
  }

  console.log('✨ 数据库访问级别配置更新完成！');
}

// 辅助函数：获取数据库名称
function getDatabaseName(code: string): string {
  const names: Record<string, string> = {
    freePat: '医药专利',
    deviceCNEvaluation: '中国大陆审评',
    deviceCNImported: '中国大陆上市',
    deviceHK: '中国香港上市',
    deviceUS: '美国上市',
    deviceJP: '日本上市',
    deviceUK: '英国上市',
    deviceSG: '新加坡上市',
    subjectNewdrug: '全球获批新药',
    subjectLicenseout: 'License out',
    subjectVbp: '国家集采结果',
  };
  return names[code] || code;
}

// 辅助函数：获取数据库分类
function getDatabaseCategory(code: string): string {
  if (code.startsWith('device')) {
    if (code.includes('CN')) return '中国器械';
    return '全球器械';
  }
  if (code.startsWith('subject')) return '专题数据';
  if (code === 'freePat') return '药物研发';
  return '其他';
}

// 辅助函数：获取数据库描述
function getDatabaseDescription(code: string): string {
  const descriptions: Record<string, string> = {
    freePat: '医药专利信息及原文下载',
    deviceCNEvaluation: '医疗器械审评进度跟踪、审评结论查询',
    deviceCNImported: '中国大陆已上市的医疗器械信息',
    deviceHK: '中国香港已上市的医疗器械信息',
    deviceUS: '美国FDA批准的医疗器械信息',
    deviceJP: '日本PMDA批准的医疗器械信息',
    deviceUK: '英国MHRA批准的医疗器械信息',
    deviceSG: '新加坡HSA批准的医疗器械信息',
    subjectNewdrug: '全球获批新药数据库',
    subjectLicenseout: 'License out交易数据',
    subjectVbp: '国家药品集中采购结果数据',
  };
  return descriptions[code] || '';
}

// 执行更新
updateDatabaseAccessLevels()
  .then(() => {
    console.log('🎉 所有配置更新完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 更新过程中发生错误:', error);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
