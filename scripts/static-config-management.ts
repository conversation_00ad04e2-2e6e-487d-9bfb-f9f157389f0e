#!/usr/bin/env tsx

import { StaticTableMappingService } from '../src/lib/staticTableMappingService';
import { AppInitializer } from '../src/lib/appInitializer';

const COMMANDS = {
  status: '查看配置状态',
  refresh: '刷新配置',
  list: '列出所有配置',
  test: '测试配置性能',
  init: '初始化配置'
} as const;

async function showStatus() {
  console.log('📊 静态配置状态:');
  
  const appStatus = AppInitializer.getStatus();
  const configStatus = StaticTableMappingService.getInitializationStatus();
  
  console.log(`   应用初始化状态: ${appStatus.initialized ? '✅ 已初始化' : '❌ 未初始化'}`);
  console.log(`   配置初始化状态: ${configStatus.initialized ? '✅ 已初始化' : '❌ 未初始化'}`);
  console.log(`   配置数量: ${configStatus.configCount} 个`);
  
  if (configStatus.configCodes.length > 0) {
    console.log('   已加载的数据库:');
    configStatus.configCodes.forEach(code => {
      const mapping = StaticTableMappingService.getTableMapping(code);
      if (mapping) {
        console.log(`     - ${code}: ${mapping.displayName}`);
        console.log(`       表名: ${mapping.tableName || 'N/A'}`);
        console.log(`       模型: ${mapping.modelName || 'N/A'}`);
      }
    });
  }
}

async function refreshConfig() {
  console.log('🔄 刷新配置...');
  
  try {
    await AppInitializer.reinitialize();
    console.log('✅ 配置刷新成功');
    
    const status = StaticTableMappingService.getInitializationStatus();
    console.log(`   重新加载了 ${status.configCount} 个配置`);
    
  } catch (error) {
    console.error('❌ 配置刷新失败:', error);
    process.exit(1);
  }
}

async function listConfigs() {
  console.log('📋 配置列表:');
  
  // 确保已初始化
  if (!StaticTableMappingService.getInitializationStatus().initialized) {
    await StaticTableMappingService.initialize();
  }
  
  const allMappings = StaticTableMappingService.getAllMappings();
  const codes = Object.keys(allMappings);
  
  if (codes.length === 0) {
    console.log('   没有找到配置');
    return;
  }
  
  console.log(`   共 ${codes.length} 个配置:\n`);
  
  codes.forEach(code => {
    const mapping = allMappings[code];
    console.log(`   📌 ${code}`);
    console.log(`      显示名: ${mapping.displayName}`);
    console.log(`      分类: ${mapping.category || 'N/A'}`);
    console.log(`      表名: ${mapping.tableName || 'N/A'}`);
    console.log(`      模型: ${mapping.modelName || 'N/A'}`);
    console.log(`      描述: ${mapping.description || 'N/A'}\n`);
  });
}

async function testPerformance() {
  console.log('⚡ 性能测试...');
  
  // 确保已初始化
  if (!StaticTableMappingService.getInitializationStatus().initialized) {
    await StaticTableMappingService.initialize();
  }
  
  const testRounds = 1000;
  const testCode = 'deviceCNImported';
  
  // 测试配置读取性能
  console.log(`   测试配置读取性能 (${testRounds} 次)...`);
  
  const times: number[] = [];
  for (let i = 0; i < testRounds; i++) {
    const start = performance.now();
    StaticTableMappingService.getTableMapping(testCode);
    const end = performance.now();
    times.push(end - start);
  }
  
  const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
  const minTime = Math.min(...times);
  const maxTime = Math.max(...times);
  
  console.log(`   平均响应时间: ${avgTime.toFixed(6)}ms`);
  console.log(`   最快响应时间: ${minTime.toFixed(6)}ms`);
  console.log(`   最慢响应时间: ${maxTime.toFixed(6)}ms`);
  
  // 测试模型获取性能
  console.log(`\n   测试模型获取性能 (${testRounds} 次)...`);
  
  const modelTimes: number[] = [];
  for (let i = 0; i < testRounds; i++) {
    const start = performance.now();
    StaticTableMappingService.getDynamicModel(testCode);
    const end = performance.now();
    modelTimes.push(end - start);
  }
  
  const avgModelTime = modelTimes.reduce((a, b) => a + b, 0) / modelTimes.length;
  const minModelTime = Math.min(...modelTimes);
  const maxModelTime = Math.max(...modelTimes);
  
  console.log(`   平均响应时间: ${avgModelTime.toFixed(6)}ms`);
  console.log(`   最快响应时间: ${minModelTime.toFixed(6)}ms`);
  console.log(`   最慢响应时间: ${maxModelTime.toFixed(6)}ms`);
  
  // 性能评估
  console.log('\n   📈 性能评估:');
  if (avgTime < 0.001) {
    console.log('   ✅ 配置读取性能优秀 (< 0.001ms)');
  } else if (avgTime < 0.01) {
    console.log('   ✅ 配置读取性能良好 (< 0.01ms)');
  } else {
    console.log('   ⚠️  配置读取性能需要优化 (> 0.01ms)');
  }
  
  if (avgModelTime < 0.01) {
    console.log('   ✅ 模型获取性能优秀 (< 0.01ms)');
  } else if (avgModelTime < 0.1) {
    console.log('   ✅ 模型获取性能良好 (< 0.1ms)');
  } else {
    console.log('   ⚠️  模型获取性能需要优化 (> 0.1ms)');
  }
}

async function initConfig() {
  console.log('🚀 初始化配置...');
  
  try {
    await AppInitializer.initialize();
    console.log('✅ 配置初始化成功');
    
    const status = StaticTableMappingService.getInitializationStatus();
    console.log(`   加载了 ${status.configCount} 个配置`);
    
  } catch (error) {
    console.error('❌ 配置初始化失败:', error);
    process.exit(1);
  }
}

async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  if (!command) {
    console.log('🛠️ 静态配置管理工具\n');
    console.log('使用方法:');
    Object.entries(COMMANDS).forEach(([cmd, desc]) => {
      console.log(`   npx tsx scripts/static-config-management.ts ${cmd} - ${desc}`);
    });
    console.log('\n示例:');
    console.log('   npx tsx scripts/static-config-management.ts status');
    console.log('   npx tsx scripts/static-config-management.ts refresh');
    console.log('   npx tsx scripts/static-config-management.ts list');
    return;
  }

  switch (command) {
    case 'status':
      await showStatus();
      break;
      
    case 'refresh':
      await refreshConfig();
      break;
      
    case 'list':
      await listConfigs();
      break;
      
    case 'test':
      await testPerformance();
      break;
      
    case 'init':
      await initConfig();
      break;
      
    default:
      console.error(`❌ 未知命令: ${command}`);
      console.log('可用命令:', Object.keys(COMMANDS).join(', '));
      process.exit(1);
  }
}

if (require.main === module) {
  main().catch(console.error);
} 