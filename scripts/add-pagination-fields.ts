import { PrismaClient } from '@prisma/client';

const db = new PrismaClient();

async function addPaginationFields() {
  console.log('🔧 开始为 DatabaseConfig 表新增翻页配置字段...\n');

  try {
    // 1. 新增字段
    console.log('📝 新增 defaultPageSize 字段...');
    await db.$executeRaw`
      ALTER TABLE "DatabaseConfig" 
      ADD COLUMN IF NOT EXISTS "defaultPageSize" INTEGER DEFAULT 20;
    `;
    console.log('✅ defaultPageSize 字段添加成功');

    console.log('📝 新增 maxPageSize 字段...');
    await db.$executeRaw`
      ALTER TABLE "DatabaseConfig" 
      ADD COLUMN IF NOT EXISTS "maxPageSize" INTEGER DEFAULT 100;
    `;
    console.log('✅ maxPageSize 字段添加成功');

    console.log('📝 新增 maxPages 字段...');
    await db.$executeRaw`
      ALTER TABLE "DatabaseConfig" 
      ADD COLUMN IF NOT EXISTS "maxPages" INTEGER DEFAULT 500;
    `;
    console.log('✅ maxPages 字段添加成功');

    // 2. 更新现有数据
    console.log('\n🎯 更新现有数据库配置...');
    
    console.log('📝 更新 us_class 配置...');
    await db.$executeRaw`
      UPDATE "DatabaseConfig" 
      SET 
        "defaultPageSize" = 20,
        "maxPageSize" = 50,
        "maxPages" = 200
      WHERE "code" = 'us_class';
    `;
    console.log('✅ us_class 配置更新成功');

    console.log('📝 更新 us_pmn 配置...');
    await db.$executeRaw`
      UPDATE "DatabaseConfig" 
      SET 
        "defaultPageSize" = 20,
        "maxPageSize" = 100,
        "maxPages" = 300
      WHERE "code" = 'us_pmn';
    `;
    console.log('✅ us_pmn 配置更新成功');

    // 3. 验证结果（使用原始SQL查询）
    console.log('\n🎯 验证配置结果...');
    const result = await db.$queryRaw`
      SELECT 
        "code",
        "name",
        "defaultPageSize",
        "maxPageSize", 
        "maxPages",
        "accessLevel"
      FROM "DatabaseConfig" 
      WHERE "isActive" = true
      ORDER BY "code";
    ` as any[];

    console.log('\n📊 当前数据库翻页配置:');
    console.log('┌─────────────┬──────────────────────┬─────────┬─────────┬─────────┬─────────────┐');
    console.log('│ 数据库代码  │ 数据库名称           │ 默认条数│ 最大条数│ 最大页数│ 访问级别    │');
    console.log('├─────────────┼──────────────────────┼─────────┼─────────┼─────────┼─────────────┤');
    
    result.forEach(config => {
      const code = String(config.code).padEnd(11);
      const name = String(config.name).padEnd(20);
      const defaultSize = String(config.defaultPageSize || 'N/A').padEnd(7);
      const maxSize = String(config.maxPageSize || 'N/A').padEnd(7);
      const maxPages = String(config.maxPages || 'N/A').padEnd(7);
      const accessLevel = String(config.accessLevel).padEnd(11);
      
      console.log(`│ ${code} │ ${name} │ ${defaultSize} │ ${maxSize} │ ${maxPages} │ ${accessLevel} │`);
    });
    
    console.log('└─────────────┴──────────────────────┴─────────┴─────────┴─────────┴─────────────┘');

    console.log('\n✨ 翻页配置字段新增完成！');
    console.log('\n📋 配置说明:');
    console.log('  • us_class: 默认20条/页，最大50条/页，最多200页');
    console.log('  • us_pmn: 默认20条/页，最大100条/页，最多300页');
    console.log('  • 当用户翻页达到限制时，会显示英文提示信息');

  } catch (error) {
    console.error('❌ 执行失败:', error);
    throw error;
  } finally {
    await db.$disconnect();
  }
}

// 运行脚本
addPaginationFields().catch(console.error);
