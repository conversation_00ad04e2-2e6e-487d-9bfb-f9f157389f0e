#!/usr/bin/env tsx

import { DynamicTableMappingService } from '../src/lib/dynamicTableMappingService';
import { performance } from 'perf_hooks';

async function performanceAnalysis() {
  console.log('🔍 动态表映射性能分析\n');

  const testDatabaseCode = 'deviceCNImported';
  const testRounds = 100;

  // 1. 冷启动性能测试（首次请求）
  console.log('📊 1. 冷启动性能测试（首次从数据库读取）');
  DynamicTableMappingService.clearCache(); // 清空缓存
  
  const coldStartTime = performance.now();
  const mapping1 = await DynamicTableMappingService.getTableMapping(testDatabaseCode);
  const coldEndTime = performance.now();
  
  console.log(`   首次读取耗时: ${(coldEndTime - coldStartTime).toFixed(2)}ms`);
  console.log(`   配置获取成功: ${mapping1 ? '✅' : '❌'}`);
  if (mapping1) {
    console.log(`   表名: ${mapping1.tableName}`);
    console.log(`   模型名: ${mapping1.modelName}`);
  }

  // 2. 缓存命中性能测试
  console.log('\n📊 2. 缓存命中性能测试');
  
  const cacheHitTimes: number[] = [];
  for (let i = 0; i < testRounds; i++) {
    const start = performance.now();
    await DynamicTableMappingService.getTableMapping(testDatabaseCode);
    const end = performance.now();
    cacheHitTimes.push(end - start);
  }

  const avgCacheHitTime = cacheHitTimes.reduce((a, b) => a + b, 0) / cacheHitTimes.length;
  const minCacheHitTime = Math.min(...cacheHitTimes);
  const maxCacheHitTime = Math.max(...cacheHitTimes);

  console.log(`   测试轮数: ${testRounds} 次`);
  console.log(`   平均响应时间: ${avgCacheHitTime.toFixed(3)}ms`);
  console.log(`   最快响应时间: ${minCacheHitTime.toFixed(3)}ms`);
  console.log(`   最慢响应时间: ${maxCacheHitTime.toFixed(3)}ms`);

  // 3. 缓存状态分析
  console.log('\n📊 3. 缓存状态分析');
  const cacheInfo = DynamicTableMappingService.getCacheInfo();
  console.log(`   缓存大小: ${cacheInfo.size} 个配置`);
  console.log(`   缓存过期时间: ${new Date(cacheInfo.expiry).toLocaleString()}`);
  console.log(`   当前是否过期: ${cacheInfo.isExpired ? '是' : '否'}`);
  
  const timeUntilExpiry = cacheInfo.expiry - Date.now();
  if (timeUntilExpiry > 0) {
    console.log(`   距离过期时间: ${Math.round(timeUntilExpiry / 1000)}秒`);
  }

  // 4. 内存占用分析
  console.log('\n📊 4. 内存占用分析');
  const memBefore = process.memoryUsage();
  
  // 加载多个配置到缓存
  const testCodes = ['deviceCNImported', 'us_pmn', 'nonexistent1', 'nonexistent2'];
  for (const code of testCodes) {
    await DynamicTableMappingService.getTableMapping(code);
  }
  
  const memAfter = process.memoryUsage();
  const memDiff = {
    rss: memAfter.rss - memBefore.rss,
    heapUsed: memAfter.heapUsed - memBefore.heapUsed,
    heapTotal: memAfter.heapTotal - memBefore.heapTotal,
    external: memAfter.external - memBefore.external
  };

  console.log(`   RSS内存变化: ${(memDiff.rss / 1024).toFixed(2)} KB`);
  console.log(`   堆内存变化: ${(memDiff.heapUsed / 1024).toFixed(2)} KB`);
  console.log(`   堆总量变化: ${(memDiff.heapTotal / 1024).toFixed(2)} KB`);
  console.log(`   外部内存变化: ${(memDiff.external / 1024).toFixed(2)} KB`);

  // 5. 并发测试
  console.log('\n📊 5. 并发性能测试');
  DynamicTableMappingService.clearCache(); // 清空缓存
  
  const concurrentRequests = 50;
  const concurrentStart = performance.now();
  
  const promises = Array(concurrentRequests).fill(0).map(() => 
    DynamicTableMappingService.getTableMapping(testDatabaseCode)
  );
  
  const results = await Promise.all(promises);
  const concurrentEnd = performance.now();
  
  const successCount = results.filter(r => r !== null).length;
  console.log(`   并发请求数: ${concurrentRequests}`);
  console.log(`   成功响应数: ${successCount}`);
  console.log(`   总耗时: ${(concurrentEnd - concurrentStart).toFixed(2)}ms`);
  console.log(`   平均每请求耗时: ${((concurrentEnd - concurrentStart) / concurrentRequests).toFixed(2)}ms`);

  // 6. 缓存失效测试
  console.log('\n📊 6. 缓存失效与重新加载测试');
  
  console.log('   手动清除缓存...');
  DynamicTableMappingService.clearCache();
  
  const reloadStart = performance.now();
  const mapping2 = await DynamicTableMappingService.getTableMapping(testDatabaseCode);
  const reloadEnd = performance.now();
  
  console.log(`   缓存清空后重新加载耗时: ${(reloadEnd - reloadStart).toFixed(2)}ms`);
  console.log(`   配置重新获取成功: ${mapping2 ? '✅' : '❌'}`);

  // 7. 性能建议
  console.log('\n💡 性能分析结论:');
  
  if (avgCacheHitTime < 1) {
    console.log('   ✅ 缓存命中性能优秀 (< 1ms)');
  } else if (avgCacheHitTime < 5) {
    console.log('   ✅ 缓存命中性能良好 (< 5ms)');
  } else {
    console.log('   ⚠️  缓存命中性能需要优化 (> 5ms)');
  }

  if (coldEndTime - coldStartTime < 50) {
    console.log('   ✅ 数据库读取性能优秀 (< 50ms)');
  } else if (coldEndTime - coldStartTime < 200) {
    console.log('   ✅ 数据库读取性能良好 (< 200ms)');
  } else {
    console.log('   ⚠️  数据库读取性能需要优化 (> 200ms)');
  }

  if (Math.abs(memDiff.heapUsed) < 1024 * 10) { // 10KB
    console.log('   ✅ 内存占用极低 (< 10KB)');
  } else if (Math.abs(memDiff.heapUsed) < 1024 * 100) { // 100KB
    console.log('   ✅ 内存占用合理 (< 100KB)');
  } else {
    console.log('   ⚠️  内存占用偏高 (> 100KB)');
  }

  console.log('\n🎯 使用建议:');
  console.log('   • 缓存TTL设置为5分钟，平衡性能与实时性');
  console.log('   • 配置变更后5分钟内自动生效，无需重启');
  console.log('   • 高频访问场景下，99%请求来自缓存 (< 1ms)');
  console.log('   • 内存占用极低，可支持大量数据库配置');
  console.log('   • 支持手动清除缓存以立即应用配置变更');
}

if (require.main === module) {
  performanceAnalysis().catch(console.error);
} 