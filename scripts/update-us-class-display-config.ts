#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 更新 us_class 数据库的统计显示配置
 * 设置合理的默认显示数量和最大显示数量
 */

async function updateUsClassDisplayConfig() {
  console.log('📊 更新 us_class 统计显示配置...');

  try {
    // 统计显示配置
    const displayConfigs = [
      {
        fieldName: 'deviceclass',
        statisticsDefaultLimit: 5,  // 默认显示5个
        statisticsMaxLimit: 15,     // 最多显示15个
        description: '器械类别数量较少，适合显示全部'
      },
      {
        fieldName: 'medicalspecialty',
        statisticsDefaultLimit: 5,  // 默认显示5个
        statisticsMaxLimit: 20,     // 最多显示20个
        description: '医学专科种类中等，适合显示前20个'
      },
      {
        fieldName: 'productcode',
        statisticsDefaultLimit: 8,  // 默认显示8个（产品代码用户更关心）
        statisticsMaxLimit: 100,    // 最多显示100个
        description: '产品代码数量很多，用户经常需要查找特定代码'
      },
      {
        fieldName: 'regulationnumber',
        statisticsDefaultLimit: 5,  // 默认显示5个
        statisticsMaxLimit: 50,     // 最多显示50个
        description: '法规编号数量较多，显示前50个主要的'
      }
    ];

    console.log(`\n🔧 更新 ${displayConfigs.length} 个字段的显示配置...`);

    for (const config of displayConfigs) {
      try {
        const result = await db.fieldConfig.updateMany({
          where: {
            databaseCode: 'us_class',
            fieldName: config.fieldName,
            isActive: true,
          },
          data: {
            statisticsDefaultLimit: config.statisticsDefaultLimit,
            statisticsMaxLimit: config.statisticsMaxLimit,
          },
        });

        if (result.count > 0) {
          console.log(`  ✅ ${config.fieldName}: default=${config.statisticsDefaultLimit}, max=${config.statisticsMaxLimit}`);
          console.log(`     ${config.description}`);
        } else {
          console.log(`  ⚠️  ${config.fieldName} - 未找到匹配的字段配置`);
        }
      } catch (error) {
        console.error(`  ❌ ${config.fieldName} - 更新失败:`, error);
      }
    }

    // 验证配置结果
    console.log('\n🔍 验证显示配置...');
    const updatedFields = await db.fieldConfig.findMany({
      where: {
        databaseCode: 'us_class',
        isStatisticsEnabled: true,
        isActive: true,
      },
      select: {
        fieldName: true,
        statisticsDisplayName: true,
        statisticsDefaultLimit: true,
        statisticsMaxLimit: true,
        statisticsOrder: true,
        statisticsSortOrder: true,
      },
      orderBy: {
        statisticsOrder: 'asc'
      }
    });

    console.log('\n📋 已配置的统计显示设置:');
    updatedFields.forEach((field, index) => {
      console.log(`\n   ${index + 1}. ${field.statisticsDisplayName} (${field.fieldName})`);
      console.log(`      默认显示: ${field.statisticsDefaultLimit} 项`);
      console.log(`      最大显示: ${field.statisticsMaxLimit} 项`);
      console.log(`      排序方向: ${(field as any).statisticsSortOrder}`);
    });

    console.log('\n✅ us_class 统计显示配置更新完成！');
    console.log('\n📝 配置说明:');
    console.log('   - 器械类别: 默认5项，最多15项');
    console.log('   - 医学专科: 默认5项，最多20项');
    console.log('   - 产品代码: 默认8项，最多100项（用户最关心）');
    console.log('   - 法规编号: 默认5项，最多50项');

  } catch (error) {
    console.error('❌ 更新 us_class 显示配置失败:', error);
    throw error;
  } finally {
    await db.$disconnect();
  }
}

// 执行脚本
if (require.main === module) {
  updateUsClassDisplayConfig()
    .then(() => {
      console.log('\n🎉 脚本执行完成！');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 脚本执行失败:', error);
      process.exit(1);
    });
}

export { updateUsClassDisplayConfig };
