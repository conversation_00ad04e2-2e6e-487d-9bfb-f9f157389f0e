-- 为 FieldConfig 表和字段添加注释说明
-- 这些注释将帮助开发者理解每个字段的作用和用途

-- 1. 为表添加注释
COMMENT ON TABLE "FieldConfig" IS '字段配置表 - 统一管理所有数据库字段的显示、搜索、筛选、排序、导出等功能配置';

-- 2. 为各个字段添加详细注释
COMMENT ON COLUMN "FieldConfig"."id" IS '主键 - 字段配置的唯一标识符';
COMMENT ON COLUMN "FieldConfig"."databaseCode" IS '数据库代码 - 关联到 DatabaseConfig.code，标识此配置属于哪个数据库';
COMMENT ON COLUMN "FieldConfig"."fieldName" IS '字段名称 - 数据库表中的实际字段名';
COMMENT ON COLUMN "FieldConfig"."displayName" IS '显示名称 - 在用户界面中显示的友好名称';
COMMENT ON COLUMN "FieldConfig"."fieldType" IS '字段类型 - 数据类型：text/date/number/boolean/select/json，影响显示格式和验证';

-- 显示控制相关字段
COMMENT ON COLUMN "FieldConfig"."isVisible" IS '列表页可见性 - true: 在数据列表页显示此字段，false: 隐藏';
COMMENT ON COLUMN "FieldConfig"."listOrder" IS '列表显示顺序 - 控制字段在数据列表页中的列显示顺序，数值越小越靠前';
COMMENT ON COLUMN "FieldConfig"."detailOrder" IS '详情页显示顺序 - 控制字段在详情页中的显示顺序，0表示不显示，>0按数值排序';

-- 功能控制相关字段
COMMENT ON COLUMN "FieldConfig"."isSearchable" IS '可搜索性 - true: 此字段可以被全文搜索，false: 不参与搜索';
COMMENT ON COLUMN "FieldConfig"."searchType" IS '搜索类型 - 搜索匹配方式：exact/contains/range/date_range/starts_with/ends_with';
COMMENT ON COLUMN "FieldConfig"."isFilterable" IS '可筛选性 - true: 在筛选面板中显示此字段的筛选器，false: 不显示';
COMMENT ON COLUMN "FieldConfig"."filterType" IS '筛选器类型 - 筛选器UI类型：select/input/date_range/checkbox/multi_select/range';
COMMENT ON COLUMN "FieldConfig"."isSortable" IS '可排序性 - true: 列表页中此字段可以点击排序，false: 不可排序';
COMMENT ON COLUMN "FieldConfig"."sortOrder" IS '排序优先级 - 字段配置查询时的排序优先级，也用于默认排序字段选择';

-- 特殊功能字段
COMMENT ON COLUMN "FieldConfig"."todetail" IS '详情页链接 - true: 此字段显示为链接，点击跳转到详情页，false: 普通文本显示';

-- 配置和验证
COMMENT ON COLUMN "FieldConfig"."validationRules" IS '验证规则 - JSON格式存储字段验证规则，如长度限制、格式要求等';
COMMENT ON COLUMN "FieldConfig"."options" IS '选项配置 - JSON格式存储字段选项，如下拉框的选项列表、筛选器配置等';

-- 统计功能相关字段
COMMENT ON COLUMN "FieldConfig"."isStatisticsEnabled" IS '统计功能启用 - true: 为此字段生成统计图表，false: 不生成统计';
COMMENT ON COLUMN "FieldConfig"."statisticsOrder" IS '统计显示顺序 - 控制统计图表在统计页面中的显示顺序';
COMMENT ON COLUMN "FieldConfig"."statisticsType" IS '统计类型 - 统计方式：count/sum/avg/min_max/group_by';
COMMENT ON COLUMN "FieldConfig"."statisticsDisplayName" IS '统计显示名称 - 在统计图表中显示的名称，如果为空则使用displayName';
COMMENT ON COLUMN "FieldConfig"."statisticsConfig" IS '统计配置 - JSON格式存储统计相关配置，如图表类型、颜色等';
COMMENT ON COLUMN "FieldConfig"."statisticsSortOrder" IS '统计排序方向 - 统计结果的排序方向：asc(正序)/desc(倒序)';
COMMENT ON COLUMN "FieldConfig"."statisticsDefaultLimit" IS '统计默认限制 - 统计结果默认显示的条目数量';
COMMENT ON COLUMN "FieldConfig"."statisticsMaxLimit" IS '统计最大限制 - 统计结果最多显示的条目数量';

-- 导出功能相关字段
COMMENT ON COLUMN "FieldConfig"."isExportable" IS '可导出性 - true: 此字段包含在数据导出中，false: 导出时排除';
COMMENT ON COLUMN "FieldConfig"."exportOrder" IS '导出顺序 - 控制字段在导出文件中的列顺序，0表示不导出';
COMMENT ON COLUMN "FieldConfig"."exportDisplayName" IS '导出列名 - 在导出文件中使用的列标题，如果为空则使用displayName';

-- 状态和时间戳
COMMENT ON COLUMN "FieldConfig"."isActive" IS '激活状态 - true: 配置生效，false: 配置禁用（软删除）';
COMMENT ON COLUMN "FieldConfig"."createdAt" IS '创建时间 - 配置创建的时间戳';
COMMENT ON COLUMN "FieldConfig"."updatedAt" IS '更新时间 - 配置最后修改的时间戳';
