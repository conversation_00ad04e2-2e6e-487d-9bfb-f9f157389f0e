#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

async function fixExpeditedReviewOrder() {
  console.log('🔧 修复 expeditedreview 字段顺序问题...');
  
  try {
    // 1. 检查当前的 multi_select 字段顺序
    console.log('\n📋 1. 检查当前 multi_select 字段顺序...');
    
    const multiSelectFields = await db.fieldConfig.findMany({
      where: {
        databaseCode: 'us_pmn',
        filterType: 'multi_select',
        isFilterable: true,
        isActive: true
      },
      select: {
        fieldName: true,
        displayName: true,
        listOrder: true
      },
      orderBy: { listOrder: 'asc' }
    });
    
    console.log('当前 multi_select 字段:');
    multiSelectFields.forEach((field, index) => {
      console.log(`  ${index + 1}. ${field.fieldName} (${field.displayName}) - 顺序: ${field.listOrder}`);
    });
    
    // 2. 修复 expeditedreview 的 listOrder
    console.log('\n🔧 2. 修复 expeditedreview 的 listOrder...');
    
    const result = await db.fieldConfig.updateMany({
      where: {
        databaseCode: 'us_pmn',
        fieldName: 'expeditedreview',
        isActive: true
      },
      data: {
        listOrder: 15, // 设置一个合理的顺序
        updatedAt: new Date()
      }
    });
    
    if (result.count > 0) {
      console.log('✅ expeditedreview listOrder 已更新为 15');
    } else {
      console.log('❌ expeditedreview listOrder 更新失败');
    }
    
    // 3. 验证更新后的顺序
    console.log('\n📋 3. 验证更新后的顺序...');
    
    const updatedFields = await db.fieldConfig.findMany({
      where: {
        databaseCode: 'us_pmn',
        filterType: 'multi_select',
        isFilterable: true,
        isActive: true
      },
      select: {
        fieldName: true,
        displayName: true,
        listOrder: true
      },
      orderBy: { listOrder: 'asc' }
    });
    
    console.log('更新后的 multi_select 字段:');
    updatedFields.forEach((field, index) => {
      console.log(`  ${index + 1}. ${field.fieldName} (${field.displayName}) - 顺序: ${field.listOrder}`);
    });
    
    // 4. 检查 expeditedreview 数据分布
    console.log('\n📊 4. 检查 expeditedreview 数据分布...');
    
    const expeditedStats = await db.uSPremarketNotification.groupBy({
      by: ['expeditedreview'],
      _count: { expeditedreview: true },
      orderBy: { _count: { expeditedreview: 'desc' } }
    });
    
    console.log('expeditedreview 数据分布:');
    expeditedStats.forEach((item, index) => {
      const value = item.expeditedreview === null ? 'NULL' : (item.expeditedreview || 'Empty String');
      console.log(`  ${index + 1}. ${value}: ${item._count.expeditedreview} 条`);
    });
    
    // 5. 测试 meta API
    console.log('\n🌐 5. 测试 meta API...');
    
    try {
      const response = await fetch('http://localhost:3001/api/meta/us_pmn');
      const result = await response.json();
      
      if (result.success) {
        const expeditedData = result.dataWithCounts?.expeditedreview;
        if (expeditedData) {
          console.log('✅ Meta API 中的 expeditedreview 数据:');
          expeditedData.forEach((item: any, index: number) => {
            console.log(`  ${index + 1}. ${item.value}: ${item.count} 条`);
          });
        } else {
          console.log('❌ Meta API 中没有 expeditedreview 数据');
        }
      } else {
        console.log('❌ Meta API 失败:', result.error);
      }
    } catch (error) {
      console.log('❌ Meta API 请求失败:', (error as Error).message);
    }
    
    // 6. 建议
    console.log('\n💡 6. 建议和下一步...');
    
    console.log('关于 expeditedreview 数据:');
    console.log('- 大部分记录的 expeditedreview 字段为 NULL');
    console.log('- 只有 28 条记录有 "Y" 值');
    console.log('- 这可能是正常的业务数据分布');
    
    console.log('\n关于 multi_select 显示:');
    console.log('- 字段配置已正确');
    console.log('- listOrder 已修复');
    console.log('- 需要刷新浏览器页面查看效果');
    
    console.log('\n测试步骤:');
    console.log('1. 刷新浏览器页面: http://localhost:3001/data/list/us_pmn');
    console.log('2. 查看筛选面板中是否出现 Expedited Review 多选框');
    console.log('3. 检查选项是否包含 "Y" 和 "N/A"');
    console.log('4. 测试联动功能是否正常');
    
  } catch (error) {
    console.error('❌ 修复失败:', error);
  } finally {
    await db.$disconnect();
  }
}

fixExpeditedReviewOrder().catch(console.error);
