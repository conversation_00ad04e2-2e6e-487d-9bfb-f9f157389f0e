-- 为 DatabaseConfig 表新增翻页配置字段
-- 注意：这个脚本只新增字段，不会删除或修改现有数据

-- 1. 新增翻页配置字段
ALTER TABLE "DatabaseConfig" 
ADD COLUMN IF NOT EXISTS "defaultPageSize" INTEGER DEFAULT 20;

ALTER TABLE "DatabaseConfig" 
ADD COLUMN IF NOT EXISTS "maxPageSize" INTEGER DEFAULT 100;

ALTER TABLE "DatabaseConfig" 
ADD COLUMN IF NOT EXISTS "maxPages" INTEGER DEFAULT 500;

-- 2. 为现有的两个数据库设置具体的翻页配置
-- us_class 数据库配置 (数据量较大，设置较严格的限制)
UPDATE "DatabaseConfig" 
SET 
  "defaultPageSize" = 20,
  "maxPageSize" = 50,
  "maxPages" = 200
WHERE "code" = 'us_class';

-- us_pmn 数据库配置 (数据量中等，设置中等限制)
UPDATE "DatabaseConfig" 
SET 
  "defaultPageSize" = 20,
  "maxPageSize" = 100,
  "maxPages" = 300
WHERE "code" = 'us_pmn';

-- 3. 验证更新结果
SELECT 
  "code",
  "name",
  "defaultPageSize",
  "maxPageSize", 
  "maxPages",
  "accessLevel"
FROM "DatabaseConfig" 
WHERE "isActive" = true
ORDER BY "code";
