#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';
import { DatabaseFieldConfig, validateDatabaseConfig, ConfigCacheService } from '../src/lib/configCache';
import { CONFIG_TEMPLATES, createFieldsFromTemplate } from '../src/lib/config-templates';

/**
 * 配置管理工具
 * 提供数据库配置的创建、更新、验证等功能
 */

export interface DatabaseConfigInput {
  code: string;
  name: string;
  category: string;
  description?: string;
  tableName?: string;
  modelName?: string;
  accessLevel: 'free' | 'premium' | 'enterprise';
  fields: Omit<DatabaseFieldConfig, 'id' | 'databaseCode' | 'createdAt' | 'updatedAt'>[];
}

export class ConfigManager {
  
  /**
   * 创建完整的数据库配置
   */
  static async createDatabaseConfig(config: DatabaseConfigInput): Promise<void> {
    console.log(`🔧 创建数据库配置: ${config.code}`);

    try {
      // 1. 验证配置
      const validation = validateDatabaseConfig({ fields: config.fields as DatabaseFieldConfig[] });
      if (!validation.isValid) {
        console.error('❌ 配置验证失败:');
        validation.errors.forEach(error => console.error(`   - ${error}`));
        Object.entries(validation.fieldErrors).forEach(([field, errors]) => {
          console.error(`   字段 ${field}:`);
          errors.forEach(error => console.error(`     - ${error}`));
        });
        throw new Error('配置验证失败');
      }

      // 2. 创建数据库配置
      const databaseConfig = await db.databaseConfig.upsert({
        where: { code: config.code },
        update: {
          name: config.name,
          category: config.category,
          description: config.description,
          tableName: config.tableName,
          modelName: config.modelName,
          accessLevel: config.accessLevel,
          updatedAt: new Date(),
        },
        create: {
          code: config.code,
          name: config.name,
          category: config.category,
          description: config.description || '',
          tableName: config.tableName,
          modelName: config.modelName,
          accessLevel: config.accessLevel,
          isActive: true,
        },
      });

      console.log(`✅ 数据库配置已创建: ${databaseConfig.name}`);

      // 3. 批量创建字段配置
      let createdCount = 0;
      let updatedCount = 0;

      for (const field of config.fields) {
        const result = await db.fieldConfig.upsert({
          where: {
            databaseCode_fieldName: {
              databaseCode: config.code,
              fieldName: field.fieldName,
            },
          },
          update: {
            ...field,
            validationRules: field.validationRules as any,
            options: field.options as any,
            statisticsConfig: field.statisticsConfig as any,
            updatedAt: new Date(),
          },
          create: {
            ...field,
            validationRules: field.validationRules as any,
            options: field.options as any,
            statisticsConfig: field.statisticsConfig as any,
            databaseCode: config.code,
            isActive: true,
          },
        });

        if (result.createdAt === result.updatedAt) {
          createdCount++;
        } else {
          updatedCount++;
        }
      }

      console.log(`✅ 字段配置完成: 创建 ${createdCount} 个，更新 ${updatedCount} 个`);

      // 4. 清除缓存
      await ConfigCacheService.clearDatabaseCache(config.code);
      console.log(`🧹 已清除 ${config.code} 的缓存`);

    } catch (error) {
      console.error(`❌ 创建配置失败:`, error);
      throw error;
    }
  }

  /**
   * 从模板创建数据库配置
   */
  static async createFromTemplate(
    templateKey: keyof typeof CONFIG_TEMPLATES,
    databaseInfo: {
      code: string;
      name: string;
      category: string;
      description?: string;
      tableName?: string;
      modelName?: string;
      accessLevel: 'free' | 'premium' | 'enterprise';
    }
  ): Promise<void> {
    console.log(`🎨 使用模板 ${templateKey} 创建数据库配置`);

    try {
      const fields = createFieldsFromTemplate(templateKey, databaseInfo.code);
      
      await this.createDatabaseConfig({
        ...databaseInfo,
        fields: fields.map(field => {
          return field;
        }),
      });

      console.log(`✅ 已使用模板 ${templateKey} 创建配置`);
    } catch (error) {
      console.error(`❌ 从模板创建配置失败:`, error);
      throw error;
    }
  }

  /**
   * 批量更新字段的筛选类型
   */
  static async updateFilterTypes(
    databaseCode: string,
    updates: Array<{ fieldName: string; filterType: string }>
  ): Promise<void> {
    console.log(`🔄 批量更新 ${databaseCode} 的筛选类型`);

    try {
      for (const update of updates) {
        await db.fieldConfig.updateMany({
          where: {
            databaseCode,
            fieldName: update.fieldName,
            isActive: true,
          },
          data: {
            filterType: update.filterType as any,
            updatedAt: new Date(),
          },
        });

        console.log(`✅ 已更新 ${update.fieldName} 的筛选类型为 ${update.filterType}`);
      }

      // 清除缓存
      await ConfigCacheService.clearDatabaseCache(databaseCode);
      console.log(`🧹 已清除 ${databaseCode} 的缓存`);

    } catch (error) {
      console.error(`❌ 批量更新失败:`, error);
      throw error;
    }
  }

  /**
   * 验证数据库配置
   */
  static async validateConfig(databaseCode: string): Promise<void> {
    console.log(`🔍 验证数据库配置: ${databaseCode}`);

    try {
      // 获取配置
      const config = await ConfigCacheService.getDatabaseConfig(databaseCode);
      
      // 验证配置
      const validation = validateDatabaseConfig(config);
      
      if (validation.isValid) {
        console.log(`✅ 配置验证通过`);
        console.log(`   - 字段数量: ${config.fields.length}`);
        console.log(`   - 可筛选字段: ${config.fields.filter(f => f.isFilterable).length}`);
        console.log(`   - 可搜索字段: ${config.fields.filter(f => f.isSearchable).length}`);
      } else {
        console.error(`❌ 配置验证失败:`);
        validation.errors.forEach(error => console.error(`   - ${error}`));
        Object.entries(validation.fieldErrors).forEach(([field, errors]) => {
          console.error(`   字段 ${field}:`);
          errors.forEach(error => console.error(`     - ${error}`));
        });
      }

    } catch (error) {
      console.error(`❌ 验证失败:`, error);
      throw error;
    }
  }

  /**
   * 列出所有数据库配置
   */
  static async listConfigs(): Promise<void> {
    console.log(`📋 列出所有数据库配置`);

    try {
      const configs = await db.databaseConfig.findMany({
        where: { isActive: true },
        orderBy: { code: 'asc' },
      });

      console.log(`\n找到 ${configs.length} 个数据库配置:`);

      for (const config of configs) {
        // 单独查询字段数量
        const fieldCount = await db.fieldConfig.count({
          where: {
            databaseCode: config.code,
            isActive: true,
          },
        });

        console.log(`\n📊 ${config.code}`);
        console.log(`   名称: ${config.name}`);
        console.log(`   分类: ${config.category}`);
        console.log(`   访问级别: ${config.accessLevel}`);
        console.log(`   表名: ${config.tableName || 'N/A'}`);
        console.log(`   模型名: ${config.modelName || 'N/A'}`);
        console.log(`   字段数量: ${fieldCount}`);
      }

    } catch (error) {
      console.error(`❌ 列出配置失败:`, error);
      throw error;
    }
  }

  /**
   * 导出配置为 JSON
   */
  static async exportConfig(databaseCode: string): Promise<void> {
    console.log(`📤 导出配置: ${databaseCode}`);

    try {
      const databaseConfig = await (db.databaseConfig as any).findUnique({
        where: { code: databaseCode },
        include: { fieldConfigs: true },
      });

      if (!databaseConfig) {
        throw new Error(`数据库配置不存在: ${databaseCode}`);
      }

      const exportData = {
        database: {
          code: databaseConfig.code,
          name: databaseConfig.name,
          category: databaseConfig.category,
          description: databaseConfig.description,
          tableName: databaseConfig.tableName,
          modelName: databaseConfig.modelName,
          accessLevel: databaseConfig.accessLevel,
        },
        fields: databaseConfig.fieldConfigs.map((field: any) => {
          const { id, databaseCode, createdAt, updatedAt, ...fieldData } = field;
          return fieldData;
        }),
      };

      const fs = await import('fs');
      const filename = `config-export-${databaseCode}-${new Date().toISOString().split('T')[0]}.json`;
      fs.writeFileSync(filename, JSON.stringify(exportData, null, 2));

      console.log(`✅ 配置已导出到: ${filename}`);

    } catch (error) {
      console.error(`❌ 导出失败:`, error);
      throw error;
    }
  }
}

// 如果直接运行此脚本，显示帮助信息
if (require.main === module) {
  console.log(`
🛠️  配置管理工具

使用方法:
  npx tsx scripts/config-manager.ts

可用命令:
  - ConfigManager.listConfigs()           # 列出所有配置
  - ConfigManager.validateConfig(code)    # 验证配置
  - ConfigManager.exportConfig(code)      # 导出配置
  
示例:
  import { ConfigManager } from './scripts/config-manager';
  await ConfigManager.listConfigs();
`);
}
