#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 全面诊断 us_pmn 数据库页面问题
 */

async function diagnoseUsPmnIssue() {
  console.log('🔍 全面诊断 us_pmn 数据库页面问题...\n');

  try {
    // 1. 检查数据库配置
    console.log('📋 1. 检查数据库配置...');
    const dbConfig = await db.databaseConfig.findUnique({
      where: { code: 'us_pmn' },
      select: {
        code: true,
        name: true,
        accessLevel: true,
        isActive: true,
        modelName: true,
        tableName: true,
        defaultSort: true
      }
    });

    if (!dbConfig) {
      console.log('❌ 数据库配置不存在');
      return;
    }

    console.log('✅ 数据库配置正常:');
    console.log(`   代码: ${dbConfig.code}`);
    console.log(`   名称: ${dbConfig.name}`);
    console.log(`   访问级别: ${dbConfig.accessLevel}`);
    console.log(`   模型名: ${dbConfig.modelName}`);
    console.log(`   表名: ${dbConfig.tableName}`);
    console.log(`   默认排序: ${JSON.stringify(dbConfig.defaultSort)}`);

    // 2. 检查字段配置
    console.log('\n📊 2. 检查字段配置...');
    const fieldConfigs = await db.fieldConfig.findMany({
      where: { databaseCode: 'us_pmn', isActive: true },
      select: {
        fieldName: true,
        displayName: true,
        isVisible: true,
        isFilterable: true,
        isSortable: true,
        listOrder: true,
        sortOrder: true,
        filterType: true
      },
      orderBy: { listOrder: 'asc' }
    });

    console.log(`✅ 找到 ${fieldConfigs.length} 个字段配置`);

    // 可见字段
    const visibleFields = fieldConfigs.filter(f => f.isVisible);
    console.log(`\n📈 可见字段 (${visibleFields.length} 个):`);
    visibleFields.forEach((field, index) => {
      console.log(`   ${index + 1}. ${field.fieldName} (${field.displayName}) - listOrder: ${field.listOrder}, sortOrder: ${field.sortOrder}`);
    });

    // 可筛选字段
    const filterableFields = fieldConfigs.filter(f => f.isFilterable);
    console.log(`\n🔍 可筛选字段 (${filterableFields.length} 个):`);
    filterableFields.forEach((field, index) => {
      const visibility = field.isVisible ? '可见' : '隐藏';
      console.log(`   ${index + 1}. ${field.fieldName} (${field.displayName}) - ${field.filterType} [${visibility}]`);
    });

    // 3. 检查数据
    console.log('\n📈 3. 检查数据...');
    const model = (db as any).uSPremarketNotification;
    if (!model) {
      console.log('❌ 模型 uSPremarketNotification 不存在');
      return;
    }

    const totalCount = await model.count();
    console.log(`✅ 数据记录总数: ${totalCount.toLocaleString()}`);

    if (totalCount > 0) {
      const sampleData = await model.findFirst({
        select: {
          id: true,
          knumber: true,
          devicename: true,
          applicant: true,
          datereceived: true,
          decisiondate: true,
          decision: true,
          productcode: true,
          type: true
        }
      });
      console.log('📝 示例数据:');
      console.log(`   ID: ${sampleData.id}`);
      console.log(`   K Number: ${sampleData.knumber}`);
      console.log(`   Device Name: ${sampleData.devicename}`);
      console.log(`   Applicant: ${sampleData.applicant}`);
      console.log(`   Date Received: ${sampleData.datereceived}`);
      console.log(`   Decision Date: ${sampleData.decisiondate}`);
      console.log(`   Decision: ${sampleData.decision}`);
      console.log(`   Product Code: ${sampleData.productcode}`);
      console.log(`   Type: ${sampleData.type}`);
    }

    // 4. 测试 API 端点
    console.log('\n🌐 4. 测试 API 端点...');
    
    // 测试数据 API
    try {
      const response = await fetch('http://localhost:3000/api/data/us_pmn?page=1&limit=1');
      const result = await response.json();
      
      if (result.success) {
        console.log('✅ 数据 API 正常');
        console.log(`   返回数据条数: ${result.data?.length || 0}`);
        console.log(`   总记录数: ${result.pagination?.totalCount || 0}`);
        console.log(`   配置字段数: ${result.config?.fields?.length || 0}`);
        
        if (result.config?.fields) {
          const visibleApiFields = result.config.fields.filter((f: any) => f.isVisible);
          console.log(`   可见字段数: ${visibleApiFields.length}`);
          console.log(`   可见字段: ${visibleApiFields.map((f: any) => f.fieldName).join(', ')}`);
        }
      } else {
        console.log('❌ 数据 API 返回错误:', result.error);
      }
    } catch (error) {
      console.log('❌ 数据 API 请求失败:', (error as Error).message);
    }

    // 测试元数据 API
    try {
      const response = await fetch('http://localhost:3000/api/meta/us_pmn');
      const result = await response.json();
      
      if (result.success) {
        console.log('✅ 元数据 API 正常');
        const metaFields = Object.keys(result.data || {});
        console.log(`   元数据字段数: ${metaFields.length}`);
        console.log(`   元数据字段: ${metaFields.slice(0, 5).join(', ')}${metaFields.length > 5 ? '...' : ''}`);
      } else {
        console.log('❌ 元数据 API 返回错误:', result.error);
      }
    } catch (error) {
      console.log('❌ 元数据 API 请求失败:', (error as Error).message);
    }

    // 5. 检查权限配置
    console.log('\n🔐 5. 检查权限配置...');
    console.log(`   数据库访问级别: ${dbConfig.accessLevel}`);
    
    if (dbConfig.accessLevel === 'free') {
      console.log('✅ 权限配置正确 - 免费访问');
    } else {
      console.log('⚠️  权限配置可能有问题 - 需要高级权限');
    }

    // 6. 总结
    console.log('\n📋 6. 诊断总结...');
    
    const issues = [];
    
    if (visibleFields.length === 0) {
      issues.push('没有可见字段配置');
    }
    
    if (filterableFields.length === 0) {
      issues.push('没有可筛选字段配置');
    }
    
    if (totalCount === 0) {
      issues.push('没有数据记录');
    }
    
    if (dbConfig.accessLevel !== 'free') {
      issues.push('权限级别不是免费访问');
    }

    if (issues.length === 0) {
      console.log('✅ 所有检查都通过，us_pmn 配置应该正常工作');
      console.log('\n💡 建议操作:');
      console.log('   1. 清除浏览器缓存');
      console.log('   2. 重启开发服务器');
      console.log('   3. 检查浏览器控制台是否有 JavaScript 错误');
    } else {
      console.log('❌ 发现以下问题:');
      issues.forEach((issue, index) => {
        console.log(`   ${index + 1}. ${issue}`);
      });
    }

  } catch (error) {
    console.error('❌ 诊断过程中发生错误:', error);
  } finally {
    await db.$disconnect();
  }
}

// 执行诊断
diagnoseUsPmnIssue()
  .then(() => {
    console.log('\n✨ 诊断完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 诊断失败:', error);
    process.exit(1);
  });
