#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 修复 us_class 数据库配置问题
 * 这个脚本会：
 * 1. 添加 us_class 到 DatabaseConfig 表
 * 2. 创建完整的字段配置到 FieldConfig 表
 * 3. 验证数据表是否存在和有数据
 */

async function fixUSClassConfig() {
  console.log('🚀 开始修复 us_class 数据库配置...');
  
  try {
    // 1. 添加数据库配置
    console.log('\n📋 添加 us_class 数据库配置...');
    
    const databaseConfig = await db.databaseConfig.upsert({
      where: { code: 'us_class' },
      update: {
        name: '美国器械分类',
        category: '参考数据',
        description: 'FDA医疗器械分类目录，包含产品代码、器械名称、分类信息等',
        accessLevel: 'free', // 设为免费访问
        tableName: 'us_class', // PostgreSQL表名
        modelName: 'uSClass', // Prisma模型名（注意大小写）
        isActive: true,
        sortOrder: 100, // 设置较大的排序值，放在列表后面
      },
      create: {
        code: 'us_class',
        name: '美国器械分类',
        category: '参考数据',
        description: 'FDA医疗器械分类目录，包含产品代码、器械名称、分类信息等',
        accessLevel: 'free',
        tableName: 'us_class',
        modelName: 'uSClass',
        isActive: true,
        sortOrder: 100,
      },
    });
    
    console.log(`  ✅ 数据库配置已创建/更新: ${databaseConfig.name}`);

    // 2. 创建字段配置
    console.log('\n🔧 创建 us_class 字段配置...');
    
    const fieldConfigs = [
      {
        fieldName: 'devicename',
        displayName: '器械名称',
        fieldType: 'text',
        isVisible: true,
        isSearchable: true,
        isFilterable: true,
        isSortable: true,
        sortOrder: 1,
        listOrder: 1,
        detailOrder: 1,
        searchType: 'contains',
        filterType: 'input',
      },
      {
        fieldName: 'productcode',
        displayName: '产品代码',
        fieldType: 'text',
        isVisible: true,
        isSearchable: true,
        isFilterable: true,
        isSortable: true,
        sortOrder: 2,
        listOrder: 2,
        detailOrder: 2,
        searchType: 'exact',
        filterType: 'input',
      },
      {
        fieldName: 'deviceclass',
        displayName: '器械类别',
        fieldType: 'select',
        isVisible: true,
        isSearchable: false,
        isFilterable: true,
        isSortable: true,
        sortOrder: 3,
        listOrder: 3,
        detailOrder: 3,
        searchType: 'exact',
        filterType: 'select',
      },
      {
        fieldName: 'medicalspecialty',
        displayName: '医学专科',
        fieldType: 'select',
        isVisible: true,
        isSearchable: false,
        isFilterable: true,
        isSortable: true,
        sortOrder: 4,
        listOrder: 4,
        detailOrder: 4,
        searchType: 'exact',
        filterType: 'select',
      },
      {
        fieldName: 'regulationnumber',
        displayName: '法规编号',
        fieldType: 'text',
        isVisible: true,
        isSearchable: true,
        isFilterable: false,
        isSortable: true,
        sortOrder: 5,
        listOrder: 5,
        detailOrder: 5,
        searchType: 'contains',
        filterType: 'input',
      },
      {
        fieldName: 'definition',
        displayName: '定义说明',
        fieldType: 'text',
        isVisible: false, // 仅在详情页显示
        isSearchable: true,
        isFilterable: false,
        isSortable: false,
        sortOrder: 6,
        listOrder: 0, // 不在列表中显示
        detailOrder: 6,
        searchType: 'contains',
        filterType: 'input',
      },
      {
        fieldName: 'review_panel',
        displayName: '审查小组',
        fieldType: 'select',
        isVisible: true,
        isSearchable: false,
        isFilterable: true,
        isSortable: false,
        sortOrder: 7,
        listOrder: 6,
        detailOrder: 7,
        searchType: 'exact',
        filterType: 'select',
      },
      {
        fieldName: 'gmpexemptflag',
        displayName: 'GMP豁免',
        fieldType: 'boolean',
        isVisible: false,
        isSearchable: false,
        isFilterable: true,
        isSortable: false,
        sortOrder: 8,
        listOrder: 0,
        detailOrder: 8,
        searchType: 'exact',
        filterType: 'checkbox',
      },
      {
        fieldName: 'thirdpartyflag',
        displayName: '第三方标志',
        fieldType: 'select', // 改为 select，因为实际数据是文本 "Y", "N"
        isVisible: false,
        isSearchable: false,
        isFilterable: true,
        isSortable: false,
        sortOrder: 9,
        listOrder: 0,
        detailOrder: 9,
        searchType: 'exact',
        filterType: 'multi_select', // 支持多选
      },
    ];

    for (const fieldConfig of fieldConfigs) {
      const result = await db.fieldConfig.upsert({
        where: {
          databaseCode_fieldName: {
            databaseCode: 'us_class',
            fieldName: fieldConfig.fieldName,
          },
        },
        update: {
          displayName: fieldConfig.displayName,
          fieldType: fieldConfig.fieldType as any,
          isVisible: fieldConfig.isVisible,
          isSearchable: fieldConfig.isSearchable,
          isFilterable: fieldConfig.isFilterable,
          isSortable: fieldConfig.isSortable,
          sortOrder: fieldConfig.sortOrder,
          listOrder: fieldConfig.listOrder,
          detailOrder: fieldConfig.detailOrder,
          searchType: fieldConfig.searchType as any,
          filterType: fieldConfig.filterType as any,
          isActive: true,
        },
        create: {
          databaseCode: 'us_class',
          fieldName: fieldConfig.fieldName,
          displayName: fieldConfig.displayName,
          fieldType: fieldConfig.fieldType as any,
          isVisible: fieldConfig.isVisible,
          isSearchable: fieldConfig.isSearchable,
          isFilterable: fieldConfig.isFilterable,
          isSortable: fieldConfig.isSortable,
          sortOrder: fieldConfig.sortOrder,
          listOrder: fieldConfig.listOrder,
          detailOrder: fieldConfig.detailOrder,
          searchType: fieldConfig.searchType as any,
          filterType: fieldConfig.filterType as any,
          isActive: true,
        },
      });
      
      console.log(`  ✅ 字段配置已创建/更新: ${fieldConfig.fieldName} (${fieldConfig.displayName})`);
    }

    // 3. 验证数据表和数据
    console.log('\n🔍 验证 us_class 数据表和数据...');
    
    try {
      // 检查表是否存在以及数据数量
      const count = await db.uSClass.count();
      console.log(`  📊 us_class 表数据量: ${count} 条记录`);
      
      if (count === 0) {
        console.log('  ⚠️  警告: us_class 表中没有数据，需要导入分类数据');
        console.log('  💡 建议: 运行数据导入脚本或联系数据管理员');
      } else {
        // 显示一些示例数据
        const sampleData = await db.uSClass.findMany({
          take: 3,
          select: {
            devicename: true,
            productcode: true,
            deviceclass: true,
            medicalspecialty: true,
          }
        });
        
        console.log('  📋 示例数据:');
        sampleData.forEach((item, index) => {
          console.log(`    ${index + 1}. ${item.devicename || 'N/A'} (${item.productcode || 'N/A'}) - 类别: ${item.deviceclass || 'N/A'}`);
        });
      }
      
    } catch (tableError) {
      console.error('  ❌ 数据表访问错误:', tableError);
      console.log('  💡 可能原因: Prisma 模型名称不匹配或表不存在');
    }

    // 4. 验证配置完整性
    console.log('\n🔍 验证配置完整性...');
    
    const allFieldConfigs = await db.fieldConfig.findMany({
      where: { databaseCode: 'us_class', isActive: true },
      orderBy: { sortOrder: 'asc' }
    });
    
    const visibleFields = allFieldConfigs.filter(f => f.isVisible);
    const searchableFields = allFieldConfigs.filter(f => f.isSearchable);
    const filterableFields = allFieldConfigs.filter(f => f.isFilterable);
    
    console.log(`  📊 配置统计:`);
    console.log(`    - 总字段数: ${allFieldConfigs.length}`);
    console.log(`    - 可见字段: ${visibleFields.length}`);
    console.log(`    - 可搜索字段: ${searchableFields.length}`);
    console.log(`    - 可筛选字段: ${filterableFields.length}`);

    console.log('\n🎉 us_class 数据库配置修复完成！');
    console.log('\n📋 后续操作建议：');
    console.log('  1. 重启应用或刷新配置缓存');
    console.log('  2. 访问 http://localhost:3000/data/list/us_class 测试');
    console.log('  3. 如果没有数据，需要导入FDA分类数据');
    console.log('  4. 根据实际使用情况调整字段配置');
    
  } catch (error) {
    console.error('❌ 修复过程中出现错误：', error);
    throw error;
  } finally {
    await db.$disconnect();
  }
}

// 执行修复
if (require.main === module) {
  fixUSClassConfig()
    .catch(error => {
      console.error('❌ 脚本执行失败：', error);
      process.exit(1);
    });
}

export { fixUSClassConfig }; 