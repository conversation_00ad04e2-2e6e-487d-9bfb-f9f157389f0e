#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 基于 us_class 创建标准化的数据库配置模板
 * 这个脚本展示了如何快速复制一个数据库的完整配置到新数据库
 */

interface DatabaseTemplate {
  code: string;
  name: string;
  category: string;
  description: string;
  accessLevel: 'free' | 'premium' | 'enterprise';
  tableName: string;
  modelName: string;
  defaultSort?: any[];
  // 字段配置会从源数据库复制
}

async function createDatabaseFromTemplate(
  sourceDatabase: string,
  template: DatabaseTemplate
) {
  console.log(`🚀 基于 ${sourceDatabase} 创建新数据库: ${template.code}`);
  
  try {
    // 1. 创建 DatabaseConfig
    console.log('\n📋 1. 创建数据库配置...');
    const dbConfig = await db.databaseConfig.upsert({
      where: { code: template.code },
      update: {
        name: template.name,
        category: template.category,
        description: template.description,
        accessLevel: template.accessLevel,
        tableName: template.tableName,
        modelName: template.modelName,
        defaultSort: template.defaultSort,
        isActive: true
      },
      create: {
        code: template.code,
        name: template.name,
        category: template.category,
        description: template.description,
        accessLevel: template.accessLevel,
        tableName: template.tableName,
        modelName: template.modelName,
        defaultSort: template.defaultSort,
        isActive: true
      }
    });
    
    console.log(`✅ 数据库配置已创建: ${dbConfig.name}`);
    
    // 2. 复制 FieldConfig
    console.log('\n🔧 2. 复制字段配置...');
    const sourceFields = await db.fieldConfig.findMany({
      where: {
        databaseCode: sourceDatabase,
        isActive: true
      },
      select: {
        fieldName: true,
        displayName: true,
        fieldType: true,
        isVisible: true,
        isSearchable: true,
        isFilterable: true,
        isSortable: true,
        sortOrder: true,
        listOrder: true,
        detailOrder: true,
        searchType: true,
        filterType: true,
        validationRules: true,
        options: true,
        todetail: true,
        isStatisticsEnabled: true,
        statisticsOrder: true,
        statisticsType: true,
        statisticsDisplayName: true,
        statisticsConfig: true,
        statisticsSortOrder: true,
        statisticsDefaultLimit: true,
        statisticsMaxLimit: true,
        isExportable: true,
        exportOrder: true,
        exportDisplayName: true
      }
    });
    
    console.log(`📊 找到 ${sourceFields.length} 个源字段配置`);
    
    // 批量创建字段配置
    for (const field of sourceFields) {
      await db.fieldConfig.upsert({
        where: {
          databaseCode_fieldName: {
            databaseCode: template.code,
            fieldName: field.fieldName
          }
        },
        update: {
          displayName: field.displayName,
          fieldType: field.fieldType,
          isVisible: field.isVisible,
          isSearchable: field.isSearchable,
          isFilterable: field.isFilterable,
          isSortable: field.isSortable,
          sortOrder: field.sortOrder,
          listOrder: field.listOrder,
          detailOrder: field.detailOrder,
          searchType: field.searchType,
          filterType: field.filterType,
          validationRules: field.validationRules as any,
          options: field.options as any,
          todetail: field.todetail,
          isStatisticsEnabled: field.isStatisticsEnabled,
          statisticsOrder: field.statisticsOrder,
          statisticsType: field.statisticsType,
          statisticsDisplayName: field.statisticsDisplayName,
          statisticsConfig: field.statisticsConfig as any,
          statisticsSortOrder: field.statisticsSortOrder,
          statisticsDefaultLimit: field.statisticsDefaultLimit,
          statisticsMaxLimit: field.statisticsMaxLimit,
          isExportable: field.isExportable,
          exportOrder: field.exportOrder,
          exportDisplayName: field.exportDisplayName,
          isActive: true
        },
        create: {
          databaseCode: template.code,
          fieldName: field.fieldName,
          displayName: field.displayName,
          fieldType: field.fieldType,
          isVisible: field.isVisible,
          isSearchable: field.isSearchable,
          isFilterable: field.isFilterable,
          isSortable: field.isSortable,
          sortOrder: field.sortOrder,
          listOrder: field.listOrder,
          detailOrder: field.detailOrder,
          searchType: field.searchType,
          filterType: field.filterType,
          validationRules: field.validationRules as any,
          options: field.options as any,
          todetail: field.todetail,
          isStatisticsEnabled: field.isStatisticsEnabled,
          statisticsOrder: field.statisticsOrder,
          statisticsType: field.statisticsType,
          statisticsDisplayName: field.statisticsDisplayName,
          statisticsConfig: field.statisticsConfig as any,
          statisticsSortOrder: field.statisticsSortOrder,
          statisticsDefaultLimit: field.statisticsDefaultLimit,
          statisticsMaxLimit: field.statisticsMaxLimit,
          isExportable: field.isExportable,
          exportOrder: field.exportOrder,
          exportDisplayName: field.exportDisplayName,
          isActive: true
        }
      });
    }
    
    console.log(`✅ 已复制 ${sourceFields.length} 个字段配置`);
    
    // 3. 验证配置
    console.log('\n🔍 3. 验证新数据库配置...');
    const newFieldCount = await db.fieldConfig.count({
      where: {
        databaseCode: template.code,
        isActive: true
      }
    });
    
    console.log(`✅ 新数据库字段配置数量: ${newFieldCount}`);
    console.log(`✅ 配置复制完成！`);
    
    console.log('\n🎯 使用说明:');
    console.log(`1. 确保 PostgreSQL 中存在表: ${template.tableName}`);
    console.log(`2. 确保 Prisma schema 中存在模型: ${template.modelName}`);
    console.log(`3. 访问 http://localhost:3001/data/list/${template.code} 查看效果`);
    
    return {
      success: true,
      databaseCode: template.code,
      fieldsCount: newFieldCount
    };
    
  } catch (error) {
    console.error('❌ 创建数据库模板失败:', error);
    throw error;
  }
}

// 示例：基于 us_class 创建新的数据库
async function main() {
  console.log('🎯 演示：基于现有数据库创建新数据库配置');
  
  // 示例1：创建欧盟器械分类数据库
  await createDatabaseFromTemplate('us_class', {
    code: 'eu_class',
    name: '欧盟器械分类',
    category: '参考数据',
    description: 'EU MDR医疗器械分类目录',
    accessLevel: 'free',
    tableName: 'eu_class',
    modelName: 'euClass',
    defaultSort: [
      { field: 'productcode', order: 'desc' },
      { field: 'id', order: 'asc' }
    ]
  });
  
  console.log('\n✅ 演示完成！');
  console.log('\n💡 关键优势:');
  console.log('  - 只需要配置 DatabaseConfig + FieldConfig');
  console.log('  - 所有功能自动可用：列表、筛选、搜索、导出、统计');
  console.log('  - 前端界面完全配置驱动，无需修改代码');
  console.log('  - API 路由完全通用，支持任意数据库');
  
  await db.$disconnect();
}

if (require.main === module) {
  main().catch(console.error);
}

export { createDatabaseFromTemplate };
