#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * 数据库模板迁移脚本
 * 
 * 功能：
 * 1. 将deviceCNImported转换为医疗器械模板数据库
 * 2. 删除deviceCNImported相关的实际医疗器械数据
 * 3. 将"中国器械"分类改名为"Regulation"
 * 4. 保留表结构和字段配置作为模板
 */

interface MigrationOptions {
  dryRun?: boolean;
  verbose?: boolean;
}

async function main() {
  const options: MigrationOptions = {
    dryRun: process.argv.includes('--dry-run'),
    verbose: process.argv.includes('--verbose')
  };

  console.log('🚀 开始数据库模板迁移...');
  console.log(`模式: ${options.dryRun ? '预览模式' : '执行模式'}`);
  console.log('');

  try {
    // 步骤1: 检查当前状态
    await checkCurrentState(options);
    
    // 步骤2: 备份相关数据（预览模式时跳过）
    if (!options.dryRun) {
      await backupData(options);
    }
    
    // 步骤3: 删除deviceCNImported相关的医疗器械数据
    await cleanupMedicalDeviceData(options);
    
    // 步骤4: 更新"中国器械"分类为"Regulation"
    await updateCategoryToRegulation(options);
    
    // 步骤5: 将deviceCNImported标记为模板数据库
    await convertToTemplate(options);
    
    // 步骤6: 验证迁移结果
    await verifyMigration(options);
    
    console.log('\n✅ 数据库模板迁移完成！');
    
  } catch (error) {
    console.error('\n❌ Error occurred during migration:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

async function checkCurrentState(options: MigrationOptions) {
  console.log('📊 Checking current database status...');
  
  // 检查DatabaseConfig表中的"中国器械"分类
  const chineseDeviceConfigs = await prisma.databaseConfig.findMany({
    where: { category: '中国器械' },
    select: { code: true, name: true, category: true, isActive: true }
  });
  
  console.log(`  - Found ${chineseDeviceConfigs.length} database configurations with "中国器械" category:`);
  chineseDeviceConfigs.forEach(config => {
    console.log(`    • ${config.code}: ${config.name} (Active: ${config.isActive})`);
  });
  
  // 检查deviceCNImported的医疗器械数据
  const deviceCount = await (prisma as any).medicalDevice.count({
    where: { database: 'deviceCNImported' }
  });
  
  console.log(`  - deviceCNImported数据库中有 ${deviceCount} 条医疗器械记录`);
  
  // 检查FieldConfig配置
  const fieldConfigCount = await prisma.fieldConfig.count({
    where: { databaseCode: 'deviceCNImported' }
  });
  
  console.log(`  - deviceCNImported有 ${fieldConfigCount} 个字段配置`);
  console.log('');
}

async function backupData(options: MigrationOptions) {
  console.log('💾 创建数据备份...');
  
  // 导出即将删除的医疗器械数据
  const deviceData = await (prisma as any).medicalDevice.findMany({
    where: { database: 'deviceCNImported' }
  });
  
  if (deviceData.length > 0) {
    const backupFile = `backup_deviceCNImported_${Date.now()}.json`;
    const fs = await import('fs/promises');
    await fs.writeFile(backupFile, JSON.stringify(deviceData, null, 2));
    console.log(`  ✅ 已备份 ${deviceData.length} 条记录到 ${backupFile}`);
  } else {
    console.log('  ℹ️ 没有需要备份的医疗器械数据');
  }
  console.log('');
}

async function cleanupMedicalDeviceData(options: MigrationOptions) {
  console.log('🗑️ 清理deviceCNImported的医疗器械数据...');
  
  if (options.dryRun) {
    const count = await (prisma as any).medicalDevice.count({
      where: { database: 'deviceCNImported' }
    });
    console.log(`  [预览] 将删除 ${count} 条deviceCNImported的医疗器械记录`);
  } else {
    const deleteResult = await (prisma as any).medicalDevice.deleteMany({
      where: { database: 'deviceCNImported' }
    });
    console.log(`  ✅ 已删除 ${deleteResult.count} 条deviceCNImported的医疗器械记录`);
  }
  console.log('');
}

async function updateCategoryToRegulation(options: MigrationOptions) {
  console.log('🏷️ 更新"中国器械"分类为"Regulation"...');
  
  if (options.dryRun) {
    const configs = await prisma.databaseConfig.findMany({
      where: { category: '中国器械' },
      select: { code: true, name: true }
    });
    console.log(`  [预览] 将更新以下数据库的分类:`);
    configs.forEach(config => {
      console.log(`    • ${config.code}: ${config.name}`);
    });
  } else {
    const updateResult = await prisma.databaseConfig.updateMany({
      where: { category: '中国器械' },
      data: { category: 'Regulation' }
    });
    console.log(`  ✅ 已更新 ${updateResult.count} 个数据库配置的分类为"Regulation"`);
  }
  console.log('');
}

async function convertToTemplate(options: MigrationOptions) {
  console.log('🎨 将deviceCNImported转换为模板数据库...');
  
  const templateConfig = {
    name: '医疗器械模板',
    description: '新建医疗器械数据库时的基础模板，包含完整的字段配置和表结构',
    isActive: false, // 不在前端显示
  };
  
  if (options.dryRun) {
    console.log('  [预览] deviceCNImported配置将更新为:');
    console.log(`    • 名称: ${templateConfig.name}`);
    console.log(`    • 描述: ${templateConfig.description}`);
    console.log(`    • 激活状态: ${templateConfig.isActive}`);
  } else {
    await prisma.databaseConfig.update({
      where: { code: 'deviceCNImported' },
      data: templateConfig
    });
    console.log('  ✅ deviceCNImported已转换为模板数据库');
  }
  console.log('');
}

async function verifyMigration(options: MigrationOptions) {
  console.log('🔍 验证迁移结果...');
  
  // 验证分类更新
  const regulationCount = await prisma.databaseConfig.count({
    where: { category: 'Regulation' }
  });
  console.log(`  ✅ "Regulation"分类下有 ${regulationCount} 个数据库`);
  
  const chineseDeviceCount = await prisma.databaseConfig.count({
    where: { category: '中国器械' }
  });
  console.log(`  ✅ "中国器械"分类下有 ${chineseDeviceCount} 个数据库 (应为0)`);
  
  // 验证数据清理
  const remainingDevices = await (prisma as any).medicalDevice.count({
    where: { database: 'deviceCNImported' }
  });
  console.log(`  ✅ deviceCNImported中剩余 ${remainingDevices} 条医疗器械记录 (应为0)`);
  
  // 验证模板配置
  const templateConfig = await prisma.databaseConfig.findUnique({
    where: { code: 'deviceCNImported' },
    select: { name: true, category: true, isActive: true }
  });
  
  if (templateConfig) {
    console.log(`  ✅ 模板配置: ${templateConfig.name} (分类: ${templateConfig.category}, 激活: ${templateConfig.isActive})`);
  }
  
  // 验证字段配置保留
  const fieldConfigCount = await prisma.fieldConfig.count({
    where: { databaseCode: 'deviceCNImported' }
  });
  console.log(`  ✅ 保留 ${fieldConfigCount} 个字段配置作为模板`);
  
  console.log('');
}

// 脚本使用说明
function printUsage() {
  console.log('📖 使用说明:');
  console.log('');
  console.log('预览模式 (不执行实际操作):');
  console.log('  npm run template-migration -- --dry-run');
  console.log('');
  console.log('执行迁移:');
  console.log('  npm run template-migration');
  console.log('');
  console.log('详细输出:');
  console.log('  npm run template-migration -- --verbose');
  console.log('');
}

// 检查命令行参数
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  printUsage();
  process.exit(0);
}

main(); 