#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 医药数据库设置脚本');
console.log('================================');

// 检查环境变量
function checkDatabaseUrl() {
  const envPath = path.join(__dirname, '../.env.local');

  if (!fs.existsSync(envPath)) {
    console.error('❌ 找不到 .env.local 文件');
    process.exit(1);
  }

  const envContent = fs.readFileSync(envPath, 'utf8');
  const dbUrlMatch = envContent.match(/^DATABASE_URL="([^"]*)"$/m);

  if (!dbUrlMatch || !dbUrlMatch[1] || dbUrlMatch[1].trim() === '') {
    console.log('⚠️  数据库URL未配置');
    console.log('');
    console.log('请按照以下步骤配置数据库：');
    console.log('1. 访问 https://neon.tech 创建免费PostgreSQL数据库');
    console.log('2. 复制数据库连接字符串');
    console.log('3. 在 .env.local 文件中设置 DATABASE_URL');
    console.log('4. 重新运行此脚本');
    console.log('');
    console.log('详细指南请查看: .same/database-setup.md');
    process.exit(1);
  }

  console.log('✅ 数据库URL已配置');
  return dbUrlMatch[1];
}

// 运行命令
function runCommand(command, description) {
  console.log(`\n🔄 ${description}...`);
  try {
    execSync(command, { stdio: 'inherit', cwd: path.join(__dirname, '..') });
    console.log(`✅ ${description}完成`);
  } catch (error) {
    console.error(`❌ ${description}失败:`, error.message);
    process.exit(1);
  }
}

// 主设置流程
async function setupDatabase() {
  try {
    // 1. 检查数据库URL
    const dbUrl = checkDatabaseUrl();

    // 2. 生成迁移文件（如果需要）
    console.log('\n📝 检查迁移文件...');
    const migrationExists = fs.existsSync(path.join(__dirname, '../drizzle/0000_gigantic_thunderbolts.sql'));

    if (!migrationExists) {
      runCommand('bun run db:generate', '生成数据库迁移文件');
    } else {
      console.log('✅ 迁移文件已存在');
    }

    // 3. 执行迁移
    runCommand('bun run db:migrate', '执行数据库迁移');

    // 4. 插入种子数据
    runCommand('bun run db:seed', '插入种子数据');

    // 5. 完成提示
    console.log('\n🎉 数据库设置完成！');
    console.log('');
    console.log('📊 数据库包含:');
    console.log('- medical_devices 表：医疗器械数据');
    console.log('- database_configs 表：数据库配置');
    console.log('');
    console.log('🚀 现在可以重启开发服务器测试:');
    console.log('   bun run dev');
    console.log('');
    console.log('🔧 可用命令:');
    console.log('   bun run db:studio  - 打开数据库管理界面');
    console.log('   bun run db:seed    - 重新插入种子数据');

  } catch (error) {
    console.error('\n❌ 设置过程中出现错误:', error.message);
    process.exit(1);
  }
}

// 运行设置
setupDatabase();
