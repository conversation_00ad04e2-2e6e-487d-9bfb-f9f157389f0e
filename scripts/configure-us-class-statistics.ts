#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 为 us_class 数据库配置统计功能
 * 这个脚本会更新现有的 fieldConfig 记录，添加统计配置
 */

async function configureUSClassStatistics() {
  console.log('📊 配置 us_class 数据库统计功能...');

  try {
    // 统计配置定义 - 包含排序配置
    const statisticsConfigs = [
      {
        fieldName: 'deviceclass',
        isStatisticsEnabled: true,
        statisticsOrder: 1,
        statisticsType: 'group_by',
        statisticsDisplayName: '器械类别分布',
        statisticsSortOrder: 'desc', // 倒序 - 数量从大到小
        statisticsConfig: {
          limit: 10,
          description: '按器械类别统计数量分布，按数量倒序排列'
        }
      },
      {
        fieldName: 'medicalspecialty',
        isStatisticsEnabled: true,
        statisticsOrder: 2,
        statisticsType: 'group_by',
        statisticsDisplayName: '医学专科分布',
        statisticsSortOrder: 'desc', // 倒序 - 数量从大到小
        statisticsConfig: {
          limit: 8,
          description: '按医学专科统计数量分布，按数量倒序排列'
        }
      },
      {
        fieldName: 'productcode',
        isStatisticsEnabled: true,
        statisticsOrder: 3,
        statisticsType: 'group_by', // 改为 group_by 以便看到分布
        statisticsDisplayName: '产品代码统计',
        statisticsSortOrder: 'asc', // 正序 - 按字母顺序
        statisticsConfig: {
          limit: 100, // 增加到100以支持更多产品代码展示
          description: '按产品代码统计数量分布，按代码正序排列'
        }
      },
      {
        fieldName: 'regulationnumber',
        isStatisticsEnabled: true,
        statisticsOrder: 4,
        statisticsType: 'group_by', // 改为 group_by 以便看到分布
        statisticsDisplayName: '法规编号统计',
        statisticsSortOrder: 'desc', // 倒序 - 数量从大到小
        statisticsConfig: {
          limit: 50, // 增加到50以支持更多法规编号展示
          description: '按法规编号统计数量分布，按数量倒序排列'
        }
      }
    ];

    console.log(`\n🔧 更新 ${statisticsConfigs.length} 个字段的统计配置...`);

    for (const config of statisticsConfigs) {
      try {
        const result = await db.fieldConfig.updateMany({
          where: {
            databaseCode: 'us_class',
            fieldName: config.fieldName,
            isActive: true,
          },
          data: {
            isStatisticsEnabled: config.isStatisticsEnabled,
            statisticsOrder: config.statisticsOrder,
            statisticsType: config.statisticsType as any,
            statisticsDisplayName: config.statisticsDisplayName,
            statisticsSortOrder: (config as any).statisticsSortOrder,
            statisticsConfig: config.statisticsConfig,
          },
        });

        if (result.count > 0) {
          console.log(`  ✅ ${config.fieldName} (${config.statisticsDisplayName}) - 已更新`);
        } else {
          console.log(`  ⚠️  ${config.fieldName} - 未找到匹配的字段配置`);
        }
      } catch (error) {
        console.error(`  ❌ ${config.fieldName} - 更新失败:`, error);
      }
    }

    // 验证配置结果
    console.log('\n🔍 验证统计配置...');
    const enabledStats = await db.fieldConfig.findMany({
      where: {
        databaseCode: 'us_class',
        isStatisticsEnabled: true,
        isActive: true,
      },
      select: {
        fieldName: true,
        displayName: true,
        statisticsDisplayName: true,
        statisticsType: true,
        statisticsOrder: true,
        statisticsSortOrder: true,
        statisticsConfig: true,
      },
      orderBy: { statisticsOrder: 'asc' },
    });

    console.log(`\n📋 已启用统计的字段 (${enabledStats.length} 个):`);
    enabledStats.forEach((stat, index) => {
      const sortIcon = (stat as any).statisticsSortOrder === 'desc' ? '📉' : '📈';
      console.log(`\n   ${index + 1}. 字段: ${stat.fieldName}`);
      console.log(`      显示名: ${stat.displayName}`);
      console.log(`      统计名: ${stat.statisticsDisplayName}`);
      console.log(`      统计类型: ${stat.statisticsType}`);
      console.log(`      排序: ${stat.statisticsOrder}`);
      console.log(`      排序方向: ${(stat as any).statisticsSortOrder} ${sortIcon}`);
      console.log(`      配置: ${JSON.stringify(stat.statisticsConfig, null, 2)}`);
    });

    // 检查是否有其他数据库可以参考这个配置
    console.log('\n💡 配置完成提示:');
    console.log('   1. 统计配置已保存到 fieldConfig 表中');
    console.log('   2. 可以通过 /api/stats/us_class/configurable 访问新的统计API');
    console.log('   3. 前端可以使用 ConfigurableStatsPanel 组件显示统计');
    console.log('   4. 其他数据库可以参考这个配置模式');

  } catch (error) {
    console.error('❌ 配置统计功能失败:', error);
    throw error;
  } finally {
    await db.$disconnect();
  }
}

// 执行配置
configureUSClassStatistics()
  .then(() => {
    console.log('\n✨ us_class 统计配置完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 配置失败:', error);
    process.exit(1);
  });
