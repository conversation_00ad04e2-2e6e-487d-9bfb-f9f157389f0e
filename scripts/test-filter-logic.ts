#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

async function testFilterLogic() {
  console.log('🔍 测试筛选器联动逻辑...');
  
  try {
    // 1. 测试场景：选择 country_code = 'PH' 后，expeditedreview 的计数
    console.log('\n📊 1. 测试场景分析...');
    console.log('用户操作：选择 Country Code = PH');
    console.log('期望：expeditedreview 的计数应该基于已筛选的 PH 数据');
    
    // 2. 验证数据
    console.log('\n🔍 2. 验证实际数据...');
    
    // 总的 PH 数据
    const totalPH = await db.uSPremarketNotification.count({
      where: { country_code: 'PH' }
    });
    console.log(`PH 总记录数: ${totalPH}`);
    
    // PH 数据中的 expeditedreview 分布
    const phExpedited = await db.uSPremarketNotification.groupBy({
      by: ['expeditedreview'],
      where: { country_code: 'PH' },
      _count: { expeditedreview: true },
      orderBy: { _count: { expeditedreview: 'desc' } }
    });
    
    console.log('PH 数据中的 expeditedreview 分布:');
    phExpedited.forEach(item => {
      const value = item.expeditedreview || 'N/A';
      console.log(`  ${value}: ${item._count.expeditedreview} 条`);
    });
    
    // 3. 测试 dynamic-counts API
    console.log('\n🌐 3. 测试 dynamic-counts API...');
    
    try {
      // 模拟前端请求：选择了 country_code = PH，查询 expeditedreview 的动态计数
      const filters = { country_code: ['PH'] };
      const params = new URLSearchParams({
        field: 'expeditedreview',
        filters: JSON.stringify(filters)
      });
      
      const response = await fetch(`http://localhost:3001/api/meta/us_pmn/dynamic-counts?${params}`);
      const result = await response.json();
      
      if (result.success) {
        console.log('✅ Dynamic-counts API 返回结果:');
        result.data.forEach((item: any) => {
          console.log(`  ${item.value}: ${item.count} 条`);
        });
        
        // 验证总数是否匹配
        const apiTotal = result.data.reduce((sum: number, item: any) => sum + item.count, 0);
        console.log(`API 返回总数: ${apiTotal}`);
        console.log(`实际 PH 总数: ${totalPH}`);
        
        if (apiTotal === totalPH) {
          console.log('✅ 计数匹配正确');
        } else {
          console.log('❌ 计数不匹配！');
        }
      } else {
        console.log('❌ Dynamic-counts API 失败:', result.error);
      }
    } catch (error) {
      console.log('❌ API 请求失败:', (error as Error).message);
    }
    
    // 4. 检查前端逻辑问题
    console.log('\n🔧 4. 分析前端逻辑问题...');
    
    // 检查 us_pmn 的筛选字段配置
    const usPmnFilters = await db.fieldConfig.findMany({
      where: {
        databaseCode: 'us_pmn',
        isFilterable: true,
        isActive: true
      },
      select: {
        fieldName: true,
        displayName: true,
        filterType: true
      },
      orderBy: { fieldName: 'asc' }
    });
    
    console.log('us_pmn 筛选字段配置:');
    usPmnFilters.forEach(field => {
      console.log(`  ${field.fieldName} (${field.displayName}): ${field.filterType}`);
    });
    
    // 5. 对比 us_class 的逻辑
    console.log('\n📋 5. 对比 us_class 的筛选逻辑...');
    
    try {
      // 测试 us_class 的动态计数
      const classFilters = { deviceclass: ['1'] }; // 假设选择 Class I
      const classParams = new URLSearchParams({
        field: 'medicalspecialty',
        filters: JSON.stringify(classFilters)
      });
      
      const classResponse = await fetch(`http://localhost:3001/api/meta/us_class/dynamic-counts?${classParams}`);
      const classResult = await classResponse.json();
      
      if (classResult.success) {
        console.log('✅ us_class dynamic-counts 正常工作');
        console.log(`返回选项数: ${classResult.data?.length || 0}`);
      } else {
        console.log('❌ us_class dynamic-counts 失败:', classResult.error);
      }
    } catch (error) {
      console.log('⚠️  us_class 测试跳过:', (error as Error).message);
    }
    
    // 6. 问题分析
    console.log('\n🎯 6. 问题分析...');
    
    console.log('可能的问题原因:');
    console.log('1. ❓ select 类型筛选器没有联动更新 (只有 multi_select 有联动)');
    console.log('2. ❓ dynamic-counts API 的筛选条件构建有问题');
    console.log('3. ❓ 前端 useEffect 只监听 multi_select 类型字段');
    
    // 检查前端逻辑
    console.log('\n🔍 前端逻辑检查:');
    console.log('当前 useEffect 只更新 multi_select 类型字段的动态计数:');
    console.log('  const filterableFields = config.fields.filter((f: any) => f.isFilterable && f.filterType === "multi_select");');
    console.log('');
    console.log('但是 expeditedreview 是 select 类型，不是 multi_select 类型！');
    console.log('所以当 country_code (multi_select) 改变时，expeditedreview (select) 的计数不会更新。');
    
    console.log('\n💡 解决方案:');
    console.log('需要修改前端逻辑，让所有筛选器类型都支持联动更新，不只是 multi_select。');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await db.$disconnect();
  }
}

testFilterLogic().catch(console.error);
