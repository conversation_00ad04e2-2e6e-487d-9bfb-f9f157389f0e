#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

async function debugMultiSelectAndLinkage() {
  console.log('🔍 调试 multi_select 显示和联动问题...');
  
  try {
    // 1. 检查 expeditedreview 字段配置
    console.log('\n📋 1. 检查 expeditedreview 字段配置...');
    
    const expeditedConfig = await db.fieldConfig.findFirst({
      where: {
        databaseCode: 'us_pmn',
        fieldName: 'expeditedreview',
        isActive: true
      },
      select: {
        fieldName: true,
        displayName: true,
        fieldType: true,
        filterType: true,
        isFilterable: true,
        isVisible: true,
        listOrder: true
      }
    });
    
    if (expeditedConfig) {
      console.log('✅ expeditedreview 字段配置:');
      console.log(`   字段名: ${expeditedConfig.fieldName}`);
      console.log(`   显示名: ${expeditedConfig.displayName}`);
      console.log(`   字段类型: ${expeditedConfig.fieldType}`);
      console.log(`   筛选类型: ${expeditedConfig.filterType}`);
      console.log(`   可筛选: ${expeditedConfig.isFilterable}`);
      console.log(`   可见: ${expeditedConfig.isVisible}`);
      console.log(`   列表顺序: ${expeditedConfig.listOrder}`);
      
      if (expeditedConfig.filterType === 'multi_select' && expeditedConfig.isFilterable) {
        console.log('✅ 配置正确：multi_select 且可筛选');
      } else {
        console.log('❌ 配置问题：');
        console.log(`   期望: filterType = 'multi_select', isFilterable = true`);
        console.log(`   实际: filterType = '${expeditedConfig.filterType}', isFilterable = ${expeditedConfig.isFilterable}`);
      }
    } else {
      console.log('❌ 未找到 expeditedreview 字段配置');
    }
    
    // 2. 检查 expeditedreview 数据
    console.log('\n📊 2. 检查 expeditedreview 数据...');
    
    const expeditedStats = await db.uSPremarketNotification.groupBy({
      by: ['expeditedreview'],
      _count: { expeditedreview: true },
      orderBy: { _count: { expeditedreview: 'desc' } }
    });
    
    console.log('expeditedreview 数据分布:');
    expeditedStats.forEach((item, index) => {
      const value = item.expeditedreview || 'N/A';
      console.log(`  ${index + 1}. ${value}: ${item._count.expeditedreview} 条`);
    });
    
    // 3. 测试联动数据
    console.log('\n🔗 3. 测试联动数据...');
    
    // 测试场景：reviewadvisecomm = 'PA'
    const paExpedited = await db.uSPremarketNotification.groupBy({
      by: ['expeditedreview'],
      where: { reviewadvisecomm: 'PA' },
      _count: { expeditedreview: true },
      orderBy: { _count: { expeditedreview: 'desc' } }
    });
    
    console.log('当 reviewadvisecomm = "PA" 时，expeditedreview 分布:');
    paExpedited.forEach((item, index) => {
      const value = item.expeditedreview || 'N/A';
      console.log(`  ${index + 1}. ${value}: ${item._count.expeditedreview} 条`);
    });
    
    // 4. 测试 API 联动
    console.log('\n🌐 4. 测试 API 联动...');
    
    try {
      const filters = { reviewadvisecomm: ['PA'] };
      const params = new URLSearchParams({
        field: 'expeditedreview',
        filters: JSON.stringify(filters)
      });
      
      const response = await fetch(`http://localhost:3001/api/meta/us_pmn/dynamic-counts?${params}`);
      const result = await response.json();
      
      if (result.success) {
        console.log('✅ API 联动测试成功:');
        result.data.forEach((item: any, index: number) => {
          console.log(`  ${index + 1}. ${item.value}: ${item.count} 条`);
        });
        
        // 验证数据一致性
        const apiTotal = result.data.reduce((sum: number, item: any) => sum + item.count, 0);
        const dbTotal = paExpedited.reduce((sum, item) => sum + item._count.expeditedreview, 0);
        
        console.log(`API 总计: ${apiTotal}, 数据库总计: ${dbTotal}`);
        if (apiTotal === dbTotal) {
          console.log('✅ API 数据与数据库一致');
        } else {
          console.log('❌ API 数据与数据库不一致');
        }
      } else {
        console.log('❌ API 联动测试失败:', result.error);
      }
    } catch (error) {
      console.log('❌ API 请求失败:', (error as Error).message);
    }
    
    // 5. 检查前端渲染条件
    console.log('\n🎨 5. 检查前端渲染条件...');
    
    console.log('multi_select 筛选器显示的条件:');
    console.log('1. fieldConfig.isFilterable === true');
    console.log('2. fieldConfig.filterType === "multi_select"');
    console.log('3. mergedOptions.length > 0 (有选项数据)');
    console.log('4. 字段在 filterableFields 列表中');
    
    // 检查所有 multi_select 字段
    const multiSelectFields = await db.fieldConfig.findMany({
      where: {
        databaseCode: 'us_pmn',
        filterType: 'multi_select',
        isFilterable: true,
        isActive: true
      },
      select: {
        fieldName: true,
        displayName: true,
        listOrder: true
      },
      orderBy: { listOrder: 'asc' }
    });
    
    console.log(`\nus_pmn 所有 multi_select 字段 (${multiSelectFields.length} 个):`);
    multiSelectFields.forEach((field, index) => {
      console.log(`  ${index + 1}. ${field.fieldName} (${field.displayName}) - 顺序: ${field.listOrder}`);
    });
    
    // 6. 诊断可能的问题
    console.log('\n🔍 6. 诊断可能的问题...');
    
    console.log('multi_select 不显示的可能原因:');
    console.log('1. ❓ 字段配置未生效 (缓存问题)');
    console.log('2. ❓ 前端组件渲染逻辑问题');
    console.log('3. ❓ mergedOptions 为空');
    console.log('4. ❓ 字段顺序问题');
    console.log('5. ❓ 浏览器缓存问题');
    
    console.log('\n联动数据不正确的可能原因:');
    console.log('1. ❓ 防抖函数仍有问题');
    console.log('2. ❓ filtersRef 未正确更新');
    console.log('3. ❓ API 调用时机问题');
    console.log('4. ❓ 前端状态更新延迟');
    console.log('5. ❓ 缓存干扰');
    
    // 7. 建议的解决步骤
    console.log('\n🔧 7. 建议的解决步骤...');
    
    console.log('解决 multi_select 不显示:');
    console.log('1. 刷新浏览器页面 (清除前端缓存)');
    console.log('2. 检查浏览器控制台是否有错误');
    console.log('3. 检查 Network 标签，确认 meta API 返回正确数据');
    console.log('4. 检查字段是否在筛选面板的正确位置');
    
    console.log('\n解决联动数据问题:');
    console.log('1. 打开浏览器开发者工具');
    console.log('2. 在 Console 中输入: localStorage.clear()');
    console.log('3. 刷新页面');
    console.log('4. 逐步测试筛选器联动');
    console.log('5. 观察 Network 请求的参数和响应');
    
  } catch (error) {
    console.error('❌ 调试失败:', error);
  } finally {
    await db.$disconnect();
  }
}

debugMultiSelectAndLinkage().catch(console.error);
