#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 为 FieldConfig 表添加统计显示配置字段
 * 不会清空现有数据，只是添加新字段
 */

async function addStatisticsDisplayConfig() {
  console.log('🔧 开始添加统计显示配置字段...');

  try {
    // 1. 检查字段是否已存在
    console.log('🔍 检查现有字段...');
    const existingColumns = await db.$queryRaw`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'FieldConfig' 
      AND column_name IN (
        'statisticsDefaultLimit',
        'statisticsMaxLimit'
      )
    ` as any[];

    const existingColumnNames = existingColumns.map((col: any) => col.column_name);
    console.log('   已存在的显示配置字段:', existingColumnNames);

    // 2. 添加统计显示配置字段
    if (!existingColumnNames.includes('statisticsDefaultLimit')) {
      console.log('🔧 添加统计显示配置字段...');
      await db.$executeRaw`
        ALTER TABLE "FieldConfig" 
        ADD COLUMN "statisticsDefaultLimit" INTEGER NOT NULL DEFAULT 5,
        ADD COLUMN "statisticsMaxLimit" INTEGER NOT NULL DEFAULT 50;
      `;
      
      console.log('✅ 统计显示配置字段添加成功');
    } else {
      console.log('✅ 统计显示配置字段已存在');
    }

    // 3. 添加字段注释
    console.log('🔧 添加字段注释...');
    await db.$executeRaw`
      COMMENT ON COLUMN "FieldConfig"."statisticsDefaultLimit" IS '统计默认显示数量（收起状态）';
    `;
    await db.$executeRaw`
      COMMENT ON COLUMN "FieldConfig"."statisticsMaxLimit" IS '统计最大显示数量（展开状态）';
    `;

    // 4. 创建索引
    console.log('🔧 创建索引...');
    await db.$executeRaw`
      CREATE INDEX IF NOT EXISTS "FieldConfig_statisticsDefaultLimit_idx" ON "FieldConfig"("statisticsDefaultLimit");
    `;

    // 5. 验证字段添加结果
    console.log('🔍 验证字段添加结果...');
    const verifyResult = await db.$queryRaw`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'FieldConfig' 
      AND column_name IN (
        'statisticsDefaultLimit',
        'statisticsMaxLimit'
      )
      ORDER BY column_name
    ` as any[];

    console.log('\n📋 新增的统计显示配置字段:');
    verifyResult.forEach((col: any) => {
      console.log(`   ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable}, default: ${col.column_default})`);
    });

    console.log('\n✅ 统计显示配置字段添加完成！');
    console.log('\n📝 新增功能:');
    console.log('   - statisticsDefaultLimit: 控制默认显示数量（收起状态）');
    console.log('   - statisticsMaxLimit: 控制最大显示数量（展开状态）');
    console.log('   - 默认值: defaultLimit=5, maxLimit=50');

  } catch (error) {
    console.error('❌ 添加统计显示配置字段失败:', error);
    throw error;
  } finally {
    await db.$disconnect();
  }
}

// 执行脚本
if (require.main === module) {
  addStatisticsDisplayConfig()
    .then(() => {
      console.log('\n🎉 脚本执行完成！');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 脚本执行失败:', error);
      process.exit(1);
    });
}

export { addStatisticsDisplayConfig };
