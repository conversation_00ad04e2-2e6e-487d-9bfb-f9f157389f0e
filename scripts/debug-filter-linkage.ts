#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

async function debugFilterLinkage() {
  console.log('🔍 调试筛选器联动问题...');
  
  try {
    // 1. 验证测试数据
    console.log('\n📊 1. 验证测试数据...');
    
    // 检查 reviewadvisecomm = 'PA' 的数据
    const paTotal = await db.uSPremarketNotification.count({
      where: { reviewadvisecomm: 'PA' }
    });
    console.log(`reviewadvisecomm = 'PA' 总记录数: ${paTotal}`);
    
    // 检查 PA 数据中的 country_code 分布
    const paCountryStats = await db.uSPremarketNotification.groupBy({
      by: ['country_code'],
      where: { reviewadvisecomm: 'PA' },
      _count: { country_code: true },
      orderBy: { _count: { country_code: 'desc' } }
    });
    
    console.log('PA 数据中的 country_code 分布 (前10个):');
    paCountryStats.slice(0, 10).forEach((item, index) => {
      const value = item.country_code || 'N/A';
      console.log(`  ${index + 1}. ${value}: ${item._count.country_code} 条`);
    });
    
    // 检查同时满足两个条件的数据
    const bothConditions = await db.uSPremarketNotification.count({
      where: {
        reviewadvisecomm: 'PA',
        country_code: 'US'
      }
    });
    console.log(`同时满足 reviewadvisecomm='PA' 和 country_code='US' 的记录数: ${bothConditions}`);
    
    // 2. 测试 dynamic-counts API
    console.log('\n🌐 2. 测试 dynamic-counts API...');
    
    try {
      // 测试场景：选择了 reviewadvisecomm = 'PA'，查询 country_code 的动态计数
      const filters = { reviewadvisecomm: ['PA'] };
      const params = new URLSearchParams({
        field: 'country_code',
        filters: JSON.stringify(filters)
      });
      
      const response = await fetch(`http://localhost:3001/api/meta/us_pmn/dynamic-counts?${params}`);
      const result = await response.json();
      
      if (result.success) {
        console.log('✅ API 返回结果 (前10个):');
        result.data.slice(0, 10).forEach((item: any, index: number) => {
          console.log(`  ${index + 1}. ${item.value}: ${item.count} 条`);
        });
        
        // 检查 US 的计数
        const usCount = result.data.find((item: any) => item.value === 'US');
        if (usCount) {
          console.log(`✅ API 中 US 的计数: ${usCount.count}`);
          console.log(`✅ 数据库中的计数: ${bothConditions}`);
          
          if (usCount.count === bothConditions) {
            console.log('✅ API 计数与数据库一致');
          } else {
            console.log('❌ API 计数与数据库不一致！');
          }
        } else {
          console.log('❌ API 结果中没有找到 US');
        }
      } else {
        console.log('❌ API 失败:', result.error);
      }
    } catch (error) {
      console.log('❌ API 请求失败:', (error as Error).message);
    }
    
    // 3. 分析可能的问题
    console.log('\n🔍 3. 分析可能的问题...');
    
    console.log('可能的问题源:');
    console.log('1. ❓ 防抖延迟导致用户看到的是旧数据');
    console.log('2. ❓ 前端状态更新时机问题');
    console.log('3. ❓ API 调用顺序问题');
    console.log('4. ❓ 缓存问题');
    console.log('5. ❓ 筛选条件构建错误');
    
    // 4. 检查字段配置
    console.log('\n📋 4. 检查字段配置...');
    
    const fieldConfigs = await db.fieldConfig.findMany({
      where: {
        databaseCode: 'us_pmn',
        fieldName: { in: ['reviewadvisecomm', 'country_code'] },
        isActive: true
      },
      select: {
        fieldName: true,
        displayName: true,
        filterType: true,
        isFilterable: true
      }
    });
    
    fieldConfigs.forEach(field => {
      console.log(`${field.fieldName} (${field.displayName}):`);
      console.log(`  筛选类型: ${field.filterType}`);
      console.log(`  可筛选: ${field.isFilterable}`);
    });
    
    // 5. 模拟前端逻辑
    console.log('\n🔧 5. 模拟前端逻辑...');
    
    console.log('用户操作序列:');
    console.log('1. 选择 reviewadvisecomm = "PA"');
    console.log('2. 触发防抖函数更新 country_code 计数');
    console.log('3. 300ms 后 API 调用');
    console.log('4. 用户看到 country_code 的新计数');
    console.log('5. 选择 country_code = "US"');
    console.log('6. 触发防抖函数更新其他字段计数');
    
    console.log('\n可能的时序问题:');
    console.log('- 用户操作太快，防抖还没完成就进行下一步操作');
    console.log('- 前端显示的是缓存的旧数据');
    console.log('- API 调用被防抖延迟，用户看到的不是最新数据');
    
    // 6. 建议的调试步骤
    console.log('\n🧪 6. 建议的调试步骤...');
    
    console.log('请按以下步骤调试:');
    console.log('1. 打开浏览器开发者工具 (F12)');
    console.log('2. 切换到 Network 标签');
    console.log('3. 访问 us_pmn 页面');
    console.log('4. 选择 reviewadvisecomm = "PA"');
    console.log('5. 等待 300ms，观察 API 请求');
    console.log('6. 检查 country_code 的计数是否更新');
    console.log('7. 选择 country_code = "US"');
    console.log('8. 观察是否有新的 API 请求');
    console.log('9. 检查其他字段的计数是否更新');
    
    console.log('\n🎯 预期行为:');
    console.log('✅ 选择 reviewadvisecomm = "PA" 后');
    console.log('   → 300ms 后发出 API 请求');
    console.log('   → country_code 显示基于 PA 数据的计数');
    console.log('   → US 应该显示实际的交集数量');
    console.log('');
    console.log('✅ 选择 country_code = "US" 后');
    console.log('   → 300ms 后发出 API 请求');
    console.log('   → 其他字段显示基于 PA+US 数据的计数');
    
  } catch (error) {
    console.error('❌ 调试失败:', error);
  } finally {
    await db.$disconnect();
  }
}

debugFilterLinkage().catch(console.error);
