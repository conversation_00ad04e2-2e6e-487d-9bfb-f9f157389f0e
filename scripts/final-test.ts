#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

async function finalTest() {
  console.log('🎯 最终测试：筛选器联动修复验证');
  
  try {
    // 1. 验证API工作正常
    console.log('\n📊 1. 验证API工作正常...');
    
    const initialResponse = await fetch('http://localhost:3001/api/meta/us_class/dynamic-counts?field=thirdpartyflag&filters=%7B%7D');
    const initialResult = await initialResponse.json();
    
    const filteredResponse = await fetch('http://localhost:3001/api/meta/us_class/dynamic-counts?field=thirdpartyflag&filters=%7B%22deviceclass%22%3A%5B%222%22%5D%7D');
    const filteredResult = await filteredResponse.json();
    
    if (initialResult.success && filteredResult.success) {
      console.log('✅ API工作正常');
      
      const initialTotal = initialResult.data.reduce((sum: number, item: any) => sum + item.count, 0);
      const filteredTotal = filteredResult.data.reduce((sum: number, item: any) => sum + item.count, 0);
      
      console.log(`初始状态: ${initialTotal} 条`);
      console.log(`筛选后: ${filteredTotal} 条`);
      console.log(`联动正确: ${filteredTotal === 3567 ? '✅' : '❌'}`);
    } else {
      console.log('❌ API有问题');
      return;
    }
    
    // 2. 验证数据库数据
    console.log('\n📊 2. 验证数据库数据...');
    
    const totalRecords = await db.uSClass.count();
    const class2Records = await db.uSClass.count({ where: { deviceclass: '2' } });
    
    console.log(`总记录数: ${totalRecords}`);
    console.log(`器械类别=2: ${class2Records}`);
    console.log(`数据一致: ${class2Records === 3567 ? '✅' : '❌'}`);
    
    // 3. 验证第三方标志分布
    console.log('\n📊 3. 验证第三方标志分布...');
    
    const allThirdParty = await db.uSClass.groupBy({
      by: ['thirdpartyflag'],
      _count: { thirdpartyflag: true },
      orderBy: { _count: { thirdpartyflag: 'desc' } }
    });
    
    const class2ThirdParty = await db.uSClass.groupBy({
      by: ['thirdpartyflag'],
      where: { deviceclass: '2' },
      _count: { thirdpartyflag: true },
      orderBy: { _count: { thirdpartyflag: 'desc' } }
    });
    
    console.log('全库第三方标志分布:');
    allThirdParty.forEach(item => {
      const value = item.thirdpartyflag || 'N/A';
      console.log(`  ${value}: ${item._count.thirdpartyflag} 条`);
    });
    
    console.log('器械类别=2的第三方标志分布:');
    class2ThirdParty.forEach(item => {
      const value = item.thirdpartyflag || 'N/A';
      console.log(`  ${value}: ${item._count.thirdpartyflag} 条`);
    });
    
    // 4. 修复总结
    console.log('\n🔧 4. 修复总结...');
    
    console.log('已完成的修复:');
    console.log('✅ 1. 移除了 useEffect 中的 Object.keys(filters).length > 0 限制');
    console.log('✅ 2. 添加了页面初始化时的动态计数获取');
    console.log('✅ 3. 修复了用静态计数初始化动态计数的问题');
    console.log('✅ 4. 添加了详细的调试日志');
    
    console.log('\n预期效果:');
    console.log('- 页面初始加载时，筛选器显示全库动态计数');
    console.log('- 选择筛选条件后，其他筛选器显示基于当前筛选条件的动态计数');
    console.log('- 计数数字实时反映当前筛选状态下的数据分布');
    
    // 5. 测试指南
    console.log('\n📋 5. 测试指南...');
    
    console.log('请按以下步骤测试:');
    console.log('1. 访问: http://localhost:3001/data/list/us_class');
    console.log('2. 打开浏览器开发者工具，查看Console标签');
    console.log('3. 观察初始状态下"第三方标志"的计数');
    console.log('4. 选择"器械类别"为"2"');
    console.log('5. 观察"第三方标志"的计数是否更新为 N(2141), Y(1426)');
    console.log('6. 继续选择其他筛选条件，验证联动效果');
    
    console.log('\n调试信息:');
    console.log('- 查找 "🚀 Initializing dynamic counts" 日志');
    console.log('- 查找 "✅ Dynamic counts updated" 日志');
    console.log('- 查找 "🔍 Field thirdpartyflag" 日志');
    
    // 6. 可能的问题
    console.log('\n⚠️ 6. 如果仍有问题...');
    
    console.log('可能的原因:');
    console.log('1. 浏览器缓存: 尝试硬刷新 (Ctrl+Shift+R)');
    console.log('2. React状态: 检查是否有其他地方重置了dynamicCounts');
    console.log('3. 网络请求: 检查Network标签是否有dynamic-counts请求');
    console.log('4. 权限问题: 确保用户有访问权限');
    
    console.log('\n🎯 修复完成！请测试验证效果。');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  } finally {
    await db.$disconnect();
  }
}

// 运行最终测试
finalTest().catch(console.error);
