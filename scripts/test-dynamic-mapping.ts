#!/usr/bin/env tsx

import { DynamicTableMappingService } from '../src/lib/dynamicTableMappingService';

async function testDynamicMapping() {
  console.log('🧪 测试动态表映射服务...\n');

  // 测试有效的数据库代码
  const testCodes = ['deviceCNImported', 'us_pmn', 'nonexistent'];

  for (const code of testCodes) {
    console.log(`📋 测试数据库代码: ${code}`);
    
    try {
      // 测试验证
      const validation = await DynamicTableMappingService.validateDatabaseCode(code);
      console.log(`   验证结果: ${validation.isValid ? '✅ 有效' : '❌ 无效'}`);
      if (!validation.isValid) {
        console.log(`   错误信息: ${validation.error}`);
        console.log(`   状态码: ${validation.status}`);
      }

      if (validation.isValid) {
        // 测试获取映射
        const mapping = await DynamicTableMappingService.getTableMapping(code);
        if (mapping) {
          console.log(`   表名: ${mapping.tableName}`);
          console.log(`   模型名: ${mapping.modelName}`);
          console.log(`   显示名: ${mapping.displayName}`);
          console.log(`   分类: ${mapping.category}`);
        }

        // 测试获取模型
        try {
          const model = await DynamicTableMappingService.getDynamicModel(code);
          const isValid = DynamicTableMappingService.isPrismaModel(model);
          console.log(`   模型获取: ${isValid ? '✅ 成功' : '❌ 失败'}`);
        } catch (error) {
          console.log(`   模型获取: ❌ 失败 - ${error instanceof Error ? error.message : String(error)}`);
        }
      }

    } catch (error) {
      console.log(`   ❌ 测试失败: ${error instanceof Error ? error.message : String(error)}`);
    }
    
    console.log('');
  }

  // 测试获取所有数据库代码
  console.log('📋 测试获取所有数据库代码:');
  try {
    const allCodes = await DynamicTableMappingService.getAllDatabaseCodes();
    console.log(`   可用数据库: ${allCodes.join(', ')}`);
  } catch (error) {
    console.log(`   ❌ 获取失败: ${error instanceof Error ? error.message : String(error)}`);
  }

  // 测试缓存信息
  console.log('\n📋 缓存信息:');
  const cacheInfo = DynamicTableMappingService.getCacheInfo();
  console.log(`   缓存大小: ${cacheInfo.size}`);
  console.log(`   是否过期: ${cacheInfo.isExpired ? '是' : '否'}`);

  console.log('\n🎉 测试完成！');
}

if (require.main === module) {
  testDynamicMapping().catch(console.error);
} 