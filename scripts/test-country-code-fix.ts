#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

async function testCountryCodeFix() {
  console.log('🧪 测试 Country Code 筛选修复...');
  
  try {
    // 1. 验证配置
    console.log('\n📋 1. 验证 country_code 字段配置...');
    const config = await db.fieldConfig.findFirst({
      where: {
        databaseCode: 'us_pmn',
        fieldName: 'country_code',
        isActive: true
      },
      select: {
        fieldName: true,
        displayName: true,
        filterType: true,
        isFilterable: true,
        isVisible: true
      }
    });
    
    if (config) {
      console.log('✅ 配置正确:');
      console.log(`   字段名: ${config.fieldName}`);
      console.log(`   显示名: ${config.displayName}`);
      console.log(`   筛选类型: ${config.filterType}`);
      console.log(`   可筛选: ${config.isFilterable}`);
      console.log(`   可见: ${config.isVisible}`);
      
      if (config.filterType === 'multi_select' && config.isFilterable) {
        console.log('✅ 配置符合预期 - multi_select 且可筛选');
      } else {
        console.log('❌ 配置不符合预期');
      }
    } else {
      console.log('❌ 未找到配置');
      return;
    }
    
    // 2. 测试 API 数据
    console.log('\n🌐 2. 测试 meta API...');
    const response = await fetch('http://localhost:3001/api/meta/us_pmn');
    const result = await response.json();
    
    if (result.success) {
      const countryCodeData = result.dataWithCounts?.country_code;
      if (countryCodeData && countryCodeData.length > 0) {
        console.log('✅ API 返回数据正常:');
        console.log(`   选项数量: ${countryCodeData.length}`);
        console.log(`   前5个选项: ${countryCodeData.slice(0, 5).map((item: any) => `${item.value} (${item.count})`).join(', ')}`);
      } else {
        console.log('❌ API 未返回 country_code 数据');
      }
    } else {
      console.log('❌ API 请求失败:', result.error);
    }
    
    // 3. 对比 us_class 的工作情况
    console.log('\n🔍 3. 对比 us_class 的 deviceclass 字段...');
    const classConfig = await db.fieldConfig.findFirst({
      where: {
        databaseCode: 'us_class',
        fieldName: 'deviceclass',
        isActive: true
      },
      select: {
        fieldName: true,
        displayName: true,
        filterType: true,
        isFilterable: true,
        isVisible: true
      }
    });
    
    if (classConfig) {
      console.log('✅ us_class deviceclass 配置:');
      console.log(`   字段名: ${classConfig.fieldName}`);
      console.log(`   显示名: ${classConfig.displayName}`);
      console.log(`   筛选类型: ${classConfig.filterType}`);
      console.log(`   可筛选: ${classConfig.isFilterable}`);
      console.log(`   可见: ${classConfig.isVisible}`);
    }
    
    // 4. 总结
    console.log('\n📊 4. 修复总结...');
    console.log('✅ 问题分析:');
    console.log('   - us_pmn 的 country_code 字段配置正确 (multi_select)');
    console.log('   - API 返回数据正常 (93个选项)');
    console.log('   - 问题在于前端 DatabasePageContent.tsx 的逻辑');
    console.log('   - 修复: 对 multi_select 类型使用 mergedOptions 而不是 validOptions 判断');
    
    console.log('\n💡 修复内容:');
    console.log('   - 修改了 DatabasePageContent.tsx 第 876 行的判断逻辑');
    console.log('   - multi_select 类型现在正确检查 mergedOptions.length > 0');
    console.log('   - select 类型仍然检查 validOptions.length > 0');
    
    console.log('\n🎯 预期结果:');
    console.log('   - Country Code 筛选器应该显示在左侧面板');
    console.log('   - 应该显示多选复选框界面');
    console.log('   - 每个选项应该显示计数 (如 "US (145117)")');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  } finally {
    await db.$disconnect();
  }
}

testCountryCodeFix().catch(console.error);
