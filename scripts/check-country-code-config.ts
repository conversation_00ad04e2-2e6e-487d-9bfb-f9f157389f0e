#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

async function checkCountryCodeConfig() {
  console.log('🔍 检查 us_pmn 数据库中 country_code 字段的配置...');
  
  const config = await db.fieldConfig.findFirst({
    where: {
      databaseCode: 'us_pmn',
      fieldName: 'country_code',
      isActive: true
    },
    select: {
      fieldName: true,
      displayName: true,
      fieldType: true,
      filterType: true,
      isFilterable: true,
      isVisible: true
    }
  });
  
  if (config) {
    console.log('✅ 找到配置:');
    console.log('   字段名:', config.fieldName);
    console.log('   显示名:', config.displayName);
    console.log('   字段类型:', config.fieldType);
    console.log('   筛选类型:', config.filterType);
    console.log('   可筛选:', config.isFilterable);
    console.log('   可见:', config.isVisible);
  } else {
    console.log('❌ 未找到配置');
  }
  
  // 对比 us_class 的器械类别字段
  console.log('\n🔍 检查 us_class 数据库中器械类别字段的配置...');
  
  const classConfig = await db.fieldConfig.findFirst({
    where: {
      databaseCode: 'us_class',
      fieldName: 'deviceclass',
      isActive: true
    },
    select: {
      fieldName: true,
      displayName: true,
      fieldType: true,
      filterType: true,
      isFilterable: true,
      isVisible: true
    }
  });
  
  if (classConfig) {
    console.log('✅ 找到 us_class 器械类别配置:');
    console.log('   字段名:', classConfig.fieldName);
    console.log('   显示名:', classConfig.displayName);
    console.log('   字段类型:', classConfig.fieldType);
    console.log('   筛选类型:', classConfig.filterType);
    console.log('   可筛选:', classConfig.isFilterable);
    console.log('   可见:', classConfig.isVisible);
  } else {
    console.log('❌ 未找到 us_class 器械类别配置');
  }
  
  await db.$disconnect();
}

checkCountryCodeConfig().catch(console.error);
