#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 验证导出字段添加结果
 */

async function verifyExportFields() {
  console.log('🔍 验证导出字段添加结果...\n');

  try {
    // 1. 检查字段是否存在
    console.log('📋 检查新增字段...');
    
    const sampleConfig = await db.fieldConfig.findFirst({
      where: { databaseCode: 'us_pmn' },
      select: {
        fieldName: true,
        displayName: true,
        isVisible: true,
        isExportable: true,
        exportOrder: true,
        exportDisplayName: true,
      }
    });

    if (sampleConfig) {
      console.log('  ✅ 新字段可以正常访问');
      console.log(`  示例: ${sampleConfig.fieldName} - 可见:${sampleConfig.isVisible}, 可导出:${sampleConfig.isExportable}`);
    }

    // 2. 统计各数据库的导出配置
    console.log('\n📊 统计导出配置...');
    
    const usPmnStats = await db.fieldConfig.groupBy({
      by: ['isVisible', 'isExportable'],
      where: { databaseCode: 'us_pmn', isActive: true },
      _count: true
    });

    console.log('  us_pmn 配置统计:');
    usPmnStats.forEach(stat => {
      console.log(`    可见:${stat.isVisible}, 可导出:${stat.isExportable} - ${stat._count} 个字段`);
    });

    const usClassStats = await db.fieldConfig.groupBy({
      by: ['isVisible', 'isExportable'],
      where: { databaseCode: 'us_class', isActive: true },
      _count: true
    });

    console.log('  us_class 配置统计:');
    usClassStats.forEach(stat => {
      console.log(`    可见:${stat.isVisible}, 可导出:${stat.isExportable} - ${stat._count} 个字段`);
    });

    // 3. 显示详细的字段配置
    console.log('\n📋 us_pmn 字段详情:');
    const usPmnFields = await db.fieldConfig.findMany({
      where: { databaseCode: 'us_pmn', isActive: true },
      select: {
        fieldName: true,
        displayName: true,
        isVisible: true,
        isExportable: true,
        exportOrder: true,
        exportDisplayName: true,
        listOrder: true,
      },
      orderBy: { listOrder: 'asc' }
    });

    usPmnFields.forEach((field, index) => {
      const status = field.isVisible ? '显示' : '隐藏';
      const exportStatus = field.isExportable ? '可导出' : '不导出';
      console.log(`  ${index + 1}. ${field.fieldName} (${field.displayName}) - ${status}, ${exportStatus}`);
    });

    console.log('\n📋 us_class 字段详情:');
    const usClassFields = await db.fieldConfig.findMany({
      where: { databaseCode: 'us_class', isActive: true },
      select: {
        fieldName: true,
        displayName: true,
        isVisible: true,
        isExportable: true,
        exportOrder: true,
        exportDisplayName: true,
        listOrder: true,
      },
      orderBy: { listOrder: 'asc' }
    });

    usClassFields.forEach((field, index) => {
      const status = field.isVisible ? '显示' : '隐藏';
      const exportStatus = field.isExportable ? '可导出' : '不导出';
      console.log(`  ${index + 1}. ${field.fieldName} (${field.displayName}) - ${status}, ${exportStatus}`);
    });

    console.log('\n✅ 验证完成！');
    console.log('\n💡 现在您可以:');
    console.log('  1. 独立控制字段的显示和导出');
    console.log('  2. 设置不同的导出字段顺序');
    console.log('  3. 自定义导出列名');

  } catch (error) {
    console.error('❌ 验证过程中出错:', error);
  } finally {
    await db.$disconnect();
  }
}

// 运行验证
if (require.main === module) {
  verifyExportFields();
}
