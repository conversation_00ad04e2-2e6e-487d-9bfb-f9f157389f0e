#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 将所有数据库代码统一为小写格式的脚本
 * 这个脚本会：
 * 1. 更新 DatabaseConfig 表中的 code 字段为小写
 * 2. 更新 FieldConfig 表中的 databaseCode 字段为小写
 * 3. 保持数据一致性
 */

// 数据库代码映射：大小写混合 -> 小写
const DATABASE_CODE_MAPPING = {
  'deviceCNImported': 'devicecnimported',
  'deviceCNEvaluation': 'devicecnevaluation', 
  'deviceUS': 'deviceus',
  'deviceHK': 'devicehk',
  'deviceJP': 'devicejp',
  'deviceUK': 'deviceuk',
  'deviceSG': 'devicesg',
  'us_pmn': 'uspmn', // 统一命名规范
  'freePat': 'freepat',
  'subjectNewdrug': 'subjectnewdrug',
  'subjectLicenseout': 'subjectlicenseout',
  'subjectVbp': 'subjectvbp'
} as const;

async function updateDatabaseCodesToLowercase() {
  console.log('🚀 开始将数据库代码统一为小写格式...');
  
  try {
    // 1. 更新 DatabaseConfig 表
    console.log('\n📋 更新 DatabaseConfig 表...');
    
    for (const [oldCode, newCode] of Object.entries(DATABASE_CODE_MAPPING)) {
      const result = await db.databaseConfig.updateMany({
        where: { code: oldCode },
        data: { code: newCode }
      });
      
      if (result.count > 0) {
        console.log(`  ✅ ${oldCode} -> ${newCode} (更新了 ${result.count} 条记录)`);
      } else {
        console.log(`  ⚠️  ${oldCode} -> ${newCode} (未找到记录)`);
      }
    }

    // 2. 更新 FieldConfig 表
    console.log('\n🔧 更新 FieldConfig 表...');
    
    for (const [oldCode, newCode] of Object.entries(DATABASE_CODE_MAPPING)) {
      const result = await db.fieldConfig.updateMany({
        where: { databaseCode: oldCode },
        data: { databaseCode: newCode }
      });
      
      if (result.count > 0) {
        console.log(`  ✅ ${oldCode} -> ${newCode} (更新了 ${result.count} 条字段配置)`);
      } else {
        console.log(`  ⚠️  ${oldCode} -> ${newCode} (未找到字段配置)`);
      }
    }

    // 3. 验证更新结果
    console.log('\n🔍 验证更新结果...');
    
    const allDatabaseConfigs = await db.databaseConfig.findMany({
      select: { code: true, name: true },
      orderBy: { sortOrder: 'asc' }
    });
    
    console.log('  📊 当前数据库配置:');
    allDatabaseConfigs.forEach(config => {
      const isLowercase = config.code === config.code.toLowerCase();
      console.log(`    ${isLowercase ? '✅' : '❌'} ${config.code}: ${config.name}`);
    });

    // 4. 检查是否还有大小写混合的代码
    const mixedCaseCodes = allDatabaseConfigs.filter(
      config => config.code !== config.code.toLowerCase()
    );
    
    if (mixedCaseCodes.length > 0) {
      console.log('\n⚠️  发现仍有大小写混合的代码：');
      mixedCaseCodes.forEach(config => {
        console.log(`    ❌ ${config.code} (建议改为: ${config.code.toLowerCase()})`);
      });
    } else {
      console.log('\n✅ 所有数据库代码已成功统一为小写格式！');
    }

    console.log('\n🎉 数据库代码小写化更新完成！');
    
  } catch (error) {
    console.error('❌ 更新过程中出现错误：', error);
    throw error;
  } finally {
    await db.$disconnect();
  }
}

// 执行更新
if (require.main === module) {
  updateDatabaseCodesToLowercase()
    .catch(error => {
      console.error('❌ 脚本执行失败：', error);
      process.exit(1);
    });
}

export { updateDatabaseCodesToLowercase, DATABASE_CODE_MAPPING }; 