#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 修复 deviceClass 字段名称问题
 * 将 deviceClass 改为 deviceclass 以匹配数据库字段
 */

async function fixDeviceClassFieldName() {
  console.log('🔧 修复 deviceClass 字段名称...');

  try {
    // 查找当前的 deviceClass 配置
    const currentConfig = await db.fieldConfig.findFirst({
      where: {
        databaseCode: 'us_class',
        fieldName: 'deviceClass',
      },
    });

    if (currentConfig) {
      console.log('📋 找到现有的 deviceClass 配置，更新为 deviceclass...');
      
      // 更新字段名为小写
      await db.fieldConfig.update({
        where: { id: currentConfig.id },
        data: {
          fieldName: 'deviceclass', // 改为小写，匹配数据库字段
          updatedAt: new Date(),
        },
      });
      
      console.log('✅ 已更新字段名为 deviceclass');
    } else {
      console.log('📋 未找到 deviceClass 配置，创建新的 deviceclass 配置...');
      
      // 创建新的配置
      await db.fieldConfig.create({
        data: {
          databaseCode: 'us_class',
          fieldName: 'deviceclass', // 使用小写
          displayName: '器械类别',
          fieldType: 'select',
          isVisible: true,
          isSearchable: false,
          isFilterable: true,
          isSortable: true,
          sortOrder: 5,
          listOrder: 5,
          detailOrder: 5,
          searchType: 'exact',
          filterType: 'multi_select',
          isActive: true,
        },
      });
      
      console.log('✅ 已创建新的 deviceclass 配置');
    }

    // 验证配置
    console.log('\n🔍 验证配置结果...');
    const finalConfig = await db.fieldConfig.findFirst({
      where: {
        databaseCode: 'us_class',
        fieldName: 'deviceclass',
        isActive: true,
      },
      select: {
        fieldName: true,
        displayName: true,
        filterType: true,
        isFilterable: true,
        updatedAt: true,
      },
    });

    if (finalConfig) {
      console.log(`\n📋 最终配置:`);
      console.log(`   字段名: ${finalConfig.fieldName}`);
      console.log(`   显示名: ${finalConfig.displayName}`);
      console.log(`   筛选类型: ${finalConfig.filterType}`);
      console.log(`   可筛选: ${finalConfig.isFilterable}`);
      console.log(`   更新时间: ${finalConfig.updatedAt.toISOString()}`);
    }

    // 检查数据库中的实际数据
    console.log('\n📊 检查数据库中的 deviceclass 字段数据...');
    const sampleData = await (db as any).uSClass.findMany({
      select: { deviceclass: true },
      where: { deviceclass: { not: null } },
      take: 5,
    });

    console.log('   样本数据:');
    sampleData.forEach((item: any, index: number) => {
      console.log(`   ${index + 1}. ${item.deviceclass}`);
    });

  } catch (error) {
    console.error('❌ 操作失败:', error);
  } finally {
    await db.$disconnect();
  }
}

// 执行修复
fixDeviceClassFieldName()
  .then(() => {
    console.log('\n✨ 修复完成');
    console.log('\n🔄 请清除缓存以使配置生效');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 修复失败:', error);
    process.exit(1);
  });
