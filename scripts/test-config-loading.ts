#!/usr/bin/env tsx

import { getDatabaseConfig } from '../src/lib/configCache';

/**
 * 测试配置加载
 */

async function testConfigLoading() {
  console.log('🧪 测试配置加载...\n');

  try {
    // 测试us_pmn配置
    console.log('📋 测试us_pmn配置加载...');
    const usPmnConfig = await getDatabaseConfig('us_pmn');
    
    console.log(`  字段数量: ${usPmnConfig.fields.length}`);
    
    if (usPmnConfig.fields.length > 0) {
      const firstField = usPmnConfig.fields[0];
      console.log(`  第一个字段: ${firstField.fieldName}`);
      console.log(`  是否有导出配置: ${firstField.isExportable !== undefined ? '是' : '否'}`);
      console.log(`  导出顺序: ${firstField.exportOrder || '未设置'}`);
      console.log(`  导出名称: ${firstField.exportDisplayName || '未设置'}`);
      
      // 查找有导出顺序的字段
      const exportableFields = usPmnConfig.fields
        .filter(f => f.isExportable !== false)
        .sort((a, b) => (a.exportOrder || 0) - (b.exportOrder || 0));
      
      console.log(`  可导出字段数量: ${exportableFields.length}`);
      console.log('  前5个导出字段:');
      exportableFields.slice(0, 5).forEach((field, index) => {
        console.log(`    ${index + 1}. ${field.fieldName} (顺序: ${field.exportOrder || 0})`);
      });
    }

    // 测试us_class配置
    console.log('\n📋 测试us_class配置加载...');
    const usClassConfig = await getDatabaseConfig('us_class');
    
    console.log(`  字段数量: ${usClassConfig.fields.length}`);
    
    if (usClassConfig.fields.length > 0) {
      const firstField = usClassConfig.fields[0];
      console.log(`  第一个字段: ${firstField.fieldName}`);
      console.log(`  是否有导出配置: ${firstField.isExportable !== undefined ? '是' : '否'}`);
      console.log(`  导出顺序: ${firstField.exportOrder || '未设置'}`);
      console.log(`  导出名称: ${firstField.exportDisplayName || '未设置'}`);
      
      // 查找有导出顺序的字段
      const exportableFields = usClassConfig.fields
        .filter(f => f.isExportable !== false)
        .sort((a, b) => (a.exportOrder || 0) - (b.exportOrder || 0));
      
      console.log(`  可导出字段数量: ${exportableFields.length}`);
      console.log('  前5个导出字段:');
      exportableFields.slice(0, 5).forEach((field, index) => {
        console.log(`    ${index + 1}. ${field.fieldName} (顺序: ${field.exportOrder || 0})`);
      });
    }

    console.log('\n✅ 配置加载测试完成！');

  } catch (error) {
    console.error('❌ 测试过程中出错:', error);
  }
}

// 运行测试
if (require.main === module) {
  testConfigLoading();
}
