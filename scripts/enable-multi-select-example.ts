#!/usr/bin/env tsx

import { QUICK_SETUPS } from './quick-setup-database';
import { ConfigManager } from './config-manager';

/**
 * 实际使用示例：为数据库启用多选筛选功能
 */

async function enableMultiSelectExample() {
  console.log('🎯 实际使用示例：启用多选筛选功能\n');

  try {
    // 示例1：为现有数据库字段启用多选
    console.log('📋 示例1: 为现有数据库启用多选筛选');
    console.log('   目标: us_class 数据库的 deviceclass 字段');
    
    // 检查当前配置
    console.log('   🔍 检查当前配置...');
    await ConfigManager.validateConfig('us_class');
    
    // 这个字段已经是 multi_select 了，所以我们演示其他字段
    console.log('   ✅ deviceclass 字段已经是 multi_select');
    
    // 示例2：创建新的医疗器械数据库（使用模板）
    console.log('\n📋 示例2: 创建新的医疗器械数据库');
    console.log('   目标: 创建欧盟医疗器械数据库');
    
    await QUICK_SETUPS.createMedicalDeviceDB(
      'device_eu_demo',
      '欧盟医疗器械数据库（演示）',
      'premium'
    );
    
    console.log('   ✅ 欧盟医疗器械数据库创建完成');
    console.log('   📋 该数据库自动包含以下多选字段:');
    console.log('      - deviceclass (器械类别)');
    console.log('      - category (产品分类)');
    
    // 验证新数据库配置
    await ConfigManager.validateConfig('device_eu_demo');
    
    // 示例3：创建药品数据库
    console.log('\n📋 示例3: 创建药品数据库');
    console.log('   目标: 创建中国药品数据库');
    
    await QUICK_SETUPS.createDrugDB(
      'drug_cn_demo',
      '中国药品数据库（演示）',
      'free'
    );
    
    console.log('   ✅ 中国药品数据库创建完成');
    console.log('   📋 该数据库自动包含以下多选字段:');
    console.log('      - activeIngredient (活性成分)');
    console.log('      - dosageForm (剂型)');
    console.log('      - therapeuticArea (治疗领域)');
    
    // 验证新数据库配置
    await ConfigManager.validateConfig('drug_cn_demo');
    
    // 示例4：自定义数据库配置
    console.log('\n📋 示例4: 创建自定义数据库');
    console.log('   目标: 创建临床试验数据库');
    
    await QUICK_SETUPS.createCustomDB({
      code: 'clinical_trial_demo',
      name: '临床试验数据库（演示）',
      category: '临床研究',
      description: '临床试验信息管理数据库',
      accessLevel: 'premium',
      customFields: [
        { fieldName: 'trialTitle', displayName: '试验标题', filterType: 'input' },
        { fieldName: 'phase', displayName: '试验阶段', filterType: 'multi_select' },
        { fieldName: 'indication', displayName: '适应症', filterType: 'multi_select' },
        { fieldName: 'sponsor', displayName: '申办方', filterType: 'input' },
        { fieldName: 'status', displayName: '试验状态', filterType: 'select' },
        { fieldName: 'therapeuticArea', displayName: '治疗领域', filterType: 'multi_select' },
      ],
    });
    
    console.log('   ✅ 临床试验数据库创建完成');
    console.log('   📋 该数据库包含以下多选字段:');
    console.log('      - phase (试验阶段)');
    console.log('      - indication (适应症)');
    console.log('      - therapeuticArea (治疗领域)');
    
    // 验证新数据库配置
    await ConfigManager.validateConfig('clinical_trial_demo');
    
    // 示例5：批量更新现有字段为多选
    console.log('\n📋 示例5: 批量启用多选功能');
    console.log('   目标: 为临床试验数据库的更多字段启用多选');
    
    await ConfigManager.updateFilterTypes('clinical_trial_demo', [
      { fieldName: 'sponsor', filterType: 'multi_select' }, // 将申办方改为多选
    ]);
    
    console.log('   ✅ 批量更新完成');
    
    // 最终验证
    console.log('\n🔍 最终验证所有创建的数据库配置:');
    await ConfigManager.listConfigs();
    
    console.log('\n🎉 所有示例执行完成!');
    console.log('\n📚 使用总结:');
    console.log('   1. ✅ 使用模板快速创建标准数据库配置');
    console.log('   2. ✅ 自定义字段配置满足特殊需求');
    console.log('   3. ✅ 批量更新字段筛选类型');
    console.log('   4. ✅ 配置验证确保正确性');
    console.log('   5. ✅ 多选筛选功能开箱即用');

  } catch (error) {
    console.error('\n❌ 示例执行失败:', error);
    throw error;
  }
}

/**
 * 清理示例数据
 */
async function cleanupExamples() {
  console.log('🧹 清理示例数据...');
  
  const { db } = await import('../src/lib/prisma');
  
  try {
    const demoDatabases = [
      'device_eu_demo',
      'drug_cn_demo', 
      'clinical_trial_demo'
    ];
    
    for (const dbCode of demoDatabases) {
      // 删除字段配置
      await db.fieldConfig.deleteMany({
        where: { databaseCode: dbCode },
      });
      
      // 删除数据库配置
      await db.databaseConfig.deleteMany({
        where: { code: dbCode },
      });
      
      console.log(`   ✅ 已清理 ${dbCode}`);
    }
    
    console.log('✅ 示例数据清理完成');
    
  } catch (error) {
    console.error('❌ 清理失败:', error);
    throw error;
  } finally {
    await db.$disconnect();
  }
}

/**
 * 显示使用指南
 */
function showUsageGuide() {
  console.log(`
🛠️  多选筛选功能使用指南

📋 快速启用多选筛选的方法:

1️⃣ 使用模板创建新数据库:
   await QUICK_SETUPS.createMedicalDeviceDB('my_device_db', '我的器械数据库', 'premium');

2️⃣ 为现有字段启用多选:
   await ConfigManager.updateFilterTypes('my_database', [
     { fieldName: 'category', filterType: 'multi_select' },
     { fieldName: 'tags', filterType: 'multi_select' },
   ]);

3️⃣ 创建自定义数据库:
   await QUICK_SETUPS.createCustomDB({
     code: 'my_custom_db',
     name: '我的自定义数据库',
     category: '自定义',
     accessLevel: 'free',
     customFields: [
       { fieldName: 'type', displayName: '类型', filterType: 'multi_select' },
     ],
   });

4️⃣ 验证配置:
   await ConfigManager.validateConfig('my_database');

🎯 支持的筛选类型:
   - select: 单选下拉框
   - multi_select: 多选复选框 ⭐
   - input: 文本输入框
   - date_range: 日期范围选择器
   - checkbox: 复选框

📚 运行示例:
   npx tsx scripts/enable-multi-select-example.ts
   npx tsx scripts/enable-multi-select-example.ts --cleanup
`);
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--cleanup')) {
    await cleanupExamples();
  } else if (args.includes('--help')) {
    showUsageGuide();
  } else {
    await enableMultiSelectExample();
  }
}

// 执行示例
if (require.main === module) {
  main()
    .then(() => {
      console.log('\n✨ 执行完成!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 执行失败:', error);
      process.exit(1);
    });
}

export { enableMultiSelectExample, cleanupExamples };
