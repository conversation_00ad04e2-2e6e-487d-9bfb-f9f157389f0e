#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// 从现有的dynamicTableMapping.ts获取的映射配置
const TABLE_MAPPING_CONFIGS = {
  // 中国大陆医疗器械数据库
  deviceCNImported: {
    tableName: 'medical_device_cn_imported',
    modelName: 'medicalDevice_CN_Imported'
  },
  
  deviceCNEvaluation: {
    tableName: 'medical_device_cn_evaluation', 
    modelName: 'medicalDevice_CN_Evaluation'
  },

  // 国际医疗器械数据库
  deviceUS: {
    tableName: 'medical_device_us',
    modelName: 'medicalDevice_US'
  },

  deviceHK: {
    tableName: 'medical_device_hk',
    modelName: 'medicalDevice_HK'
  },

  deviceJP: {
    tableName: 'medical_device_jp',
    modelName: 'medicalDevice_JP'
  },

  deviceUK: {
    tableName: 'medical_device_uk',
    modelName: 'medicalDevice_UK'
  },

  deviceSG: {
    tableName: 'medical_device_sg',
    modelName: 'medicalDevice_SG'
  },

  // 美国PMN(510k)数据库 - 专用模型
  us_pmn: {
    tableName: 'medical_device_us_pmn',
    modelName: 'medicalDevice_US_PMN'
  },

  // 专利数据库
  freePat: {
    tableName: 'medical_device_free_pat',
    modelName: 'medicalDevice_FreePat'
  },

  // 专题数据库
  subjectNewdrug: {
    tableName: 'medical_device_subject_newdrug',
    modelName: 'medicalDevice_SubjectNewdrug'
  },

  subjectLicenseout: {
    tableName: 'medical_device_subject_licenseout',
    modelName: 'medicalDevice_SubjectLicenseout'
  },

  subjectVbp: {
    tableName: 'medical_device_subject_vbp',
    modelName: 'medicalDevice_SubjectVbp'
  }
} as const;

async function migrateTableMappingConfigs() {
  console.log('🚀 开始迁移表映射配置到DatabaseConfig...');

  let successCount = 0;
  let skipCount = 0;
  let errorCount = 0;

  for (const [code, config] of Object.entries(TABLE_MAPPING_CONFIGS)) {
    try {
      // 检查DatabaseConfig记录是否存在
      const existingConfig = await prisma.databaseConfig.findUnique({
        where: { code },
        select: { id: true, code: true, tableName: true, modelName: true }
      });

      if (!existingConfig) {
        console.log(`⚠️  [${code}] DatabaseConfig记录不存在，跳过...`);
        skipCount++;
        continue;
      }

      // 如果已有配置，询问是否覆盖
      if (existingConfig.tableName || existingConfig.modelName) {
        console.log(`ℹ️  [${code}] 已有表映射配置:`);
        console.log(`   当前: tableName="${existingConfig.tableName}", modelName="${existingConfig.modelName}"`);
        console.log(`   新的: tableName="${config.tableName}", modelName="${config.modelName}"`);
        
        // 在实际使用中可以添加交互式确认，这里直接更新
        console.log(`   更新配置...`);
      }

      // 更新DatabaseConfig记录，添加表映射信息
      await prisma.databaseConfig.update({
        where: { code },
        data: {
          tableName: config.tableName,
          modelName: config.modelName,
        }
      });

      console.log(`✅ [${code}] 表映射配置更新成功`);
      console.log(`   tableName: ${config.tableName}`);
      console.log(`   modelName: ${config.modelName}`);
      successCount++;

    } catch (error) {
      console.error(`❌ [${code}] 更新失败:`, error);
      errorCount++;
    }
  }

  console.log('\n📊 迁移结果汇总:');
  console.log(`✅ 成功: ${successCount} 个`);
  console.log(`⚠️  跳过: ${skipCount} 个`);
  console.log(`❌ 失败: ${errorCount} 个`);

  if (errorCount === 0) {
    console.log('\n🎉 表映射配置迁移完成！');
  } else {
    console.log('\n⚠️  迁移完成，但存在错误，请检查上述日志。');
  }
}

async function main() {
  try {
    await migrateTableMappingConfigs();
  } catch (error) {
    console.error('💥 迁移过程发生严重错误:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch((error) => {
    console.error('💥 脚本执行失败:', error);
    process.exit(1);
  });
}

export { migrateTableMappingConfigs }; 