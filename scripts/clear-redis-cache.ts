#!/usr/bin/env tsx

import { ConfigCacheService } from '../src/lib/configCache';

/**
 * 清除 Redis 配置缓存
 */

async function clearRedisCache() {
  console.log('🧹 清除 Redis 配置缓存...');

  try {
    // 清除所有配置缓存
    await ConfigCacheService.clearAllCache();
    console.log('✅ 已清除所有配置缓存');

    // 清除特定数据库缓存
    await ConfigCacheService.clearDatabaseCache('us_class');
    console.log('✅ 已清除 us_class 数据库缓存');

    console.log('\n🔄 缓存已清除，下次请求将从数据库重新加载配置');

  } catch (error) {
    console.error('❌ 清除缓存失败:', error);
  }
}

// 执行清除
clearRedisCache()
  .then(() => {
    console.log('\n✨ 缓存清除完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 清除失败:', error);
    process.exit(1);
  });
