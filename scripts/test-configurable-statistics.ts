#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';
import { getDatabaseConfig } from '../src/lib/configCache';

/**
 * 测试可配置统计功能
 * 验证统计排序配置是否正确工作
 */

async function testConfigurableStatistics() {
  console.log('🧪 测试可配置统计功能...');

  try {
    // 1. 检查 us_class 的统计配置
    console.log('\n🔍 检查 us_class 统计配置...');
    const config = await getDatabaseConfig('us_class');
    
    const statisticsFields = config.fields.filter(f => f.isStatisticsEnabled);
    console.log(`   找到 ${statisticsFields.length} 个启用统计的字段:`);
    
    statisticsFields.forEach((field, index) => {
      const sortIcon = field.statisticsSortOrder === 'desc' ? '📉' : '📈';
      console.log(`   ${index + 1}. ${field.fieldName} (${field.statisticsDisplayName})`);
      console.log(`      排序: ${field.statisticsSortOrder} ${sortIcon}`);
      console.log(`      类型: ${field.statisticsType}`);
    });

    // 2. 测试模拟统计查询
    console.log('\n🔧 模拟统计查询...');
    
    // 获取动态模型
    const { getDynamicModel } = await import('../src/lib/staticTableMappingService');
    const model = await getDynamicModel('us_class');
    
    if (!model) {
      console.log('❌ 无法获取 us_class 模型');
      return;
    }

    // 测试每个统计字段
    for (const field of statisticsFields) {
      if (field.statisticsType === 'group_by') {
        try {
          console.log(`\n   测试字段: ${field.fieldName} (${field.statisticsSortOrder})`);
          
          const limit = (field.statisticsConfig as any)?.limit || 5;
          const sortOrder = field.statisticsSortOrder || 'desc';
          
          const results = await (model as any).groupBy({
            by: [field.fieldName],
            where: { 
              [field.fieldName]: { not: null },
              isActive: true
            },
            _count: { [field.fieldName]: true },
            orderBy: { _count: { [field.fieldName]: sortOrder } },
            take: limit,
          });

          console.log(`      查询结果 (前${limit}条, ${sortOrder}序):`);
          results.forEach((item: any, index: number) => {
            const sortIcon = sortOrder === 'desc' ? '📉' : '📈';
            console.log(`        ${index + 1}. ${item[field.fieldName]}: ${item._count[field.fieldName]} ${sortIcon}`);
          });

          // 验证排序是否正确
          if (results.length > 1) {
            const isCorrectOrder = sortOrder === 'desc' 
              ? results[0]._count[field.fieldName] >= results[1]._count[field.fieldName]
              : results[0]._count[field.fieldName] <= results[1]._count[field.fieldName];
            
            if (isCorrectOrder) {
              console.log(`      ✅ 排序正确 (${sortOrder})`);
            } else {
              console.log(`      ❌ 排序错误 (期望${sortOrder})`);
            }
          }

        } catch (error) {
          console.log(`      ❌ 查询失败: ${error}`);
        }
      }
    }

    // 3. 测试 API 端点
    console.log('\n🌐 测试 API 端点...');
    console.log('   可以通过以下方式测试:');
    console.log('   - GET /api/stats/us_class/configurable');
    console.log('   - 前端使用 ConfigurableStatsPanel 组件');
    
    // 4. 显示配置对比
    console.log('\n📊 配置对比:');
    console.log('   器械类别分布 (deviceclass): 倒序 📉 - 显示数量最多的类别在前');
    console.log('   产品代码统计 (productcode): 正序 📈 - 按字母顺序排列');
    console.log('   医学专科分布 (medicalspecialty): 倒序 📉 - 显示数量最多的专科在前');
    console.log('   法规编号统计 (regulationnumber): 倒序 📉 - 显示数量最多的法规在前');

    console.log('\n✅ 可配置统计功能测试完成！');
    console.log('\n💡 使用说明:');
    console.log('   1. 统计排序现在完全可配置');
    console.log('   2. 通过 fieldConfig.statisticsSortOrder 控制排序方向');
    console.log('   3. 支持 "asc" (正序) 和 "desc" (倒序)');
    console.log('   4. 默认值为 "desc" 保持向后兼容');

  } catch (error) {
    console.error('❌ 测试失败:', error);
    throw error;
  } finally {
    await db.$disconnect();
  }
}

// 执行测试
if (require.main === module) {
  testConfigurableStatistics()
    .then(() => {
      console.log('\n🎉 测试完成！');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 测试失败:', error);
      process.exit(1);
    });
}

export { testConfigurableStatistics };
