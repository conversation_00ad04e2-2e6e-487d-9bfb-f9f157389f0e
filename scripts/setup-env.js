#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

console.log('🚀 环境变量设置工具\n');

// 生成安全的会话密钥
function generateSessionSecret() {
  return crypto.randomBytes(32).toString('hex');
}

// 检查是否存在 .env.local
const envPath = path.join(process.cwd(), '.env.local');
const envExamplePath = path.join(process.cwd(), '.env.example');

if (fs.existsSync(envPath)) {
  console.log('⚠️  .env.local 文件已存在');
  console.log('如需重新生成，请先删除现有文件\n');
  process.exit(0);
}

// 创建环境变量内容
const sessionSecret = generateSessionSecret();

const envContent = `# 数据库连接
# 请替换为您的实际数据库连接字符串
DATABASE_URL="postgresql://username:password@localhost:5432/database_name"

# 会话密钥 (自动生成，请勿修改)
SESSION_SECRET="${sessionSecret}"

# 应用环境
NODE_ENV="development"

# 分析功能 (可选)
ANALYTICS_ENABLED="true"

# 邮件服务 (可选，如果需要邮件功能)
# SMTP_HOST="smtp.gmail.com"
# SMTP_PORT="587"
# SMTP_USER="<EMAIL>"
# SMTP_PASS="your-app-password"

# 外部API密钥 (可选)
# EXTERNAL_API_KEY="your-api-key"
`;

// 写入 .env.local 文件
try {
  fs.writeFileSync(envPath, envContent);
  console.log('✅ .env.local 文件已创建');
  console.log('📝 已自动生成安全的 SESSION_SECRET');
  console.log('\n📋 下一步操作:');
  console.log('1. 编辑 .env.local 文件');
  console.log('2. 设置正确的 DATABASE_URL');
  console.log('3. 运行 npm run dev 启动应用');
  console.log('\n🔍 使用 npm run health-check 检查系统状态');
} catch (error) {
  console.error('❌ 创建 .env.local 文件失败:', error.message);
  process.exit(1);
}

// 创建 .env.example 文件 (如果不存在)
if (!fs.existsSync(envExamplePath)) {
  const exampleContent = `# 数据库连接
DATABASE_URL="postgresql://username:password@localhost:5432/database_name"

# 会话密钥 (使用 npm run setup-env 自动生成)
SESSION_SECRET="your-session-secret-here"

# 应用环境
NODE_ENV="development"

# 分析功能
ANALYTICS_ENABLED="true"
`;

  try {
    fs.writeFileSync(envExamplePath, exampleContent);
    console.log('✅ .env.example 文件已创建');
  } catch (error) {
    console.warn('⚠️  创建 .env.example 文件失败:', error.message);
  }
}

console.log('\n🎉 环境设置完成！');
