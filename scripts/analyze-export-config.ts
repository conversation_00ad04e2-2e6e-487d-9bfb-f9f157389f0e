#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 分析当前导出功能配置
 */

async function analyzeExportConfig() {
  console.log('🔍 分析当前导出功能配置...\n');

  try {
    // 1. 查看us_pmn的字段配置
    console.log('📋 us_pmn 字段配置:');
    const usPmnFields = await db.fieldConfig.findMany({
      where: { 
        databaseCode: 'us_pmn',
        isActive: true 
      },
      select: {
        fieldName: true,
        displayName: true,
        isVisible: true,
        isSearchable: true,
        isFilterable: true,
        listOrder: true,
        fieldType: true,
      },
      orderBy: { listOrder: 'asc' }
    });

    console.log(`  总字段数: ${usPmnFields.length}`);
    console.log(`  可见字段: ${usPmnFields.filter(f => f.isVisible).length}`);
    console.log(`  不可见字段: ${usPmnFields.filter(f => !f.isVisible).length}`);
    
    console.log('\n  可见字段列表:');
    usPmnFields
      .filter(f => f.isVisible)
      .forEach((field, index) => {
        console.log(`    ${index + 1}. ${field.fieldName} (${field.displayName}) - ${field.fieldType}`);
      });

    console.log('\n  不可见字段列表:');
    usPmnFields
      .filter(f => !f.isVisible)
      .forEach((field, index) => {
        console.log(`    ${index + 1}. ${field.fieldName} (${field.displayName}) - ${field.fieldType}`);
      });

    // 2. 查看us_class的字段配置
    console.log('\n📋 us_class 字段配置:');
    const usClassFields = await db.fieldConfig.findMany({
      where: { 
        databaseCode: 'us_class',
        isActive: true 
      },
      select: {
        fieldName: true,
        displayName: true,
        isVisible: true,
        isSearchable: true,
        isFilterable: true,
        listOrder: true,
        fieldType: true,
      },
      orderBy: { listOrder: 'asc' }
    });

    console.log(`  总字段数: ${usClassFields.length}`);
    console.log(`  可见字段: ${usClassFields.filter(f => f.isVisible).length}`);
    console.log(`  不可见字段: ${usClassFields.filter(f => !f.isVisible).length}`);
    
    console.log('\n  可见字段列表:');
    usClassFields
      .filter(f => f.isVisible)
      .forEach((field, index) => {
        console.log(`    ${index + 1}. ${field.fieldName} (${field.displayName}) - ${field.fieldType}`);
      });

    console.log('\n  不可见字段列表:');
    usClassFields
      .filter(f => !f.isVisible)
      .forEach((field, index) => {
        console.log(`    ${index + 1}. ${field.fieldName} (${field.displayName}) - ${field.fieldType}`);
      });

    // 3. 分析当前导出逻辑
    console.log('\n🔍 当前导出逻辑分析:');
    console.log('  - 导出字段基于: config.fields?.filter(f => f.isVisible)');
    console.log('  - 排序方式: listOrder 升序');
    console.log('  - 权限控制: 基于数据库的 accessLevel');
    console.log('  - 支持格式: CSV, Excel, JSON');
    console.log('  - 字段选择: 仅导出可见字段 (isVisible = true)');

    // 4. 建议的改进方案
    console.log('\n💡 建议的改进方案:');
    console.log('  1. 在 FieldConfig 表中添加 isExportable 字段');
    console.log('  2. 添加 exportOrder 字段控制导出字段顺序');
    console.log('  3. 添加 exportDisplayName 字段自定义导出列名');
    console.log('  4. 修改导出逻辑使用 isExportable 而不是 isVisible');
    console.log('  5. 保持向后兼容：如果 isExportable 未设置，则使用 isVisible');

    // 5. 检查是否已有导出相关字段
    console.log('\n🔍 检查 FieldConfig 表结构...');
    const sampleField = await db.fieldConfig.findFirst({
      where: { databaseCode: 'us_pmn' }
    });
    
    if (sampleField) {
      const fieldKeys = Object.keys(sampleField);
      const exportRelatedFields = fieldKeys.filter(key => 
        key.toLowerCase().includes('export')
      );
      
      if (exportRelatedFields.length > 0) {
        console.log(`  ✅ 已存在导出相关字段: ${exportRelatedFields.join(', ')}`);
      } else {
        console.log('  ❌ 未发现导出相关字段，需要添加');
      }
    }

    console.log('\n✅ 分析完成！');

  } catch (error) {
    console.error('❌ 分析过程中出错:', error);
  } finally {
    await db.$disconnect();
  }
}

// 运行分析
if (require.main === module) {
  analyzeExportConfig();
}
