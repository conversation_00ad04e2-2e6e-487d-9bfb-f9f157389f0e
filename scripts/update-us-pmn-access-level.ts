#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 更新 us_pmn 数据库访问级别为免费
 * 这个脚本会将 us_pmn 数据库的 accessLevel 从 'premium' 修改为 'free'
 */

async function updateUSPmnAccessLevel() {
  console.log('🔄 开始更新 us_pmn 数据库访问级别...');

  try {
    // 查找 us_pmn 数据库配置
    const existingConfig = await db.databaseConfig.findUnique({
      where: { code: 'us_pmn' },
      select: { id: true, code: true, name: true, accessLevel: true }
    });

    if (!existingConfig) {
      console.log('❌ 未找到 us_pmn 数据库配置，请先创建配置');
      return;
    }

    console.log(`📋 当前配置:`);
    console.log(`   数据库: ${existingConfig.name} (${existingConfig.code})`);
    console.log(`   当前访问级别: ${existingConfig.accessLevel}`);

    if (existingConfig.accessLevel === 'free') {
      console.log('✅ us_pmn 已经是免费访问级别，无需更新');
      return;
    }

    // 更新访问级别为 free
    const updatedConfig = await db.databaseConfig.update({
      where: { code: 'us_pmn' },
      data: { accessLevel: 'free' },
      select: { code: true, name: true, accessLevel: true }
    });

    console.log(`✅ 更新成功!`);
    console.log(`   数据库: ${updatedConfig.name} (${updatedConfig.code})`);
    console.log(`   新访问级别: ${updatedConfig.accessLevel}`);

    // 验证更新
    const verifyConfig = await db.databaseConfig.findUnique({
      where: { code: 'us_pmn' },
      select: { accessLevel: true }
    });

    if (verifyConfig?.accessLevel === 'free') {
      console.log('✅ 验证成功: us_pmn 访问级别已更新为 free');
    } else {
      console.log('❌ 验证失败: 访问级别更新可能有问题');
    }

  } catch (error) {
    console.error('❌ 更新失败:', error);
  } finally {
    await db.$disconnect();
  }
}

// 执行更新
updateUSPmnAccessLevel()
  .then(() => {
    console.log('✨ 脚本执行完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 脚本执行失败:', error);
    process.exit(1);
  }); 