#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';
import { ConfigCacheService } from '../src/lib/configCache';

/**
 * 测试 us_pmn 配置是否正确
 */

async function testUsPmnConfig() {
  console.log('🔍 测试 us_pmn 配置...\n');

  try {
    // 1. 检查数据库配置
    console.log('📊 1. 数据库中的 devicename 配置:');
    const dbConfig = await db.fieldConfig.findFirst({
      where: {
        databaseCode: 'us_pmn',
        fieldName: 'devicename',
        isActive: true
      },
      select: {
        fieldName: true,
        displayName: true,
        filterType: true,
        isFilterable: true,
        isActive: true,
        updatedAt: true
      }
    });

    if (dbConfig) {
      console.table([dbConfig]);
    } else {
      console.log('❌ 未找到 devicename 配置');
      return;
    }

    // 2. 检查缓存配置
    console.log('\n💾 2. 缓存中的配置:');
    try {
      const cacheConfig = await ConfigCacheService.getDatabaseConfig('us_pmn');
      const devicenameField = cacheConfig.fields.find(f => f.fieldName === 'devicename');
      
      if (devicenameField) {
        console.table([{
          fieldName: devicenameField.fieldName,
          displayName: devicenameField.displayName,
          filterType: devicenameField.filterType,
          isFilterable: devicenameField.isFilterable
        }]);
      } else {
        console.log('❌ 缓存中未找到 devicename 配置');
      }
    } catch (error) {
      console.error('❌ 获取缓存配置失败:', error);
    }

    // 3. 测试API
    console.log('\n🌐 3. API 返回的配置:');
    try {
      const response = await fetch('http://localhost:3000/api/meta/us_pmn');
      const result = await response.json();
      
      if (result.success && result.config) {
        const devicenameField = result.config.fields.find((f: any) => f.fieldName === 'devicename');
        
        if (devicenameField) {
          console.table([{
            fieldName: devicenameField.fieldName,
            displayName: devicenameField.displayName,
            filterType: devicenameField.filterType,
            isFilterable: devicenameField.isFilterable
          }]);
        } else {
          console.log('❌ API 返回中未找到 devicename 配置');
        }
      } else {
        console.error('❌ API 调用失败:', result.error);
      }
    } catch (error) {
      console.error('❌ API 测试失败:', error);
    }

    // 4. 检查所有可筛选字段
    console.log('\n📋 4. 所有可筛选字段:');
    const filterableFields = await db.fieldConfig.findMany({
      where: {
        databaseCode: 'us_pmn',
        isFilterable: true,
        isActive: true
      },
      select: {
        fieldName: true,
        displayName: true,
        filterType: true,
        sortOrder: true
      },
      orderBy: { sortOrder: 'asc' }
    });

    console.table(filterableFields);

    // 5. 验证结果
    console.log('\n✅ 验证结果:');
    const isCorrect = dbConfig.filterType === 'input' && dbConfig.isFilterable === true;
    
    if (isCorrect) {
      console.log('✅ devicename 字段配置正确');
      console.log('   - filterType: input ✓');
      console.log('   - isFilterable: true ✓');
      console.log('\n🎯 如果前端仍显示选择框，请:');
      console.log('   1. 刷新浏览器页面 (Ctrl+F5 强制刷新)');
      console.log('   2. 清除浏览器缓存');
      console.log('   3. 检查浏览器开发者工具的网络请求');
    } else {
      console.log('❌ devicename 字段配置不正确');
      console.log(`   - filterType: ${dbConfig.filterType} (应该是 input)`);
      console.log(`   - isFilterable: ${dbConfig.isFilterable} (应该是 true)`);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 执行测试
testUsPmnConfig()
  .then(() => {
    console.log('\n✨ 测试完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 测试失败:', error);
    process.exit(1);
  });
