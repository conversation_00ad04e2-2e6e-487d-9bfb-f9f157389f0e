#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 为 FieldConfig 表添加统计排序配置字段
 * 不会清空现有数据，只是添加新字段
 */

async function addStatisticsSortConfig() {
  console.log('🔧 开始添加统计排序配置字段...');

  try {
    // 1. 检查统计相关字段是否已存在
    console.log('🔍 检查现有统计字段...');
    const existingColumns = await db.$queryRaw`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'FieldConfig' 
      AND column_name IN (
        'isStatisticsEnabled', 
        'statisticsOrder', 
        'statisticsType', 
        'statisticsDisplayName', 
        'statisticsConfig',
        'statisticsSortOrder'
      )
    ` as any[];

    const existingColumnNames = existingColumns.map((col: any) => col.column_name);
    console.log('   已存在的统计字段:', existingColumnNames);

    // 2. 创建 StatisticsType 枚举（如果不存在）
    console.log('🔧 确保 StatisticsType 枚举存在...');
    await db.$executeRaw`
      DO $$ BEGIN
        CREATE TYPE "StatisticsType" AS ENUM ('count', 'sum', 'avg', 'min_max', 'group_by');
      EXCEPTION
        WHEN duplicate_object THEN 
          RAISE NOTICE 'StatisticsType 枚举已存在，跳过创建';
      END $$;
    `;

    // 3. 添加基础统计字段（如果不存在）
    if (!existingColumnNames.includes('isStatisticsEnabled')) {
      console.log('🔧 添加基础统计字段...');
      await db.$executeRaw`
        ALTER TABLE "FieldConfig" 
        ADD COLUMN "isStatisticsEnabled" BOOLEAN NOT NULL DEFAULT false,
        ADD COLUMN "statisticsConfig" JSONB,
        ADD COLUMN "statisticsDisplayName" VARCHAR(100),
        ADD COLUMN "statisticsOrder" INTEGER NOT NULL DEFAULT 0,
        ADD COLUMN "statisticsType" "StatisticsType" NOT NULL DEFAULT 'count';
      `;
    } else {
      console.log('✅ 基础统计字段已存在');
    }

    // 4. 添加统计排序字段（新增）
    if (!existingColumnNames.includes('statisticsSortOrder')) {
      console.log('🔧 添加统计排序配置字段...');
      await db.$executeRaw`
        ALTER TABLE "FieldConfig" 
        ADD COLUMN "statisticsSortOrder" VARCHAR(10) NOT NULL DEFAULT 'desc';
      `;
      
      // 添加检查约束，确保只能是 'asc' 或 'desc'
      await db.$executeRaw`
        ALTER TABLE "FieldConfig" 
        ADD CONSTRAINT "FieldConfig_statisticsSortOrder_check" 
        CHECK ("statisticsSortOrder" IN ('asc', 'desc'));
      `;
      
      console.log('✅ 统计排序字段添加成功');
    } else {
      console.log('✅ 统计排序字段已存在');
    }

    // 5. 创建索引
    console.log('🔧 创建索引...');
    await db.$executeRaw`
      CREATE INDEX IF NOT EXISTS "FieldConfig_isStatisticsEnabled_idx" ON "FieldConfig"("isStatisticsEnabled");
    `;
    await db.$executeRaw`
      CREATE INDEX IF NOT EXISTS "FieldConfig_statisticsOrder_idx" ON "FieldConfig"("statisticsOrder");
    `;

    // 6. 验证字段添加结果
    console.log('🔍 验证字段添加结果...');
    const verifyResult = await db.$queryRaw`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'FieldConfig' 
      AND column_name IN (
        'isStatisticsEnabled', 
        'statisticsOrder', 
        'statisticsType', 
        'statisticsDisplayName', 
        'statisticsConfig',
        'statisticsSortOrder'
      )
      ORDER BY column_name
    ` as any[];

    console.log('\n📋 当前统计相关字段:');
    verifyResult.forEach((col: any) => {
      console.log(`   ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable}, default: ${col.column_default})`);
    });

    console.log('\n✅ 统计排序配置字段添加完成！');
    console.log('\n📝 新增功能:');
    console.log('   - statisticsSortOrder: 控制统计结果的排序方向 (asc/desc)');
    console.log('   - 默认值为 "desc" (倒序)，与当前行为保持一致');

  } catch (error) {
    console.error('❌ 添加统计排序配置字段失败:', error);
    throw error;
  } finally {
    await db.$disconnect();
  }
}

// 执行脚本
if (require.main === module) {
  addStatisticsSortConfig()
    .then(() => {
      console.log('\n🎉 脚本执行完成！');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 脚本执行失败:', error);
      process.exit(1);
    });
}

export { addStatisticsSortConfig };
