#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

async function testFilterLinkage() {
  console.log('🔍 测试筛选器联动逻辑...');
  
  try {
    // 1. 测试场景：us_class 数据库的器械类别筛选
    console.log('\n📊 1. 测试 us_class 数据库的筛选联动...');
    
    // 获取器械类别为 "2" 的总数
    const class2Total = await db.uSClass.count({
      where: { deviceclass: '2' }
    });
    console.log(`器械类别为 "2" 的总记录数: ${class2Total}`);
    
    // 获取器械类别为 "2" 的数据中，第三方标志的分布
    const class2ThirdParty = await db.uSClass.groupBy({
      by: ['thirdpartyflag'],
      where: { deviceclass: '2' },
      _count: { thirdpartyflag: true },
      orderBy: { _count: { thirdpartyflag: 'desc' } }
    });

    console.log('器械类别为 "2" 的数据中，第三方标志分布:');
    class2ThirdParty.forEach(item => {
      const value = item.thirdpartyflag || 'N/A';
      console.log(`  ${value}: ${item._count.thirdpartyflag} 条`);
    });

    // 2. 获取全库的第三方标志分布（对比用）
    console.log('\n📊 2. 全库第三方标志分布（对比用）:');
    const allThirdParty = await db.uSClass.groupBy({
      by: ['thirdpartyflag'],
      _count: { thirdpartyflag: true },
      orderBy: { _count: { thirdpartyflag: 'desc' } }
    });

    allThirdParty.forEach(item => {
      const value = item.thirdpartyflag || 'N/A';
      console.log(`  ${value}: ${item._count.thirdpartyflag} 条`);
    });
    
    // 3. 测试 dynamic-counts API
    console.log('\n🌐 3. 测试 dynamic-counts API...');
    
    try {
      // 模拟前端请求：选择了 deviceclass = "2"，查询 thirdpartyflag 的动态计数
      const filters = { deviceclass: ['2'] };
      const params = new URLSearchParams({
        field: 'thirdpartyflag',
        filters: JSON.stringify(filters)
      });

      console.log(`请求 URL: /api/meta/us_class/dynamic-counts?${params.toString()}`);

      const response = await fetch(`http://localhost:3000/api/meta/us_class/dynamic-counts?${params.toString()}`);
      const result = await response.json();
      
      if (result.success) {
        console.log('API 返回的动态计数:');
        result.data.forEach((item: any) => {
          console.log(`  ${item.value}: ${item.count} 条`);
        });
        
        // 验证 API 结果是否与直接查询一致
        console.log('\n✅ 验证结果:');
        const apiTotal = result.data.reduce((sum: number, item: any) => sum + item.count, 0);
        console.log(`API 计数总和: ${apiTotal}`);
        console.log(`直接查询总数: ${class2Total}`);
        console.log(`结果一致: ${apiTotal === class2Total ? '✅' : '❌'}`);
        
      } else {
        console.error('❌ API 请求失败:', result.error);
      }
    } catch (error) {
      console.error('❌ API 请求异常:', error);
    }
    
    // 4. 测试另一个筛选场景
    console.log('\n📊 4. 测试另一个筛选场景...');
    
    // 选择 deviceclass = "2" 和 thirdpartyflag = "N"，查看其他字段的分布
    const doubleFiltered = await db.uSClass.count({
      where: {
        deviceclass: '2',
        thirdpartyflag: 'N'
      }
    });
    console.log(`同时满足 deviceclass="2" 和 thirdpartyflag="N" 的记录数: ${doubleFiltered}`);

    // 在双重筛选条件下，查看 reviewcode 字段的分布
    const reviewcodeDistribution = await db.uSClass.groupBy({
      by: ['reviewcode'],
      where: {
        deviceclass: '2',
        thirdpartyflag: 'N'
      },
      _count: { reviewcode: true },
      orderBy: { _count: { reviewcode: 'desc' } },
      take: 10
    });

    console.log('在 deviceclass="2" 和 thirdpartyflag="N" 条件下，reviewcode 字段分布:');
    reviewcodeDistribution.forEach(item => {
      const value = item.reviewcode || 'N/A';
      console.log(`  ${value}: ${item._count.reviewcode} 条`);
    });
    
    // 5. 测试 dynamic-counts API 的双重筛选
    console.log('\n🌐 5. 测试双重筛选的 dynamic-counts API...');
    
    try {
      const doubleFilters = { deviceclass: ['2'], thirdpartyflag: ['N'] };
      const doubleParams = new URLSearchParams({
        field: 'reviewcode',
        filters: JSON.stringify(doubleFilters)
      });

      console.log(`请求 URL: /api/meta/us_class/dynamic-counts?${doubleParams.toString()}`);

      const doubleResponse = await fetch(`http://localhost:3000/api/meta/us_class/dynamic-counts?${doubleParams.toString()}`);
      const doubleResult = await doubleResponse.json();

      if (doubleResult.success) {
        console.log('双重筛选 API 返回的动态计数:');
        doubleResult.data.slice(0, 10).forEach((item: any) => {
          console.log(`  ${item.value}: ${item.count} 条`);
        });

        const apiDoubleTotal = doubleResult.data.reduce((sum: number, item: any) => sum + item.count, 0);
        console.log(`API 双重筛选计数总和: ${apiDoubleTotal}`);
        console.log(`直接查询双重筛选总数: ${doubleFiltered}`);
        console.log(`双重筛选结果一致: ${apiDoubleTotal === doubleFiltered ? '✅' : '❌'}`);

      } else {
        console.error('❌ 双重筛选 API 请求失败:', doubleResult.error);
      }
    } catch (error) {
      console.error('❌ 双重筛选 API 请求异常:', error);
    }
    
    console.log('\n🎯 测试完成！');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  } finally {
    await db.$disconnect();
  }
}

// 运行测试
testFilterLinkage().catch(console.error);
