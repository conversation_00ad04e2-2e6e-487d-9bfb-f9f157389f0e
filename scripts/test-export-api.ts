#!/usr/bin/env tsx

import { getDatabaseConfig } from '../src/lib/configCache';

/**
 * 测试导出API逻辑
 */

async function testExportAPI() {
  console.log('🧪 测试导出API逻辑...\n');

  try {
    // 模拟导出API的字段选择逻辑
    console.log('📋 测试us_pmn导出字段选择...');
    const usPmnConfig = await getDatabaseConfig('us_pmn');
    
    // 使用与导出API相同的逻辑
    const usPmnExportableFields = usPmnConfig.fields
      ?.filter(f => f.isExportable !== false)
      ?.sort((a, b) => (a.exportOrder || a.listOrder || 0) - (b.exportOrder || b.listOrder || 0)) || [];
    
    console.log(`  总字段数: ${usPmnConfig.fields.length}`);
    console.log(`  可导出字段数: ${usPmnExportableFields.length}`);
    console.log('  导出字段列表 (前10个):');
    usPmnExportableFields.slice(0, 10).forEach((field, index) => {
      const displayName = field.exportDisplayName || field.displayName;
      console.log(`    ${index + 1}. ${field.fieldName} -> "${displayName}" (顺序: ${field.exportOrder || 0})`);
    });

    console.log('\n📋 测试us_class导出字段选择...');
    const usClassConfig = await getDatabaseConfig('us_class');
    
    const usClassExportableFields = usClassConfig.fields
      ?.filter(f => f.isExportable !== false)
      ?.sort((a, b) => (a.exportOrder || a.listOrder || 0) - (b.exportOrder || b.listOrder || 0)) || [];
    
    console.log(`  总字段数: ${usClassConfig.fields.length}`);
    console.log(`  可导出字段数: ${usClassExportableFields.length}`);
    console.log('  导出字段列表:');
    usClassExportableFields.forEach((field, index) => {
      const displayName = field.exportDisplayName || field.displayName;
      console.log(`    ${index + 1}. ${field.fieldName} -> "${displayName}" (顺序: ${field.exportOrder || 0})`);
    });

    // 检查不可导出的字段
    console.log('\n❌ 不可导出的字段:');
    const usPmnNonExportable = usPmnConfig.fields?.filter(f => f.isExportable === false) || [];
    const usClassNonExportable = usClassConfig.fields?.filter(f => f.isExportable === false) || [];
    
    if (usPmnNonExportable.length > 0) {
      console.log(`  us_pmn (${usPmnNonExportable.length}个):`);
      usPmnNonExportable.forEach(field => {
        console.log(`    - ${field.fieldName} (${field.displayName})`);
      });
    }
    
    if (usClassNonExportable.length > 0) {
      console.log(`  us_class (${usClassNonExportable.length}个):`);
      usClassNonExportable.forEach(field => {
        console.log(`    - ${field.fieldName} (${field.displayName})`);
      });
    }
    
    if (usPmnNonExportable.length === 0 && usClassNonExportable.length === 0) {
      console.log('  所有字段都可导出');
    }

    console.log('\n✅ 导出API逻辑测试完成！');
    console.log('\n📋 总结:');
    console.log(`  - us_pmn: ${usPmnExportableFields.length} 个可导出字段`);
    console.log(`  - us_class: ${usClassExportableFields.length} 个可导出字段`);
    console.log('  - 字段按 exportOrder 正确排序');
    console.log('  - 支持自定义导出列名 (exportDisplayName)');
    
    console.log('\n🚀 现在可以测试实际的导出API:');
    console.log('  curl "http://localhost:3000/api/export/us_pmn?format=csv&limit=5"');
    console.log('  curl "http://localhost:3000/api/export/us_class?format=excel&limit=5"');

  } catch (error) {
    console.error('❌ 测试过程中出错:', error);
  }
}

// 运行测试
if (require.main === module) {
  testExportAPI();
}
