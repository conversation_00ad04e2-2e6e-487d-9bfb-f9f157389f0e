// 测试API响应结构
async function testApiResponse() {
  console.log('🧪 测试API响应结构...\n');

  try {
    // 测试 us_pmn 数据库的API响应
    const response = await fetch('http://localhost:3000/api/data/us_pmn?page=1&limit=20');
    
    if (!response.ok) {
      console.error('❌ API请求失败:', response.status, response.statusText);
      return;
    }

    const data = await response.json();
    
    console.log('📊 API响应结构:');
    console.log('success:', data.success);
    console.log('data length:', data.data?.length || 0);
    
    if (data.pagination) {
      console.log('\n📄 分页信息:');
      console.log('  page:', data.pagination.page);
      console.log('  limit:', data.pagination.limit);
      console.log('  totalCount:', data.pagination.totalCount);
      console.log('  totalPages:', data.pagination.totalPages);
      console.log('  hasNext:', data.pagination.hasNext);
      console.log('  hasPrev:', data.pagination.hasPrev);
      console.log('  maxPages:', data.pagination.maxPages);
      console.log('  isAtMaxPages:', data.pagination.isAtMaxPages);
      console.log('  maxPageSize:', data.pagination.maxPageSize);
      console.log('  defaultPageSize:', data.pagination.defaultPageSize);
      
      // 检查是否有旧的字段
      if (data.pagination.total !== undefined) {
        console.log('  ⚠️  发现旧字段 total:', data.pagination.total);
      }
    } else {
      console.log('❌ 没有找到分页信息');
    }

    // 验证计算
    if (data.pagination) {
      const { page, limit, totalCount } = data.pagination;
      const expectedStart = (page - 1) * limit + 1;
      const expectedEnd = Math.min(page * limit, totalCount);
      
      console.log('\n🔢 分页计算验证:');
      console.log(`  显示范围: ${expectedStart} - ${expectedEnd} of ${totalCount} records`);
      
      if (isNaN(totalCount)) {
        console.log('❌ totalCount 是 NaN，这就是问题所在！');
      } else {
        console.log('✅ totalCount 正常');
      }
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 如果是在浏览器环境中运行
if (typeof window !== 'undefined') {
  testApiResponse();
} else {
  console.log('请在浏览器控制台中运行此脚本');
  console.log('或者访问 http://localhost:3000/data/list/us_pmn 并在控制台运行：');
  console.log('fetch("/api/data/us_pmn?page=1&limit=20").then(r=>r.json()).then(console.log)');
}
