#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 直接为 FieldConfig 表添加统计配置字段
 * 只专注于 us_class 的统计功能，不影响其他表
 */

async function addStatisticsFields() {
  console.log('📊 为 FieldConfig 表添加统计配置字段...');

  try {
    // 检查字段是否已存在
    const result = await db.$queryRaw`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'FieldConfig' 
      AND column_name IN ('isStatisticsEnabled', 'statisticsOrder', 'statisticsType', 'statisticsDisplayName', 'statisticsConfig')
    ` as any[];

    if (result.length > 0) {
      console.log('⚠️  统计字段已存在，跳过创建');
      console.log('   已存在的字段:', result.map((r: any) => r.column_name));
      return;
    }

    console.log('🔧 创建 StatisticsType 枚举...');
    await db.$executeRaw`
      DO $$ BEGIN
        CREATE TYPE "StatisticsType" AS ENUM ('count', 'sum', 'avg', 'min_max', 'group_by');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `;

    console.log('🔧 添加统计配置字段...');
    await db.$executeRaw`
      ALTER TABLE "FieldConfig" 
      ADD COLUMN IF NOT EXISTS "isStatisticsEnabled" BOOLEAN NOT NULL DEFAULT false,
      ADD COLUMN IF NOT EXISTS "statisticsConfig" JSONB,
      ADD COLUMN IF NOT EXISTS "statisticsDisplayName" VARCHAR(100),
      ADD COLUMN IF NOT EXISTS "statisticsOrder" INTEGER NOT NULL DEFAULT 0,
      ADD COLUMN IF NOT EXISTS "statisticsType" "StatisticsType" NOT NULL DEFAULT 'count';
    `;

    console.log('🔧 创建索引...');
    await db.$executeRaw`
      CREATE INDEX IF NOT EXISTS "FieldConfig_isStatisticsEnabled_idx" ON "FieldConfig"("isStatisticsEnabled");
    `;

    console.log('✅ 统计配置字段添加完成');

    // 验证字段是否添加成功
    const verifyResult = await db.$queryRaw`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'FieldConfig' 
      AND column_name IN ('isStatisticsEnabled', 'statisticsOrder', 'statisticsType', 'statisticsDisplayName', 'statisticsConfig')
      ORDER BY column_name
    ` as any[];

    console.log('\n📋 验证结果:');
    verifyResult.forEach((col: any) => {
      console.log(`   ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable}, default: ${col.column_default})`);
    });

  } catch (error) {
    console.error('❌ 添加统计字段失败:', error);
    throw error;
  } finally {
    await db.$disconnect();
  }
}

// 执行添加
addStatisticsFields()
  .then(() => {
    console.log('\n✨ 统计字段添加完成，现在可以配置 us_class 的统计功能了');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 添加失败:', error);
    process.exit(1);
  });
