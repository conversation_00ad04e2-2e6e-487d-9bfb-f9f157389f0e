#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 检查数据库配置中的 exportConfig 字段
 */

async function checkExportConfig() {
  console.log('🔍 检查数据库配置中的 exportConfig 字段...\n');

  try {
    const configs = await db.databaseConfig.findMany({
      where: { isActive: true },
      select: {
        code: true,
        name: true,
        exportConfig: true,
      },
      orderBy: { code: 'asc' },
    });

    console.log(`📋 找到 ${configs.length} 个数据库配置:\n`);

    configs.forEach(config => {
      console.log(`🔧 ${config.code}:`);
      console.log(`   名称: ${config.name}`);
      console.log(`   exportConfig: ${JSON.stringify(config.exportConfig, null, 2)}`);
      
      if (config.exportConfig && typeof config.exportConfig === 'object') {
        const exportConfig = config.exportConfig as any;
        if (exportConfig.icon) {
          console.log(`   图标: ${exportConfig.icon}`);
        } else {
          console.log(`   ⚠️  没有图标配置`);
        }
      } else {
        console.log(`   ⚠️  exportConfig 为空或格式错误`);
      }
      console.log('   ---\n');
    });

  } catch (error) {
    console.error('❌ 检查失败:', error);
  }
}

// 执行检查
if (require.main === module) {
  checkExportConfig()
    .then(() => {
      console.log('✨ 检查完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 检查失败:', error);
      process.exit(1);
    });
}

export { checkExportConfig };
