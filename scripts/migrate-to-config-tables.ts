#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 优化现有配置表的脚本
 * 1. 补充现有数据库配置的缺失字段（图标、描述等）
 * 2. 优化分类名称
 * 3. 不删除或清空现有数据，只做增量更新
 */

// 现有配置的补充信息
const configUpdates = [
  {
    code: 'us_class',
    updates: {
      category: 'Regulation', // 从 Regular 改为 Regulation
      description: 'FDA医疗器械分类数据库，包含产品代码、器械类别等信息',
      icon: '🇺🇸',
      sortOrder: 1,
    }
  },
  {
    code: 'us_pmn',
    updates: {
      category: 'Regulation', // 从 Marketed 改为 Regulation
      description: '美国FDA PMN(510k)医疗器械审批信息',
      icon: '🇺🇸',
      sortOrder: 2,
    }
  },
];

async function optimizeExistingConfigs() {
  console.log('🔧 开始优化现有配置表...\n');

  try {
    // 1. 更新现有配置，补充缺失字段
    console.log('📝 1. 补充现有数据库配置的缺失字段...');

    for (const configUpdate of configUpdates) {
      const existing = await db.databaseConfig.findUnique({
        where: { code: configUpdate.code }
      });

      if (existing) {
        // 更新现有配置，只补充缺失的字段
        await db.databaseConfig.update({
          where: { code: configUpdate.code },
          data: {
            category: configUpdate.updates.category,
            description: configUpdate.updates.description,
            sortOrder: configUpdate.updates.sortOrder,
            // 将图标信息存储在 exportConfig 中
            exportConfig: {
              icon: configUpdate.updates.icon,
              ...(existing.exportConfig as any || {})
            }
          }
        });
        console.log(`   ✅ 更新配置: ${configUpdate.code}`);
      } else {
        console.log(`   ⚠️  配置不存在: ${configUpdate.code}`);
      }
    }

    // 2. 检查字段配置完整性
    console.log('\n🔧 2. 检查字段配置完整性...');
    
    const activeDatabases = await db.databaseConfig.findMany({
      where: { isActive: true },
      select: { code: true, name: true }
    });

    for (const dbConfig of activeDatabases) {
      const fieldCount = await db.fieldConfig.count({
        where: {
          databaseCode: dbConfig.code,
          isActive: true
        }
      });

      console.log(`   📊 ${dbConfig.code}: ${fieldCount} 个字段配置`);
      
      if (fieldCount === 0) {
        console.log(`   ⚠️  ${dbConfig.code} 没有字段配置，需要手动添加`);
      }
    }

    // 3. 验证配置完整性
    console.log('\n✅ 3. 验证配置完整性...');
    
    const allConfigs = await db.databaseConfig.findMany({
      where: { isActive: true },
      select: {
        code: true,
        name: true,
        category: true,
        description: true,
        accessLevel: true,
        tableName: true,
        modelName: true,
        exportConfig: true,
        sortOrder: true
      },
      orderBy: { sortOrder: 'asc' }
    });

    console.log(`\n📋 找到 ${allConfigs.length} 个活跃的数据库配置:`);
    
    allConfigs.forEach(config => {
      const icon = (config.exportConfig as any)?.icon || '📊';
      console.log(`\n   ${icon} ${config.code}`);
      console.log(`      名称: ${config.name}`);
      console.log(`      分类: ${config.category}`);
      console.log(`      访问级别: ${config.accessLevel}`);
      console.log(`      表名: ${config.tableName || 'N/A'}`);
      console.log(`      模型名: ${config.modelName || 'N/A'}`);
      console.log(`      描述: ${config.description || 'N/A'}`);
    });

    console.log('\n🎉 配置优化完成！');
    console.log('\n📝 下一步：');
    console.log('   1. 清理硬编码配置');
    console.log('   2. 删除假数据文件');
    console.log('   3. 优化配置获取逻辑');
    console.log('   4. 清除缓存并测试');

  } catch (error) {
    console.error('❌ 优化失败:', error);
    throw error;
  }
}

// 执行优化
if (require.main === module) {
  optimizeExistingConfigs()
    .then(() => {
      console.log('\n✨ 优化完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 优化失败:', error);
      process.exit(1);
    });
}

export { optimizeExistingConfigs };
