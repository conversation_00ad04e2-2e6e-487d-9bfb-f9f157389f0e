#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 为 DatabaseConfig 表添加导出条数限制配置
 * 注意：只添加字段和更新配置，不删除任何现有数据
 */

async function addExportLimitConfig() {
  console.log('🔧 为 DatabaseConfig 表添加导出条数限制配置...\n');

  try {
    // 1. 添加导出配置字段
    console.log('📋 添加导出配置字段...');
    
    // 添加 maxExportLimit 字段 - 最大导出条数
    try {
      await db.$executeRaw`
        ALTER TABLE "DatabaseConfig" 
        ADD COLUMN IF NOT EXISTS "maxExportLimit" INTEGER DEFAULT 10000;
      `;
      console.log('  ✅ 添加 maxExportLimit 字段成功');
    } catch (error) {
      console.log('  ℹ️  maxExportLimit 字段可能已存在');
    }

    // 添加 defaultExportLimit 字段 - 默认导出条数
    try {
      await db.$executeRaw`
        ALTER TABLE "DatabaseConfig" 
        ADD COLUMN IF NOT EXISTS "defaultExportLimit" INTEGER DEFAULT 1000;
      `;
      console.log('  ✅ 添加 defaultExportLimit 字段成功');
    } catch (error) {
      console.log('  ℹ️  defaultExportLimit 字段可能已存在');
    }

    // 添加 exportConfig 字段 - 导出相关的JSON配置
    try {
      await db.$executeRaw`
        ALTER TABLE "DatabaseConfig" 
        ADD COLUMN IF NOT EXISTS "exportConfig" JSONB;
      `;
      console.log('  ✅ 添加 exportConfig 字段成功');
    } catch (error) {
      console.log('  ℹ️  exportConfig 字段可能已存在');
    }

    // 2. 验证字段添加结果
    console.log('\n🔍 验证字段添加结果...');
    
    const tableInfo = await db.$queryRaw`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'DatabaseConfig' 
      AND column_name IN ('maxExportLimit', 'defaultExportLimit', 'exportConfig')
      ORDER BY column_name;
    ` as any[];

    if (tableInfo.length > 0) {
      console.log('  📋 新增字段信息:');
      tableInfo.forEach((col: any) => {
        console.log(`    - ${col.column_name}: ${col.data_type} (默认值: ${col.column_default || 'NULL'})`);
      });
    } else {
      console.log('  ❌ 未找到新增字段，可能添加失败');
    }

    // 3. 更新现有数据库的导出配置
    console.log('\n🔧 更新现有数据库的导出配置...');
    
    // 更新 us_pmn 配置
    try {
      const usPmnResult = await db.$executeRaw`
        UPDATE "DatabaseConfig" 
        SET 
          "maxExportLimit" = 50000,
          "defaultExportLimit" = 5000,
          "exportConfig" = '{"formats": ["csv", "excel", "json"], "allowLargeExport": true, "description": "美国PMN数据导出配置"}'::jsonb
        WHERE "code" = 'us_pmn';
      `;
      console.log('  ✅ us_pmn 导出配置已更新 (最大: 50,000, 默认: 5,000)');
    } catch (error) {
      console.error('  ❌ 更新us_pmn配置失败:', error);
    }

    // 更新 us_class 配置
    try {
      const usClassResult = await db.$executeRaw`
        UPDATE "DatabaseConfig" 
        SET 
          "maxExportLimit" = 20000,
          "defaultExportLimit" = 2000,
          "exportConfig" = '{"formats": ["csv", "excel", "json"], "allowLargeExport": false, "description": "美国器械分类数据导出配置"}'::jsonb
        WHERE "code" = 'us_class';
      `;
      console.log('  ✅ us_class 导出配置已更新 (最大: 20,000, 默认: 2,000)');
    } catch (error) {
      console.error('  ❌ 更新us_class配置失败:', error);
    }

    // 4. 验证配置更新结果
    console.log('\n📊 验证配置更新结果...');
    
    const configs = await db.$queryRaw`
      SELECT 
        "code", 
        "name", 
        "maxExportLimit", 
        "defaultExportLimit", 
        "exportConfig"
      FROM "DatabaseConfig" 
      WHERE "code" IN ('us_pmn', 'us_class')
      ORDER BY "code";
    ` as any[];

    if (configs.length > 0) {
      console.log('  📋 数据库导出配置:');
      configs.forEach((config: any) => {
        console.log(`\n    ${config.code} (${config.name}):`);
        console.log(`      最大导出: ${config.maxExportLimit || '未设置'} 条`);
        console.log(`      默认导出: ${config.defaultExportLimit || '未设置'} 条`);
        if (config.exportConfig) {
          const exportConfig = typeof config.exportConfig === 'string' 
            ? JSON.parse(config.exportConfig) 
            : config.exportConfig;
          console.log(`      支持格式: ${exportConfig.formats?.join(', ') || '未设置'}`);
          console.log(`      允许大量导出: ${exportConfig.allowLargeExport ? '是' : '否'}`);
        }
      });
    }

    console.log('\n✅ 导出条数配置添加完成！');
    console.log('\n📋 配置说明:');
    console.log('  - maxExportLimit: 单次导出的最大条数限制');
    console.log('  - defaultExportLimit: 未指定limit时的默认导出条数');
    console.log('  - exportConfig: 导出相关的JSON配置（格式、权限等）');
    console.log('\n💡 使用方式:');
    console.log('  - API会自动使用这些配置限制导出条数');
    console.log('  - 可以通过修改数据库配置调整导出限制');

  } catch (error) {
    console.error('❌ 添加导出配置时出错:', error);
  } finally {
    await db.$disconnect();
  }
}

// 运行脚本
if (require.main === module) {
  addExportLimitConfig();
}
