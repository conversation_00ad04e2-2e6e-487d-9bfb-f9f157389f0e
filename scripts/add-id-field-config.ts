import { db } from '../src/lib/prisma';

/**
 * 为所有数据库添加id字段的fieldConfig配置
 * 设置为不可见，这样可以统一管理所有字段的显示控制
 */

async function addIdFieldConfig() {
  console.log('🔧 开始为所有数据库添加id字段配置...');

  try {
    // 1. 获取所有活跃的数据库配置
    const databases = await db.databaseConfig.findMany({
      where: { isActive: true },
      select: { code: true, name: true }
    });

    console.log(`📋 找到 ${databases.length} 个活跃数据库:`);
    databases.forEach(database => {
      console.log(`   • ${database.code} (${database.name})`);
    });

    // 2. 为每个数据库添加id字段配置
    let addedCount = 0;
    let skippedCount = 0;

    for (const database of databases) {
      try {
        // 检查是否已存在id字段配置
        const existingConfig = await db.fieldConfig.findFirst({
          where: {
            databaseCode: database.code,
            fieldName: 'id',
            isActive: true
          }
        });

        if (existingConfig) {
          console.log(`  ⚠️  ${database.code} - id字段配置已存在，跳过`);
          skippedCount++;
          continue;
        }

        // 创建id字段配置
        await db.fieldConfig.create({
          data: {
            databaseCode: database.code,
            fieldName: 'id',
            displayName: 'ID',
            fieldType: 'text',
            isVisible: false,        // 设置为不可见
            isSearchable: false,     // 不可搜索
            isFilterable: false,     // 不可筛选
            isSortable: true,        // 可排序（用于默认排序）
            sortOrder: 0,            // 最高优先级（用于默认排序）
            listOrder: 0,            // 不在列表中显示
            detailOrder: 0,          // 不在详情中显示
            searchType: 'exact',
            filterType: 'input',
            isActive: true,
            todetail: false          // 不作为详情页链接
          }
        });

        console.log(`  ✅ ${database.code} - id字段配置已添加`);
        addedCount++;

      } catch (error) {
        console.error(`  ❌ ${database.code} - 添加id字段配置失败:`, error);
      }
    }

    // 3. 显示结果统计
    console.log('\n📊 添加结果统计:');
    console.log(`   ✅ 成功添加: ${addedCount} 个`);
    console.log(`   ⚠️  已存在跳过: ${skippedCount} 个`);
    console.log(`   📋 总数据库: ${databases.length} 个`);

    // 4. 验证添加结果
    console.log('\n🔍 验证添加结果...');
    const idConfigs = await db.fieldConfig.findMany({
      where: {
        fieldName: 'id',
        isActive: true
      },
      select: {
        databaseCode: true,
        displayName: true,
        isVisible: true,
        isSortable: true,
        sortOrder: true
      },
      orderBy: { databaseCode: 'asc' }
    });

    console.log(`✅ 找到 ${idConfigs.length} 个id字段配置:`);
    idConfigs.forEach(config => {
      const visibility = config.isVisible ? '👁️ 可见' : '🙈 隐藏';
      const sortable = config.isSortable ? '🔄 可排序' : '❌ 不可排序';
      console.log(`   • ${config.databaseCode}: ${config.displayName} - ${visibility}, ${sortable}, 优先级: ${config.sortOrder}`);
    });

    console.log('\n✅ id字段配置添加完成！');
    console.log('\n💡 配置说明:');
    console.log('   • isVisible: false - id字段不在列表页显示');
    console.log('   • isSortable: true - 可用于默认排序');
    console.log('   • sortOrder: 0 - 最高优先级，适合作为默认排序字段');
    console.log('   • todetail: false - 不作为详情页链接');

  } catch (error) {
    console.error('❌ 添加id字段配置过程中发生错误:', error);
  } finally {
    await db.$disconnect();
  }
}

// 执行脚本
addIdFieldConfig()
  .then(() => {
    console.log('\n✨ 脚本执行完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 脚本执行失败:', error);
    process.exit(1);
  });
