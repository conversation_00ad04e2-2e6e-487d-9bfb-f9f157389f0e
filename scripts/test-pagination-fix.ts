// 测试翻页修复
async function testPaginationFix() {
  console.log('🧪 测试翻页修复...\n');

  try {
    // 1. 测试API直接调用
    console.log('📡 测试API直接调用...');
    const apiResponse = await fetch('http://localhost:3000/api/data/us_pmn?page=1&limit=20');
    
    if (!apiResponse.ok) {
      console.error('❌ API请求失败:', apiResponse.status);
      return;
    }

    const apiData = await apiResponse.json();
    console.log('✅ API响应成功');
    console.log('  totalCount:', apiData.pagination?.totalCount);
    console.log('  totalPages:', apiData.pagination?.totalPages);
    console.log('  maxPages:', apiData.pagination?.maxPages);
    console.log('  isAtMaxPages:', apiData.pagination?.isAtMaxPages);

    // 2. 验证分页计算
    const { page, limit, totalCount } = apiData.pagination;
    const expectedStart = (page - 1) * limit + 1;
    const expectedEnd = Math.min(page * limit, totalCount);
    
    console.log('\n🔢 分页计算验证:');
    console.log(`  显示范围: ${expectedStart} - ${expectedEnd} of ${totalCount} records`);
    
    if (isNaN(totalCount)) {
      console.log('❌ totalCount 仍然是 NaN！');
    } else {
      console.log('✅ totalCount 正常:', totalCount);
    }

    // 3. 测试前端searchData函数（如果在浏览器环境）
    if (typeof window !== 'undefined') {
      console.log('\n🌐 测试前端searchData函数...');
      
      // 动态导入searchData函数
      const { searchData } = await import('../src/lib/api');
      
      const searchResponse = await searchData('us_pmn', 1, 20, {}, 'id', 'desc');
      console.log('✅ searchData响应成功');
      console.log('  totalCount:', searchResponse.pagination?.totalCount);
      console.log('  maxPages:', searchResponse.pagination?.maxPages);
    }

    // 4. 检查类型一致性
    console.log('\n🔍 检查响应字段:');
    const paginationFields = Object.keys(apiData.pagination || {});
    const expectedFields = [
      'page', 'limit', 'totalCount', 'totalPages', 
      'hasNext', 'hasPrev', 'maxPages', 'isAtMaxPages', 
      'maxPageSize', 'defaultPageSize'
    ];
    
    expectedFields.forEach(field => {
      if (paginationFields.includes(field)) {
        console.log(`  ✅ ${field}: ${apiData.pagination[field]}`);
      } else {
        console.log(`  ❌ 缺少字段: ${field}`);
      }
    });

    // 检查是否有旧字段
    if (apiData.pagination.total !== undefined) {
      console.log(`  ⚠️  发现旧字段 total: ${apiData.pagination.total}`);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 如果是在Node.js环境中运行
if (typeof window === 'undefined') {
  testPaginationFix();
} else {
  // 在浏览器中运行
  console.log('请在浏览器控制台中运行此函数');
}
