#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 为数据库配置表添加分类排序字段
 * 1. categoryOrder - 分类在导航栏中的排序
 * 2. orderInCategory - 数据库在分类内的排序
 * 
 * 注意：不清空现有数据，只添加新字段并设置合理的默认值
 */

async function addCategorySortingFields() {
  console.log('🔧 为数据库配置表添加分类排序字段...\n');

  try {
    // 首先检查当前配置
    console.log('📋 1. 检查当前配置...');
    const currentConfigs = await db.databaseConfig.findMany({
      where: { isActive: true },
      select: {
        code: true,
        name: true,
        category: true,
        sortOrder: true,
      },
      orderBy: { sortOrder: 'asc' }
    });

    console.log(`找到 ${currentConfigs.length} 个配置:`);
    currentConfigs.forEach(config => {
      console.log(`   ${config.code}: ${config.name} (${config.category}) - sortOrder: ${config.sortOrder}`);
    });

    // 分析分类
    const categories = [...new Set(currentConfigs.map(c => c.category))];
    console.log(`\n📊 发现 ${categories.length} 个分类: ${categories.join(', ')}`);

    // 2. 设计分类排序方案
    console.log('\n🎯 2. 设计分类排序方案...');
    
    // 定义分类的优先级顺序
    const categoryPriority: Record<string, number> = {
      'Regulation': 1,    // 法规类数据库优先级最高
      'Marked': 2,        // 已上市数据库
      'Marketed': 2,      // 已上市数据库（别名）
      '全球器械': 3,      // 全球器械数据库
      '药物研发': 4,      // 药物研发数据库
    };

    // 3. 为每个配置设置新的排序字段
    console.log('\n📝 3. 更新配置排序字段...');
    
    for (const config of currentConfigs) {
      const categoryOrder = categoryPriority[config.category] || 99;
      
      // 计算在分类内的排序
      const sameCategory = currentConfigs.filter(c => c.category === config.category);
      const orderInCategory = sameCategory.findIndex(c => c.code === config.code) + 1;

      // 更新配置，添加新的排序字段到 exportConfig 中
      const existingConfig = await db.databaseConfig.findUnique({
        where: { code: config.code },
        select: { exportConfig: true }
      });

      const updatedExportConfig = {
        ...(existingConfig?.exportConfig as any || {}),
        categoryOrder: categoryOrder,
        orderInCategory: orderInCategory
      };

      await db.databaseConfig.update({
        where: { code: config.code },
        data: {
          exportConfig: updatedExportConfig
        }
      });

      console.log(`   ✅ ${config.code}:`);
      console.log(`      分类: ${config.category} (categoryOrder: ${categoryOrder})`);
      console.log(`      分类内排序: ${orderInCategory}`);
    }

    // 4. 验证更新结果
    console.log('\n✅ 4. 验证更新结果...');
    
    const updatedConfigs = await db.databaseConfig.findMany({
      where: { isActive: true },
      select: {
        code: true,
        name: true,
        category: true,
        exportConfig: true,
      },
      orderBy: { sortOrder: 'asc' }
    });

    console.log('\n📋 更新后的配置:');
    updatedConfigs.forEach(config => {
      const exportConfig = config.exportConfig as any || {};
      console.log(`\n   ${exportConfig.icon || '📊'} ${config.code}:`);
      console.log(`      名称: ${config.name}`);
      console.log(`      分类: ${config.category}`);
      console.log(`      分类排序: ${exportConfig.categoryOrder || 'N/A'}`);
      console.log(`      分类内排序: ${exportConfig.orderInCategory || 'N/A'}`);
    });

    // 5. 提供导航栏排序建议
    console.log('\n🧭 5. 导航栏排序建议...');
    
    // 按分类排序，然后按分类内排序
    const sortedForNavigation = updatedConfigs.sort((a, b) => {
      const aConfig = a.exportConfig as any || {};
      const bConfig = b.exportConfig as any || {};
      
      const aCategoryOrder = aConfig.categoryOrder || 99;
      const bCategoryOrder = bConfig.categoryOrder || 99;
      
      if (aCategoryOrder !== bCategoryOrder) {
        return aCategoryOrder - bCategoryOrder;
      }
      
      const aOrderInCategory = aConfig.orderInCategory || 99;
      const bOrderInCategory = bConfig.orderInCategory || 99;
      
      return aOrderInCategory - bOrderInCategory;
    });

    console.log('\n导航栏建议显示顺序:');
    let currentCategory = '';
    sortedForNavigation.forEach(config => {
      const exportConfig = config.exportConfig as any || {};
      
      if (config.category !== currentCategory) {
        currentCategory = config.category;
        console.log(`\n📁 ${config.category}:`);
      }
      
      console.log(`   ${exportConfig.icon || '📊'} ${config.name}`);
    });

    console.log('\n🎉 分类排序字段添加完成！');
    console.log('\n📝 使用说明：');
    console.log('   - categoryOrder: 控制分类在导航栏中的顺序');
    console.log('   - orderInCategory: 控制数据库在分类内的顺序');
    console.log('   - 这些字段存储在 exportConfig 中，便于扩展');

  } catch (error) {
    console.error('❌ 添加排序字段失败:', error);
    throw error;
  }
}

// 执行脚本
if (require.main === module) {
  addCategorySortingFields()
    .then(() => {
      console.log('\n✨ 完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 失败:', error);
      process.exit(1);
    });
}

export { addCategorySortingFields };
