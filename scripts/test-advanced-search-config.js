const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testAdvancedSearchConfig() {
  try {
    console.log('🧪 测试高级搜索配置...\n');

    // 1. 直接从数据库测试字段配置
    console.log('1. 测试数据库字段配置...');

    const config = await prisma.fieldConfig.findMany({
      where: { databaseCode: 'us_pmn', isActive: true },
      select: {
        fieldName: true,
        displayName: true,
        isVisible: true,
        isSearchable: true,
        isFilterable: true,
        isAdvancedSearchable: true,
        isSortable: true
      },
      orderBy: { fieldName: 'asc' }
    });

    console.log(`✅ 成功获取 us_pmn 配置，包含 ${config.length} 个字段`);

    // 检查是否包含 isAdvancedSearchable 字段
    const hasAdvancedSearchable = config.some(f =>
      f.hasOwnProperty('isAdvancedSearchable')
    );

    if (hasAdvancedSearchable) {
      console.log('✅ 数据库配置包含 isAdvancedSearchable 字段');
    } else {
      console.log('❌ 数据库配置不包含 isAdvancedSearchable 字段');
      return;
    }

    // 2. 测试不同类型的字段分类
    console.log('\n2. 测试字段分类...');

    const filterableFields = config.filter(f => f.isFilterable);
    const searchableFields = config.filter(f => f.isSearchable);
    const advancedSearchableFields = config.filter(f => f.isAdvancedSearchable);

    // 模拟前端逻辑：Advanced Search 可用字段
    const availableForAdvancedSearch = config.filter(f =>
      f.isAdvancedSearchable || f.isFilterable || f.isSearchable
    );
    
    console.log(`✅ 可筛选字段: ${filterableFields.length} 个`);
    console.log(`✅ 可搜索字段: ${searchableFields.length} 个`);
    console.log(`✅ 可高级搜索字段: ${advancedSearchableFields.length} 个`);
    console.log(`✅ Advanced Search 可用字段: ${availableForAdvancedSearch.length} 个`);

    // 3. 显示新增的高级搜索专用字段
    console.log('\n3. 新增的高级搜索专用字段:');
    
    const advancedOnlyFields = config.filter(f =>
      f.isAdvancedSearchable && !f.isFilterable && !f.isSearchable
    );
    
    if (advancedOnlyFields.length > 0) {
      console.log(`✅ 找到 ${advancedOnlyFields.length} 个仅在高级搜索中可用的字段:`);
      advancedOnlyFields.forEach(field => {
        console.log(`   - ${field.fieldName} (${field.displayName})`);
      });
    } else {
      console.log('ℹ️  没有找到仅在高级搜索中可用的字段');
    }

    // 4. 测试重要字段是否正确配置
    console.log('\n4. 测试重要字段配置...');
    
    const importantFields = ['city', 'country_code', 'postal_code', 'street1', 'street2', 'zip'];
    
    for (const fieldName of importantFields) {
      const field = config.find(f => f.fieldName === fieldName);
      if (field) {
        if (field.isAdvancedSearchable) {
          console.log(`✅ ${fieldName} 已配置为可高级搜索`);
        } else {
          console.log(`❌ ${fieldName} 未配置为可高级搜索`);
        }
      } else {
        console.log(`⚠️  ${fieldName} 字段不存在`);
      }
    }

    // 5. 模拟前端组件逻辑测试
    console.log('\n5. 模拟前端组件逻辑测试...');
    
    // 模拟 DatabasePageContent.tsx 中的逻辑
    const oldLogic = config.filter(f => f.isFilterable || f.isSearchable);
    const newLogic = config.filter(f => f.isAdvancedSearchable || f.isFilterable || f.isSearchable);
    
    console.log(`旧逻辑 (isFilterable || isSearchable): ${oldLogic.length} 个字段`);
    console.log(`新逻辑 (isAdvancedSearchable || isFilterable || isSearchable): ${newLogic.length} 个字段`);
    console.log(`新增可用字段: ${newLogic.length - oldLogic.length} 个`);
    
    // 显示新增的字段
    const newFields = newLogic.filter(newField => 
      !oldLogic.some(oldField => oldField.fieldName === newField.fieldName)
    );
    
    if (newFields.length > 0) {
      console.log('新增的可用字段:');
      newFields.forEach(field => {
        console.log(`   - ${field.fieldName} (${field.displayName})`);
      });
    }

    // 6. 测试其他数据库
    console.log('\n6. 测试其他数据库配置...');

    try {
      const usClassConfig = await prisma.fieldConfig.findMany({
        where: { databaseCode: 'us_class', isActive: true },
        select: { fieldName: true, isAdvancedSearchable: true }
      });
      const usClassAdvancedFields = usClassConfig.filter(f => f.isAdvancedSearchable);
      console.log(`✅ us_class 数据库: ${usClassAdvancedFields.length} 个可高级搜索字段`);
    } catch (error) {
      console.log('⚠️  us_class 数据库配置获取失败:', error.message);
    }

    console.log('\n🎉 高级搜索配置测试完成！');
    console.log('\n📋 总结:');
    console.log(`- 左侧Filter面板: ${filterableFields.length} 个可筛选字段`);
    console.log(`- Advanced Search: ${availableForAdvancedSearch.length} 个可用字段`);
    console.log(`- 新增专用字段: ${advancedOnlyFields.length} 个`);

  } catch (error) {
    console.error('❌ 测试失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 运行测试
if (require.main === module) {
  testAdvancedSearchConfig()
    .then(() => {
      console.log('\n✅ 测试脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ 测试脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { testAdvancedSearchConfig };
