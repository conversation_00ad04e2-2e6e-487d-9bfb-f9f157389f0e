#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 更新 us_pmn 数据库配置的 modelName
 */

async function updateUsPmnModelName() {
  console.log('🔧 更新 us_pmn 数据库配置的 modelName...');

  try {
    // 查找现有的 us_pmn 配置
    const existingConfig = await db.databaseConfig.findUnique({
      where: { code: 'us_pmn' },
      select: { id: true, code: true, name: true, modelName: true }
    });

    if (!existingConfig) {
      console.log('❌ 未找到 us_pmn 数据库配置');
      return;
    }

    console.log(`📋 当前配置:`);
    console.log(`   数据库: ${existingConfig.name} (${existingConfig.code})`);
    console.log(`   当前 modelName: ${existingConfig.modelName || 'N/A'}`);

    // 更新 modelName
    const updatedConfig = await db.databaseConfig.update({
      where: { code: 'us_pmn' },
      data: { 
        modelName: 'uSPremarketNotification',
        tableName: 'us_pmn'
      },
      select: { code: true, name: true, modelName: true, tableName: true }
    });

    console.log(`✅ 更新成功:`);
    console.log(`   数据库: ${updatedConfig.name} (${updatedConfig.code})`);
    console.log(`   新 modelName: ${updatedConfig.modelName}`);
    console.log(`   表名: ${updatedConfig.tableName}`);

  } catch (error) {
    console.error('❌ 更新失败:', error);
  } finally {
    await db.$disconnect();
  }
}

updateUsPmnModelName();
