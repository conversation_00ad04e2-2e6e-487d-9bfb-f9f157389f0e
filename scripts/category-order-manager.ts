#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 分类顺序管理器
 * 用于管理导航栏中分类的显示顺序
 */

interface CategoryOrderConfig {
  [categoryName: string]: number;
}

// 默认分类顺序配置
const DEFAULT_CATEGORY_ORDER: CategoryOrderConfig = {
  'Marketed': 100,     // 已上市产品数据
  'Regular': 200,      // 常规数据
  '参考数据': 300,      // 参考数据
  '全球器械': 400,      // 全球器械数据
  'Regulation': 500,   // 监管数据
  '药物研发': 600,      // 药物研发数据
};

/**
 * 显示当前分类顺序
 */
async function showCurrentOrder() {
  console.log('📋 当前分类顺序:\n');

  const configs = await db.databaseConfig.findMany({
    where: { isActive: true },
    select: {
      code: true,
      name: true,
      category: true,
      sortOrder: true,
    },
    orderBy: { sortOrder: 'asc' },
  });

  // 按分类分组
  const categoriesMap = new Map<string, any[]>();
  configs.forEach(config => {
    if (!categoriesMap.has(config.category)) {
      categoriesMap.set(config.category, []);
    }
    categoriesMap.get(config.category)!.push(config);
  });

  let index = 1;
  categoriesMap.forEach((databases, category) => {
    const minSortOrder = Math.min(...databases.map(db => db.sortOrder));
    console.log(`${index}. ${category} (sortOrder: ${minSortOrder})`);
    databases.forEach(db => {
      console.log(`   - ${db.code}: ${db.name}`);
    });
    console.log('');
    index++;
  });
}

/**
 * 设置分类顺序
 */
async function setCategoryOrder(categoryOrder: CategoryOrderConfig) {
  console.log('🔧 设置分类顺序...\n');

  const configs = await db.databaseConfig.findMany({
    where: { isActive: true },
    select: {
      id: true,
      code: true,
      name: true,
      category: true,
      sortOrder: true,
    },
  });

  console.log('📋 期望的分类顺序:');
  Object.entries(categoryOrder)
    .sort(([, a], [, b]) => a - b)
    .forEach(([category, order], index) => {
      console.log(`   ${index + 1}. ${category} (sortOrder: ${order})`);
    });
  console.log('');

  console.log('🔄 更新数据库配置...\n');

  for (const config of configs) {
    const newSortOrder = categoryOrder[config.category];
    
    if (newSortOrder !== undefined && config.sortOrder !== newSortOrder) {
      await db.databaseConfig.update({
        where: { id: config.id },
        data: { sortOrder: newSortOrder },
      });
      
      console.log(`   ✅ 更新 ${config.code} (${config.category}): ${config.sortOrder} → ${newSortOrder}`);
    } else if (newSortOrder !== undefined) {
      console.log(`   ⏭️  跳过 ${config.code} (${config.category}): sortOrder 已经是 ${config.sortOrder}`);
    } else {
      console.log(`   ⚠️  未知分类 ${config.code} (${config.category}): 保持 sortOrder ${config.sortOrder}`);
    }
  }

  console.log('\n✅ 分类顺序设置完成！\n');
}

/**
 * 交换两个分类的顺序
 */
async function swapCategoryOrder(category1: string, category2: string) {
  console.log(`🔄 交换分类顺序: ${category1} ↔ ${category2}\n`);

  // 获取两个分类的当前 sortOrder
  const configs = await db.databaseConfig.findMany({
    where: { 
      isActive: true,
      category: { in: [category1, category2] }
    },
    select: {
      id: true,
      code: true,
      category: true,
      sortOrder: true,
    },
  });

  const category1Configs = configs.filter(c => c.category === category1);
  const category2Configs = configs.filter(c => c.category === category2);

  if (category1Configs.length === 0) {
    console.log(`❌ 找不到分类: ${category1}`);
    return;
  }

  if (category2Configs.length === 0) {
    console.log(`❌ 找不到分类: ${category2}`);
    return;
  }

  const category1SortOrder = Math.min(...category1Configs.map(c => c.sortOrder));
  const category2SortOrder = Math.min(...category2Configs.map(c => c.sortOrder));

  // 交换 sortOrder
  for (const config of category1Configs) {
    await db.databaseConfig.update({
      where: { id: config.id },
      data: { sortOrder: category2SortOrder },
    });
  }

  for (const config of category2Configs) {
    await db.databaseConfig.update({
      where: { id: config.id },
      data: { sortOrder: category1SortOrder },
    });
  }

  console.log(`✅ 已交换 ${category1} 和 ${category2} 的顺序`);
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  try {
    switch (command) {
      case 'show':
        await showCurrentOrder();
        break;
        
      case 'set':
        await setCategoryOrder(DEFAULT_CATEGORY_ORDER);
        await showCurrentOrder();
        break;
        
      case 'swap':
        if (args.length < 3) {
          console.log('❌ 用法: npm run category-order swap <分类1> <分类2>');
          return;
        }
        await swapCategoryOrder(args[1], args[2]);
        await showCurrentOrder();
        break;
        
      case 'marketed-first':
        // 快捷命令：让 Marketed 排在 Regular 前面
        await setCategoryOrder({
          'Marketed': 100,
          'Regular': 200,
        });
        await showCurrentOrder();
        break;
        
      default:
        console.log('📖 分类顺序管理器使用说明:');
        console.log('');
        console.log('命令:');
        console.log('  show              - 显示当前分类顺序');
        console.log('  set               - 设置默认分类顺序');
        console.log('  swap <分类1> <分类2> - 交换两个分类的顺序');
        console.log('  marketed-first    - 让 Marketed 排在 Regular 前面');
        console.log('');
        console.log('示例:');
        console.log('  npx tsx scripts/category-order-manager.ts show');
        console.log('  npx tsx scripts/category-order-manager.ts set');
        console.log('  npx tsx scripts/category-order-manager.ts swap Marketed Regular');
        console.log('  npx tsx scripts/category-order-manager.ts marketed-first');
        break;
    }
  } catch (error) {
    console.error('❌ 操作失败:', error);
  } finally {
    await db.$disconnect();
  }
}

main();
