#!/usr/bin/env tsx

import { DynamicTableMappingService } from '../src/lib/dynamicTableMappingService';

const CACHE_COMMANDS = {
  status: '查看缓存状态',
  clear: '清空所有缓存',
  refresh: '刷新指定数据库配置',
  'refresh-all': '刷新所有数据库配置',
  test: '测试配置读取速度'
} as const;

async function showCacheStatus() {
  console.log('📊 缓存状态信息:');
  const cacheInfo = DynamicTableMappingService.getCacheInfo();
  
  console.log(`   缓存大小: ${cacheInfo.size} 个配置`);
  if (cacheInfo.size > 0) {
    console.log(`   缓存过期时间: ${new Date(cacheInfo.expiry).toLocaleString()}`);
    console.log(`   当前是否过期: ${cacheInfo.isExpired ? '是' : '否'}`);
    
    const timeUntilExpiry = cacheInfo.expiry - Date.now();
    if (timeUntilExpiry > 0) {
      console.log(`   距离过期: ${Math.round(timeUntilExpiry / 1000)}秒`);
    }
  } else {
    console.log('   缓存为空');
  }
}

async function clearCache() {
  console.log('🗑️ 清空缓存...');
  DynamicTableMappingService.clearCache();
  console.log('✅ 缓存已清空');
}

async function refreshDatabase(databaseCode: string) {
  console.log(`🔄 刷新数据库配置: ${databaseCode}`);
  
  // 先清空特定缓存（这里我们简单清空全部，实际可以优化为只清空特定项）
  DynamicTableMappingService.clearCache();
  
  // 重新加载配置
  const start = performance.now();
  const mapping = await DynamicTableMappingService.getTableMapping(databaseCode);
  const end = performance.now();
  
  if (mapping) {
    console.log(`✅ 配置刷新成功 (耗时: ${(end - start).toFixed(2)}ms)`);
    console.log(`   表名: ${mapping.tableName}`);
    console.log(`   模型名: ${mapping.modelName}`);
    console.log(`   显示名: ${mapping.displayName}`);
  } else {
    console.log(`❌ 配置刷新失败: 数据库代码 ${databaseCode} 不存在或配置不完整`);
  }
}

async function refreshAllDatabases() {
  console.log('🔄 刷新所有数据库配置...');
  
  DynamicTableMappingService.clearCache();
  
  try {
    const allCodes = await DynamicTableMappingService.getAllDatabaseCodes();
    console.log(`找到 ${allCodes.length} 个数据库配置`);
    
    let successCount = 0;
    for (const code of allCodes) {
      const mapping = await DynamicTableMappingService.getTableMapping(code);
      if (mapping) {
        console.log(`   ✅ ${code}: ${mapping.displayName}`);
        successCount++;
      } else {
        console.log(`   ❌ ${code}: 配置加载失败`);
      }
    }
    
    console.log(`\n📊 刷新完成: ${successCount}/${allCodes.length} 个配置成功加载`);
  } catch (error) {
    console.error(`❌ 刷新失败:`, error);
  }
}

async function testPerformance() {
  console.log('⚡ 性能测试...');
  
  const testCode = 'deviceCNImported';
  const testRounds = 10;
  
  // 确保缓存中有数据
  await DynamicTableMappingService.getTableMapping(testCode);
  
  const times: number[] = [];
  for (let i = 0; i < testRounds; i++) {
    const start = performance.now();
    await DynamicTableMappingService.getTableMapping(testCode);
    const end = performance.now();
    times.push(end - start);
  }
  
  const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
  console.log(`   平均响应时间: ${avgTime.toFixed(3)}ms (${testRounds} 次测试)`);
  console.log(`   最快: ${Math.min(...times).toFixed(3)}ms`);
  console.log(`   最慢: ${Math.max(...times).toFixed(3)}ms`);
}

async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  if (!command) {
    console.log('🛠️ 动态表映射缓存管理工具\n');
    console.log('使用方法:');
    Object.entries(CACHE_COMMANDS).forEach(([cmd, desc]) => {
      console.log(`   npx tsx scripts/cache-management.ts ${cmd} - ${desc}`);
    });
    console.log('\n示例:');
    console.log('   npx tsx scripts/cache-management.ts status');
    console.log('   npx tsx scripts/cache-management.ts refresh deviceCNImported');
    console.log('   npx tsx scripts/cache-management.ts clear');
    return;
  }

  switch (command) {
    case 'status':
      await showCacheStatus();
      break;
      
    case 'clear':
      await clearCache();
      break;
      
    case 'refresh':
      const databaseCode = args[1];
      if (!databaseCode) {
        console.error('❌ 请指定数据库代码');
        console.log('示例: npx tsx scripts/cache-management.ts refresh deviceCNImported');
        process.exit(1);
      }
      await refreshDatabase(databaseCode);
      break;
      
    case 'refresh-all':
      await refreshAllDatabases();
      break;
      
    case 'test':
      await testPerformance();
      break;
      
    default:
      console.error(`❌ 未知命令: ${command}`);
      console.log('可用命令:', Object.keys(CACHE_COMMANDS).join(', '));
      process.exit(1);
  }
}

if (require.main === module) {
  main().catch(console.error);
} 