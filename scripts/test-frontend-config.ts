#!/usr/bin/env tsx

/**
 * 测试前端配置获取
 */

async function testFrontendConfig() {
  console.log('🌐 测试前端配置获取...\n');

  try {
    // 1. 测试 meta API
    console.log('📡 1. 测试 /api/meta/us_pmn:');
    const metaResponse = await fetch('http://localhost:3000/api/meta/us_pmn');
    const metaResult = await metaResponse.json();
    
    if (metaResult.success) {
      console.log('✅ Meta API 调用成功');
      
      // 检查 devicename 字段
      const devicenameField = metaResult.config.fields.find((f: any) => f.fieldName === 'devicename');
      if (devicenameField) {
        console.log('📋 devicename 字段配置:');
        console.log(`   - fieldName: ${devicenameField.fieldName}`);
        console.log(`   - displayName: ${devicenameField.displayName}`);
        console.log(`   - filterType: ${devicenameField.filterType}`);
        console.log(`   - isFilterable: ${devicenameField.isFilterable}`);
        
        if (devicenameField.filterType === 'input' && devicenameField.isFilterable) {
          console.log('✅ devicename 配置正确');
        } else {
          console.log('❌ devicename 配置不正确');
        }
      } else {
        console.log('❌ 未找到 devicename 字段');
      }
      
      // 显示所有可筛选字段
      const filterableFields = metaResult.config.fields.filter((f: any) => f.isFilterable);
      console.log(`\n📊 共有 ${filterableFields.length} 个可筛选字段:`);
      filterableFields.forEach((field: any) => {
        console.log(`   - ${field.fieldName} (${field.displayName}): ${field.filterType}`);
      });
      
    } else {
      console.error('❌ Meta API 调用失败:', metaResult.error);
    }

    // 2. 测试数据库配置 API
    console.log('\n📡 2. 测试 /api/config/databases:');
    const configResponse = await fetch('http://localhost:3000/api/config/databases');
    const configResult = await configResponse.json();
    
    if (configResult.success) {
      console.log('✅ 数据库配置 API 调用成功');
      
      const usPmnConfig = configResult.data['us_pmn'];
      if (usPmnConfig) {
        console.log('📋 us_pmn 数据库配置:');
        console.log(`   - name: ${usPmnConfig.name}`);
        console.log(`   - category: ${usPmnConfig.category}`);
        console.log(`   - accessLevel: ${usPmnConfig.accessLevel}`);
      } else {
        console.log('❌ 未找到 us_pmn 数据库配置');
      }
    } else {
      console.error('❌ 数据库配置 API 调用失败:', configResult.error);
    }

    // 3. 生成前端测试指令
    console.log('\n🎯 前端测试指令:');
    console.log('请在浏览器中执行以下步骤:');
    console.log('1. 打开 http://localhost:3000/data/list/us_pmn');
    console.log('2. 打开浏览器开发者工具 (F12)');
    console.log('3. 在 Console 中执行以下代码:');
    console.log('');
    console.log('// 检查配置获取');
    console.log('fetch("/api/meta/us_pmn")');
    console.log('  .then(r => r.json())');
    console.log('  .then(data => {');
    console.log('    const devicename = data.config.fields.find(f => f.fieldName === "devicename");');
    console.log('    console.log("devicename config:", devicename);');
    console.log('    console.log("filterType:", devicename?.filterType);');
    console.log('    console.log("isFilterable:", devicename?.isFilterable);');
    console.log('  });');
    console.log('');
    console.log('4. 检查筛选器面板中是否显示 "Device Name" 输入框');
    console.log('5. 如果仍显示选择框，请强制刷新页面 (Ctrl+F5)');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 执行测试
testFrontendConfig()
  .then(() => {
    console.log('\n✨ 测试完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 测试失败:', error);
    process.exit(1);
  });
