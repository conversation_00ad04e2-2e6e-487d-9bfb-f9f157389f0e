#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 检查 deviceClass 字段的配置
 */

async function checkDeviceClassConfig() {
  console.log('🔍 检查 deviceClass 字段配置...');

  try {
    // 查询所有包含 deviceClass 字段的配置
    const deviceClassConfigs = await db.fieldConfig.findMany({
      where: {
        fieldName: 'deviceClass',
        isActive: true,
      },
      select: {
        databaseCode: true,
        fieldName: true,
        displayName: true,
        filterType: true,
        isFilterable: true,
        isActive: true,
        updatedAt: true,
      },
      orderBy: {
        databaseCode: 'asc',
      },
    });

    console.log(`\n📋 找到 ${deviceClassConfigs.length} 个 deviceClass 字段配置:`);
    deviceClassConfigs.forEach(config => {
      console.log(`   数据库: ${config.databaseCode}`);
      console.log(`   字段名: ${config.fieldName}`);
      console.log(`   显示名: ${config.displayName}`);
      console.log(`   筛选类型: ${config.filterType}`);
      console.log(`   可筛选: ${config.isFilterable}`);
      console.log(`   最后更新: ${config.updatedAt.toISOString()}`);
      console.log('   ---');
    });

    // 查询第三方标志字段作为对比
    const thirdPartyConfigs = await db.fieldConfig.findMany({
      where: {
        fieldName: 'thirdpartyflag',
        isActive: true,
      },
      select: {
        databaseCode: true,
        fieldName: true,
        displayName: true,
        filterType: true,
        isFilterable: true,
        isActive: true,
      },
      orderBy: {
        databaseCode: 'asc',
      },
    });

    console.log(`\n📋 找到 ${thirdPartyConfigs.length} 个 thirdpartyflag 字段配置（作为对比）:`);
    thirdPartyConfigs.forEach(config => {
      console.log(`   数据库: ${config.databaseCode}`);
      console.log(`   字段名: ${config.fieldName}`);
      console.log(`   显示名: ${config.displayName}`);
      console.log(`   筛选类型: ${config.filterType}`);
      console.log(`   可筛选: ${config.isFilterable}`);
      console.log('   ---');
    });

    // 检查所有数据库配置
    const allDatabases = await db.databaseConfig.findMany({
      where: { isActive: true },
      select: { code: true, name: true },
      orderBy: { code: 'asc' },
    });

    console.log(`\n📊 所有激活的数据库配置:`);
    allDatabases.forEach(db => {
      console.log(`   ${db.code}: ${db.name}`);
    });

  } catch (error) {
    console.error('❌ 查询失败:', error);
  } finally {
    await db.$disconnect();
  }
}

// 执行检查
checkDeviceClassConfig()
  .then(() => {
    console.log('\n✨ 检查完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 检查失败:', error);
    process.exit(1);
  });
