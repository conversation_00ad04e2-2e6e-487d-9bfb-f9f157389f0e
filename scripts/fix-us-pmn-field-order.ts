#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 修复 us_pmn 字段配置的排序问题
 * 确保可见字段有正确的 listOrder 和 sortOrder
 */

async function fixUsPmnFieldOrder() {
  console.log('🔧 修复 us_pmn 字段配置的排序问题...');

  try {
    // 定义正确的字段顺序配置
    const fieldOrderConfig = [
      // 可见字段 - 按列表显示顺序
      { fieldName: 'knumber', listOrder: 1, sortOrder: 1, isVisible: true },
      { fieldName: 'devicename', listOrder: 2, sortOrder: 2, isVisible: true },
      { fieldName: 'applicant', listOrder: 3, sortOrder: 3, isVisible: true },
      { fieldName: 'datereceived', listOrder: 4, sortOrder: 4, isVisible: true },
      { fieldName: 'decisiondate', listOrder: 5, sortOrder: 5, isVisible: true },
      { fieldName: 'decision', listOrder: 6, sortOrder: 6, isVisible: true },
      { fieldName: 'productcode', listOrder: 7, sortOrder: 7, isVisible: true },
      { fieldName: 'type', listOrder: 8, sortOrder: 8, isVisible: true },
      
      // 隐藏但可筛选的字段
      { fieldName: 'city', listOrder: 0, sortOrder: 0, isVisible: false },
      { fieldName: 'state', listOrder: 0, sortOrder: 0, isVisible: false },
      { fieldName: 'country_code', listOrder: 0, sortOrder: 0, isVisible: false },
      { fieldName: 'thirdparty', listOrder: 0, sortOrder: 0, isVisible: false },
      { fieldName: 'expeditedreview', listOrder: 0, sortOrder: 0, isVisible: false },
      { fieldName: 'sspindicator', listOrder: 0, sortOrder: 0, isVisible: false },
      { fieldName: 'classadvisecomm', listOrder: 0, sortOrder: 0, isVisible: false },
      { fieldName: 'reviewadvisecomm', listOrder: 0, sortOrder: 0, isVisible: false },
      { fieldName: 'decision_year', listOrder: 0, sortOrder: 0, isVisible: false },
    ];

    console.log(`📋 准备更新 ${fieldOrderConfig.length} 个字段的排序配置...`);

    // 批量更新字段配置
    for (const config of fieldOrderConfig) {
      try {
        const result = await db.fieldConfig.updateMany({
          where: {
            databaseCode: 'us_pmn',
            fieldName: config.fieldName,
          },
          data: {
            listOrder: config.listOrder,
            sortOrder: config.sortOrder,
            isVisible: config.isVisible,
          },
        });

        if (result.count > 0) {
          console.log(`  ✅ 更新字段: ${config.fieldName} (listOrder: ${config.listOrder}, sortOrder: ${config.sortOrder}, visible: ${config.isVisible})`);
        } else {
          console.log(`  ⚠️  字段不存在: ${config.fieldName}`);
        }
      } catch (error) {
        console.error(`  ❌ 更新字段失败 ${config.fieldName}:`, error);
      }
    }

    // 验证更新结果
    console.log('\n📊 验证更新结果...');
    const visibleFields = await db.fieldConfig.findMany({
      where: {
        databaseCode: 'us_pmn',
        isVisible: true,
        isActive: true,
      },
      select: {
        fieldName: true,
        displayName: true,
        listOrder: true,
        sortOrder: true,
        isVisible: true,
      },
      orderBy: { listOrder: 'asc' },
    });

    console.log(`✅ 找到 ${visibleFields.length} 个可见字段:`);
    visibleFields.forEach((field, index) => {
      console.log(`   ${index + 1}. ${field.fieldName} (${field.displayName}) - listOrder: ${field.listOrder}, sortOrder: ${field.sortOrder}`);
    });

    // 检查筛选字段
    const filterableFields = await db.fieldConfig.findMany({
      where: {
        databaseCode: 'us_pmn',
        isFilterable: true,
        isActive: true,
      },
      select: {
        fieldName: true,
        displayName: true,
        filterType: true,
        isVisible: true,
      },
      orderBy: { fieldName: 'asc' },
    });

    console.log(`\n🔍 找到 ${filterableFields.length} 个可筛选字段:`);
    filterableFields.forEach((field, index) => {
      const visibility = field.isVisible ? '可见' : '隐藏';
      console.log(`   ${index + 1}. ${field.fieldName} (${field.displayName}) - ${field.filterType} [${visibility}]`);
    });

  } catch (error) {
    console.error('❌ 修复失败:', error);
  } finally {
    await db.$disconnect();
  }
}

// 执行修复
fixUsPmnFieldOrder()
  .then(() => {
    console.log('\n✨ 字段排序修复完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 脚本执行失败:', error);
    process.exit(1);
  });
