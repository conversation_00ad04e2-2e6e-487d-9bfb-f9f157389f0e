const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function addAdvancedSearchableField() {
  try {
    console.log('🚀 开始添加 isAdvancedSearchable 字段...');

    // 1. 首先检查字段是否已存在
    try {
      const testQuery = await prisma.$queryRaw`
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'FieldConfig' 
        AND column_name = 'isAdvancedSearchable'
      `;
      
      if (testQuery.length > 0) {
        console.log('✅ isAdvancedSearchable 字段已存在，跳过添加步骤');
      } else {
        // 2. 添加新字段
        console.log('📝 添加 isAdvancedSearchable 字段...');
        await prisma.$executeRaw`
          ALTER TABLE "FieldConfig" 
          ADD COLUMN "isAdvancedSearchable" BOOLEAN NOT NULL DEFAULT false
        `;
        console.log('✅ 字段添加成功');
      }
    } catch (error) {
      console.log('📝 添加 isAdvancedSearchable 字段...');
      await prisma.$executeRaw`
        ALTER TABLE "FieldConfig" 
        ADD COLUMN "isAdvancedSearchable" BOOLEAN NOT NULL DEFAULT false
      `;
      console.log('✅ 字段添加成功');
    }

    // 3. 为现有字段设置合理的默认值
    console.log('🔧 设置现有字段的 isAdvancedSearchable 默认值...');

    // 策略：
    // - 所有 isSearchable = true 的字段，设置 isAdvancedSearchable = true
    // - 所有 isFilterable = true 的字段，设置 isAdvancedSearchable = true  
    // - 一些重要的字段（如ID、名称等）也设置为 true
    
    const updateResult1 = await prisma.fieldConfig.updateMany({
      where: {
        OR: [
          { isSearchable: true },
          { isFilterable: true }
        ]
      },
      data: {
        isAdvancedSearchable: true
      }
    });

    console.log(`✅ 已更新 ${updateResult1.count} 个字段为可高级搜索`);

    // 4. 为一些重要的字段单独设置（即使它们不是 searchable 或 filterable）
    const importantFields = [
      'id', 'knumber', 'devicename', 'productcode', 'applicant', 
      'contact', 'city', 'state', 'country_code', 'postal_code', 'zip',
      'street1', 'street2', 'stateorsumm'
    ];

    for (const fieldName of importantFields) {
      const updateResult2 = await prisma.fieldConfig.updateMany({
        where: {
          fieldName: fieldName,
          isActive: true
        },
        data: {
          isAdvancedSearchable: true
        }
      });
      
      if (updateResult2.count > 0) {
        console.log(`✅ 已设置 ${fieldName} 字段为可高级搜索`);
      }
    }

    // 5. 显示统计信息
    console.log('\n📊 统计信息:');
    
    const stats = await prisma.fieldConfig.groupBy({
      by: ['databaseCode'],
      _count: {
        _all: true
      },
      where: {
        isAdvancedSearchable: true,
        isActive: true
      }
    });

    console.log('各数据库可高级搜索字段数量:');
    for (const stat of stats) {
      console.log(`  ${stat.databaseCode}: ${stat._count._all} 个字段`);
    }

    // 6. 显示一些示例配置
    console.log('\n🔍 us_pmn 数据库字段配置示例:');
    const sampleConfigs = await prisma.fieldConfig.findMany({
      where: {
        databaseCode: 'us_pmn',
        isActive: true
      },
      select: {
        fieldName: true,
        displayName: true,
        isVisible: true,
        isSearchable: true,
        isFilterable: true,
        isAdvancedSearchable: true
      },
      orderBy: { fieldName: 'asc' },
      take: 10
    });

    sampleConfigs.forEach(config => {
      const flags = [
        config.isVisible ? '可见' : '',
        config.isSearchable ? '搜索' : '',
        config.isFilterable ? '筛选' : '',
        config.isAdvancedSearchable ? '高级搜索' : ''
      ].filter(Boolean).join(', ');
      
      console.log(`  ${config.fieldName.padEnd(20)} | ${flags} | ${config.displayName}`);
    });

    console.log('\n🎉 isAdvancedSearchable 字段添加和配置完成！');

  } catch (error) {
    console.error('❌ 错误:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 运行脚本
if (require.main === module) {
  addAdvancedSearchableField()
    .then(() => {
      console.log('✅ 脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { addAdvancedSearchableField };
