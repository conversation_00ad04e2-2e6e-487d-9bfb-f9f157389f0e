import { PrismaClient } from '@prisma/client';
// import { DEFAULT_CONFIGS } from '../src/lib/configCache';

const prisma = new PrismaClient();

function safeJson(val: any) {
  if (val === undefined || val === null) return undefined;
  try {
    return JSON.parse(JSON.stringify(val));
  } catch {
    return undefined;
  }
}

async function migrate() {
  // for (const [databaseCode, config] of Object.entries(DEFAULT_CONFIGS)) {
    // 迁移字段配置
    // for (const field of config.fields) {
      // await prisma.fieldConfig.upsert({
        // where: {
          // databaseCode_fieldName: {
            // databaseCode,
            // fieldName: field.fieldName,
          // },
        // },
        // update: {
          // displayName: field.displayName,
          // fieldType: field.fieldType as any,
          // isVisible: field.isVisible,
          // isSearchable: field.isSearchable,
          // isFilterable: field.isFilterable,
          // isSortable: field.isSortable,
          // sortOrder: field.sortOrder,
          // listOrder: field.listOrder,
          // detailOrder: field.detailOrder,
          // searchType: field.searchType as any,
          // filterType: field.filterType as any,
          // validationRules: safeJson(field.validationRules),
          // options: safeJson(field.options),
          // isActive: true,
        // },
        // create: {
          // databaseCode,
          // fieldName: field.fieldName,
          // displayName: field.displayName,
          // fieldType: field.fieldType as any,
          // isVisible: field.isVisible,
          // isSearchable: field.isSearchable,
          // isFilterable: field.isFilterable,
          // isSortable: field.isSortable,
          // sortOrder: field.sortOrder,
          // listOrder: field.listOrder,
          // detailOrder: field.detailOrder,
          // searchType: field.searchType as any,
          // filterType: field.filterType as any,
          // validationRules: safeJson(field.validationRules),
          // options: safeJson(field.options),
          // isActive: true,
        // },
      // });
    // }
    // console.log(`[${databaseCode}] 配置迁移完成`);
  // }
}

migrate()
  .then(() => {
    console.log('所有默认配置已迁移到数据库');
    return prisma.$disconnect();
  })
  .catch((err) => {
    console.error('迁移出错:', err);
    return prisma.$disconnect();
  }); 