#!/usr/bin/env tsx

import { ConfigManager } from './config-manager';
import { QUICK_SETUPS } from './quick-setup-database';
import { db } from '../src/lib/prisma';

/**
 * 测试配置系统
 */

async function testConfigSystem() {
  console.log('🧪 开始测试配置系统...\n');

  try {
    // 1. 列出现有配置
    console.log('📋 步骤1: 列出现有配置');
    await ConfigManager.listConfigs();

    // 2. 验证现有配置
    console.log('\n🔍 步骤2: 验证 us_class 配置');
    await ConfigManager.validateConfig('us_class');

    // 3. 测试多选功能是否正常
    console.log('\n🔧 步骤3: 测试多选筛选功能');
    await testMultiSelectFiltering();

    // 4. 测试 API 响应
    console.log('\n🌐 步骤4: 测试 API 响应');
    await testAPIResponse();

    console.log('\n✅ 配置系统测试完成!');

  } catch (error) {
    console.error('\n❌ 测试失败:', error);
    throw error;
  } finally {
    await db.$disconnect();
  }
}

/**
 * 测试多选筛选功能
 */
async function testMultiSelectFiltering() {
  try {
    // 检查 deviceclass 字段配置
    const deviceClassConfig = await db.fieldConfig.findFirst({
      where: {
        databaseCode: 'us_class',
        fieldName: 'deviceclass',
        isActive: true,
      },
    });

    if (deviceClassConfig) {
      console.log(`   ✅ deviceclass 字段配置存在`);
      console.log(`   📋 筛选类型: ${deviceClassConfig.filterType}`);
      console.log(`   🔍 可筛选: ${deviceClassConfig.isFilterable}`);
      
      if (deviceClassConfig.filterType === 'multi_select') {
        console.log(`   🎯 多选筛选配置正确!`);
      } else {
        console.log(`   ⚠️  筛选类型不是 multi_select`);
      }
    } else {
      console.log(`   ❌ deviceclass 字段配置不存在`);
    }

    // 检查数据库中的实际数据
    const sampleData = await (db as any).uSClass.findMany({
      select: { deviceclass: true },
      where: { 
        deviceclass: {
          not: null
        }
      },
      distinct: ['deviceclass'],
      take: 5,
    });

    console.log(`   📊 可用的器械类别值: ${sampleData.map((d: any) => d.deviceclass).join(', ')}`);

  } catch (error) {
    console.error(`   ❌ 多选筛选测试失败:`, error);
  }
}

/**
 * 测试 API 响应
 */
async function testAPIResponse() {
  try {
    // 使用内置的 fetch (Node.js 18+)
    
    // 测试 meta API
    console.log(`   🔍 测试 meta API...`);
    const metaResponse = await fetch('http://localhost:3000/api/meta/us_class');
    
    if (metaResponse.ok) {
      const metaData = await metaResponse.json();
      const deviceClassField = metaData.config?.fields?.find((f: any) => f.fieldName === 'deviceclass');
      
      if (deviceClassField) {
        console.log(`   ✅ Meta API 响应正常`);
        console.log(`   📋 deviceclass 筛选类型: ${deviceClassField.filterType}`);
      } else {
        console.log(`   ⚠️  Meta API 中未找到 deviceclass 字段`);
      }
    } else {
      console.log(`   ❌ Meta API 响应失败: ${metaResponse.status}`);
    }

    // 测试 unified search API
    console.log(`   🔍 测试 unified search API...`);
    const searchResponse = await fetch('http://localhost:3000/api/unified-search/us_class', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        filters: { deviceclass: ['1', '2'] },
        page: 1,
        limit: 3,
      }),
    });

    if (searchResponse.ok) {
      const searchData = await searchResponse.json();
      console.log(`   ✅ Unified Search API 响应正常`);
      console.log(`   📊 返回数据数量: ${searchData.data?.length || 0}`);
      console.log(`   🔍 查询条件: ${JSON.stringify(searchData.mergedWhere || {})}`);
    } else {
      console.log(`   ❌ Unified Search API 响应失败: ${searchResponse.status}`);
    }

  } catch (error) {
    console.error(`   ❌ API 测试失败:`, error);
  }
}

/**
 * 演示创建新数据库配置
 */
async function demonstrateNewDatabase() {
  console.log('\n🎨 演示: 创建新数据库配置');

  try {
    // 创建一个测试数据库配置
    await QUICK_SETUPS.createCustomDB({
      code: 'demo_test',
      name: '演示测试数据库',
      category: '测试',
      description: '用于演示配置驱动功能的测试数据库',
      accessLevel: 'free',
      customFields: [
        { fieldName: 'title', displayName: '标题', filterType: 'input' },
        { fieldName: 'category', displayName: '分类', filterType: 'multi_select' },
        { fieldName: 'status', displayName: '状态', filterType: 'select' },
        { fieldName: 'tags', displayName: '标签', filterType: 'multi_select' },
        { fieldName: 'priority', displayName: '优先级', filterType: 'select' },
      ],
    });

    console.log('✅ 演示数据库配置创建完成!');
    
    // 验证新配置
    await ConfigManager.validateConfig('demo_test');

  } catch (error) {
    console.error('❌ 演示失败:', error);
  }
}

/**
 * 清理演示数据
 */
async function cleanupDemo() {
  console.log('\n🧹 清理演示数据...');

  try {
    // 删除演示数据库配置
    await db.fieldConfig.deleteMany({
      where: { databaseCode: 'demo_test' },
    });

    await db.databaseConfig.deleteMany({
      where: { code: 'demo_test' },
    });

    console.log('✅ 演示数据清理完成');

  } catch (error) {
    console.error('❌ 清理失败:', error);
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--demo')) {
    await demonstrateNewDatabase();
  } else if (args.includes('--cleanup')) {
    await cleanupDemo();
  } else {
    await testConfigSystem();
  }
}

// 执行测试
main()
  .then(() => {
    console.log('\n🎉 测试完成!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 测试失败:', error);
    process.exit(1);
  });
