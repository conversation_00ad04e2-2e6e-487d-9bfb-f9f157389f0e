#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 配置us_pmn和us_class的导出字段设置
 * 注意：只更新配置，不删除任何数据
 */

// us_pmn 导出字段配置
const US_PMN_EXPORT_CONFIG = [
  // 主要字段 - 优先导出
  { fieldName: 'knumber', isExportable: true, exportOrder: 1, exportDisplayName: 'K Number' },
  { fieldName: 'devicename', isExportable: true, exportOrder: 2, exportDisplayName: 'Device Name' },
  { fieldName: 'applicant', isExportable: true, exportOrder: 3, exportDisplayName: 'Applicant' },
  { fieldName: 'datereceived', isExportable: true, exportOrder: 4, exportDisplayName: 'Date Received' },
  { fieldName: 'decisiondate', isExportable: true, exportOrder: 5, exportDisplayName: 'Decision Date' },
  { fieldName: 'decision', isExportable: true, exportOrder: 6, exportDisplayName: 'Decision' },
  { fieldName: 'productcode', isExportable: true, exportOrder: 7, exportDisplayName: 'Product Code' },
  { fieldName: 'type', isExportable: true, exportOrder: 8, exportDisplayName: 'Type' },
  
  // 详细信息字段 - 可选导出
  { fieldName: 'contact', isExportable: true, exportOrder: 9, exportDisplayName: 'Contact' },
  { fieldName: 'street1', isExportable: true, exportOrder: 10, exportDisplayName: 'Street Address 1' },
  { fieldName: 'street2', isExportable: true, exportOrder: 11, exportDisplayName: 'Street Address 2' },
  { fieldName: 'city', isExportable: true, exportOrder: 12, exportDisplayName: 'City' },
  { fieldName: 'state', isExportable: true, exportOrder: 13, exportDisplayName: 'State' },
  { fieldName: 'zip', isExportable: true, exportOrder: 14, exportDisplayName: 'ZIP Code' },
  { fieldName: 'postal_code', isExportable: true, exportOrder: 15, exportDisplayName: 'Postal Code' },
  { fieldName: 'country_code', isExportable: true, exportOrder: 16, exportDisplayName: 'Country Code' },
  
  // 审查相关字段
  { fieldName: 'expeditedreview', isExportable: true, exportOrder: 17, exportDisplayName: 'Expedited Review' },
  { fieldName: 'thirdparty', isExportable: true, exportOrder: 18, exportDisplayName: 'Third Party' },
  { fieldName: 'classadvisecomm', isExportable: true, exportOrder: 19, exportDisplayName: 'Classification Advisory Committee' },
  { fieldName: 'reviewadvisecomm', isExportable: true, exportOrder: 20, exportDisplayName: 'Review Advisory Committee' },
  { fieldName: 'sspindicator', isExportable: true, exportOrder: 21, exportDisplayName: 'SSP Indicator' },
  { fieldName: 'stateorsumm', isExportable: true, exportOrder: 22, exportDisplayName: 'Statement or Summary' },
  { fieldName: 'decision_year', isExportable: true, exportOrder: 23, exportDisplayName: 'Decision Year' },
  
  // 系统字段 - 通常不导出
  { fieldName: 'id', isExportable: false, exportOrder: 0, exportDisplayName: 'ID' },
  { fieldName: 'source_file', isExportable: false, exportOrder: 0, exportDisplayName: 'Source File' },
  { fieldName: 'source_time', isExportable: false, exportOrder: 0, exportDisplayName: 'Source Time' },
];

// us_class 导出字段配置
const US_CLASS_EXPORT_CONFIG = [
  // 主要字段 - 优先导出
  { fieldName: 'productcode', isExportable: true, exportOrder: 1, exportDisplayName: '产品代码' },
  { fieldName: 'devicename', isExportable: true, exportOrder: 2, exportDisplayName: '器械名称' },
  { fieldName: 'deviceclass', isExportable: true, exportOrder: 3, exportDisplayName: '器械类别' },
  { fieldName: 'medicalspecialty', isExportable: true, exportOrder: 4, exportDisplayName: '医学专科' },
  { fieldName: 'regulationnumber', isExportable: true, exportOrder: 5, exportDisplayName: '法规编号' },
  
  // 详细信息字段
  { fieldName: 'definition', isExportable: true, exportOrder: 6, exportDisplayName: '定义说明' },
  { fieldName: 'review_panel', isExportable: true, exportOrder: 7, exportDisplayName: '审查小组' },
  { fieldName: 'reviewcode', isExportable: true, exportOrder: 8, exportDisplayName: '审查代码' },
  
  // 技术特征字段
  { fieldName: 'physicalstate', isExportable: true, exportOrder: 9, exportDisplayName: '物理状态' },
  { fieldName: 'technicalmethod', isExportable: true, exportOrder: 10, exportDisplayName: '技术方法' },
  { fieldName: 'targetarea', isExportable: true, exportOrder: 11, exportDisplayName: '目标区域' },
  
  // 标志字段
  { fieldName: 'implant_flag', isExportable: true, exportOrder: 12, exportDisplayName: '植入标志' },
  { fieldName: 'life_sustain_support_flag', isExportable: true, exportOrder: 13, exportDisplayName: '生命维持支持标志' },
  { fieldName: 'gmpexemptflag', isExportable: true, exportOrder: 14, exportDisplayName: 'GMP豁免' },
  { fieldName: 'thirdpartyflag', isExportable: true, exportOrder: 15, exportDisplayName: '第三方标志' },
  
  // 其他字段
  { fieldName: 'submission_type_id', isExportable: true, exportOrder: 16, exportDisplayName: '提交类型ID' },
  { fieldName: 'unclassified_reason', isExportable: true, exportOrder: 17, exportDisplayName: '未分类原因' },
  { fieldName: 'summarymalfunctionreporting', isExportable: true, exportOrder: 18, exportDisplayName: '故障报告摘要' },
];

async function configureExportFields() {
  console.log('🔧 配置导出字段设置...\n');

  try {
    // 1. 配置us_pmn导出字段
    console.log('📋 配置us_pmn导出字段...');
    let usPmnUpdated = 0;
    
    for (const config of US_PMN_EXPORT_CONFIG) {
      try {
        const result = await db.fieldConfig.updateMany({
          where: {
            databaseCode: 'us_pmn',
            fieldName: config.fieldName,
            isActive: true,
          },
          data: {
            isExportable: config.isExportable,
            exportOrder: config.exportOrder,
            exportDisplayName: config.exportDisplayName,
          },
        });
        
        if (result.count > 0) {
          usPmnUpdated++;
          const status = config.isExportable ? '✅' : '❌';
          console.log(`  ${status} ${config.fieldName} (${config.exportDisplayName}) - 顺序: ${config.exportOrder}`);
        } else {
          console.log(`  ⚠️  ${config.fieldName} - 未找到匹配字段`);
        }
      } catch (error) {
        console.error(`  ❌ 更新${config.fieldName}失败:`, error);
      }
    }
    
    console.log(`\n✅ us_pmn: 成功更新 ${usPmnUpdated} 个字段配置`);

    // 2. 配置us_class导出字段
    console.log('\n📋 配置us_class导出字段...');
    let usClassUpdated = 0;
    
    for (const config of US_CLASS_EXPORT_CONFIG) {
      try {
        const result = await db.fieldConfig.updateMany({
          where: {
            databaseCode: 'us_class',
            fieldName: config.fieldName,
            isActive: true,
          },
          data: {
            isExportable: config.isExportable,
            exportOrder: config.exportOrder,
            exportDisplayName: config.exportDisplayName,
          },
        });
        
        if (result.count > 0) {
          usClassUpdated++;
          const status = config.isExportable ? '✅' : '❌';
          console.log(`  ${status} ${config.fieldName} (${config.exportDisplayName}) - 顺序: ${config.exportOrder}`);
        } else {
          console.log(`  ⚠️  ${config.fieldName} - 未找到匹配字段`);
        }
      } catch (error) {
        console.error(`  ❌ 更新${config.fieldName}失败:`, error);
      }
    }
    
    console.log(`\n✅ us_class: 成功更新 ${usClassUpdated} 个字段配置`);

    // 3. 验证配置结果
    console.log('\n🔍 验证配置结果...');
    
    const usPmnExportable = await db.fieldConfig.count({
      where: { databaseCode: 'us_pmn', isActive: true, isExportable: true }
    });
    
    const usClassExportable = await db.fieldConfig.count({
      where: { databaseCode: 'us_class', isActive: true, isExportable: true }
    });
    
    console.log(`  📊 us_pmn: ${usPmnExportable} 个可导出字段`);
    console.log(`  📊 us_class: ${usClassExportable} 个可导出字段`);

    console.log('\n✅ 导出字段配置完成！');
    console.log('\n📋 配置说明:');
    console.log('  - isExportable: 控制字段是否可导出');
    console.log('  - exportOrder: 控制导出字段顺序');
    console.log('  - exportDisplayName: 自定义导出列名');
    console.log('  - 系统字段(如id, source_file)设为不导出');

  } catch (error) {
    console.error('❌ 配置导出字段时出错:', error);
  } finally {
    await db.$disconnect();
  }
}

// 运行配置
if (require.main === module) {
  configureExportFields();
}
