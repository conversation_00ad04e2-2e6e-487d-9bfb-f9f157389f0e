#!/usr/bin/env tsx

/**
 * 测试优化后的系统
 * 验证所有硬编码配置已被移除，配置表正常工作
 */

async function testOptimizedSystem() {
  console.log('🧪 测试优化后的系统...\n');

  const baseUrl = 'http://localhost:3000';
  
  try {
    // 1. 测试数据库配置API
    console.log('1. 测试数据库配置API...');
    const configResponse = await fetch(`${baseUrl}/api/config/databases`);
    const configData = await configResponse.json();
    
    if (configData.success) {
      console.log('   ✅ 配置API正常工作');
      console.log(`   📊 找到 ${Object.keys(configData.data).length} 个数据库配置`);
      
      // 检查图标是否正确
      Object.entries(configData.data).forEach(([code, config]: [string, any]) => {
        console.log(`   ${config.icon} ${code}: ${config.name}`);
      });
    } else {
      console.log('   ❌ 配置API失败');
      return false;
    }

    // 2. 测试数据API
    console.log('\n2. 测试数据API...');
    const dataResponse = await fetch(`${baseUrl}/api/data/us_class?page=1&limit=5`);
    const dataResult = await dataResponse.json();
    
    if (dataResult.success) {
      console.log('   ✅ 数据API正常工作');
      console.log(`   📋 返回 ${dataResult.data.length} 条记录`);
    } else {
      console.log('   ❌ 数据API失败');
      return false;
    }

    // 3. 测试元数据API（包含字段配置）
    console.log('\n3. 测试元数据API...');
    const metaResponse = await fetch(`${baseUrl}/api/meta/us_class`);
    const metaResult = await metaResponse.json();

    if (metaResult.success) {
      console.log('   ✅ 元数据API正常工作');
      console.log(`   🔧 找到 ${metaResult.config.fields.length} 个字段配置`);
      console.log(`   📊 元数据字段: ${Object.keys(metaResult.data).length} 个`);
    } else {
      console.log('   ❌ 元数据API失败');
      return false;
    }

    // 4. 测试全局搜索
    console.log('\n4. 测试全局搜索...');
    const searchResponse = await fetch(`${baseUrl}/api/global-search?q=device`);
    const searchResult = await searchResponse.json();
    
    if (searchResult.success) {
      console.log('   ✅ 全局搜索API正常工作');
      console.log(`   🔍 搜索结果: ${searchResult.data.length} 个数据库有匹配`);
    } else {
      console.log('   ❌ 全局搜索API失败');
      return false;
    }

    console.log('\n🎉 所有测试通过！系统优化成功！');
    
    // 5. 性能测试
    console.log('\n5. 性能测试...');
    const startTime = Date.now();
    
    // 并发请求测试
    const promises = [
      fetch(`${baseUrl}/api/config/databases`),
      fetch(`${baseUrl}/api/data/us_class?page=1&limit=10`),
      fetch(`${baseUrl}/api/meta/us_class`),
      fetch(`${baseUrl}/api/global-search?q=test`)
    ];
    
    await Promise.all(promises);
    const endTime = Date.now();
    
    console.log(`   ⚡ 并发请求耗时: ${endTime - startTime}ms`);
    
    if (endTime - startTime < 2000) {
      console.log('   ✅ 性能良好');
    } else {
      console.log('   ⚠️  性能可能需要进一步优化');
    }

    return true;

  } catch (error) {
    console.error('❌ 测试失败:', error);
    return false;
  }
}

// 执行测试
if (require.main === module) {
  testOptimizedSystem()
    .then((success) => {
      if (success) {
        console.log('\n✨ 系统优化验证完成');
        process.exit(0);
      } else {
        console.log('\n💥 系统存在问题');
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('💥 测试执行失败:', error);
      process.exit(1);
    });
}

export { testOptimizedSystem };
