import { validatePaginationParams, buildPaginationResponse, GLOBAL_PAGINATION_CONFIG } from '../src/lib/globalPagination';

function testGlobalPagination() {
  console.log('🧪 测试全局翻页配置...\n');

  // 1. 测试配置常量
  console.log('📋 全局翻页配置:');
  console.log(`   默认每页条数: ${GLOBAL_PAGINATION_CONFIG.DEFAULT_PAGE_SIZE}`);
  console.log(`   最大每页条数: ${GLOBAL_PAGINATION_CONFIG.MAX_PAGE_SIZE}`);
  console.log(`   最大翻页页数: ${GLOBAL_PAGINATION_CONFIG.MAX_PAGES}`);

  // 2. 测试参数验证
  console.log('\n🔍 测试参数验证功能:');
  
  const testCases = [
    { page: 1, limit: 0, desc: '默认请求' },
    { page: 25, limit: 30, desc: '正常请求' },
    { page: 100, limit: 200, desc: '超限请求' },
    { page: -5, limit: -10, desc: '无效请求' },
    { page: 50, limit: 50, desc: '边界请求' },
    { page: 51, limit: 101, desc: '刚好超限' },
  ];

  testCases.forEach(testCase => {
    const result = validatePaginationParams(testCase.page, testCase.limit);
    console.log(`\n   ${testCase.desc}:`);
    console.log(`     输入: page=${testCase.page}, limit=${testCase.limit}`);
    console.log(`     输出: page=${result.page}, limit=${result.limit}`);
    console.log(`     是否达到最大页数: ${result.isAtMaxPages}`);
  });

  // 3. 测试分页响应构建
  console.log('\n📊 测试分页响应构建:');
  
  const responseTestCases = [
    { page: 1, limit: 20, total: 1000, desc: '第一页' },
    { page: 25, limit: 20, total: 1000, desc: '中间页' },
    { page: 50, limit: 20, total: 1000, desc: '最大页' },
    { page: 51, limit: 20, total: 1000, desc: '超过最大页' },
  ];

  responseTestCases.forEach(testCase => {
    const response = buildPaginationResponse(testCase.page, testCase.limit, testCase.total);
    console.log(`\n   ${testCase.desc}:`);
    console.log(`     页码: ${response.page}/${response.totalPages}`);
    console.log(`     记录: ${testCase.total} 总计, ${testCase.limit} 每页`);
    console.log(`     导航: hasNext=${response.hasNext}, hasPrev=${response.hasPrev}`);
    console.log(`     限制: isAtMaxPages=${response.isAtMaxPages}`);
  });

  // 4. 性能对比
  console.log('\n⚡ 性能对比测试:');
  
  const iterations = 10000;
  
  // 测试全局配置性能
  const startTime = performance.now();
  for (let i = 0; i < iterations; i++) {
    validatePaginationParams(Math.floor(Math.random() * 100), Math.floor(Math.random() * 200));
  }
  const endTime = performance.now();
  
  const avgTime = (endTime - startTime) / iterations;
  console.log(`   ${iterations}次参数验证:`);
  console.log(`   总时间: ${(endTime - startTime).toFixed(2)}ms`);
  console.log(`   平均时间: ${avgTime.toFixed(4)}ms/次`);
  console.log(`   QPS: ${(1000 / avgTime).toFixed(0)} 操作/秒`);

  // 5. 实际场景模拟
  console.log('\n🌐 实际场景模拟:');
  
  const scenarios = [
    { name: 'us_class (6994条记录)', totalRecords: 6994 },
    { name: 'us_pmn (假设5000条)', totalRecords: 5000 },
    { name: '大型数据库 (100万条)', totalRecords: 1000000 },
  ];

  scenarios.forEach(scenario => {
    const pageSize = 20;
    const totalPages = Math.ceil(scenario.totalRecords / pageSize);
    const maxAccessibleRecords = GLOBAL_PAGINATION_CONFIG.MAX_PAGES * pageSize;
    const coveragePercent = (maxAccessibleRecords / scenario.totalRecords * 100).toFixed(1);
    
    console.log(`\n   ${scenario.name}:`);
    console.log(`     总记录数: ${scenario.totalRecords.toLocaleString()}`);
    console.log(`     总页数: ${totalPages.toLocaleString()}`);
    console.log(`     可访问页数: ${GLOBAL_PAGINATION_CONFIG.MAX_PAGES}`);
    console.log(`     可访问记录: ${maxAccessibleRecords.toLocaleString()}`);
    console.log(`     覆盖率: ${coveragePercent}%`);
  });

  // 6. 用户体验分析
  console.log('\n👥 用户体验分析:');
  console.log('   ✅ 统一的翻页行为，用户容易理解');
  console.log('   ✅ 50页限制对大多数用户足够');
  console.log('   ✅ 性能优化，响应更快');
  console.log('   ✅ 代码简洁，维护成本低');
  
  console.log('\n🎯 建议:');
  console.log('   • 50页限制适合大多数使用场景');
  console.log('   • 如需访问更多数据，建议使用搜索功能');
  console.log('   • 可考虑添加"跳转到页"功能提升体验');

  console.log('\n✨ 全局翻页配置测试完成！');
}

// 运行测试
testGlobalPagination();
