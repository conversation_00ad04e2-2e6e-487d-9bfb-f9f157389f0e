#!/usr/bin/env tsx

import { getDatabaseExportLimits, validateExportLimit, isSupportedExportFormat, getExportConfigSummary } from '../src/lib/exportConfig';

/**
 * 测试导出限制配置
 */

async function testExportLimits() {
  console.log('🧪 测试导出限制配置...\n');

  try {
    // 1. 测试us_pmn导出配置
    console.log('📋 测试us_pmn导出配置...');
    const usPmnLimits = await getDatabaseExportLimits('us_pmn');
    
    console.log(`  最大导出: ${usPmnLimits.maxExportLimit} 条`);
    console.log(`  默认导出: ${usPmnLimits.defaultExportLimit} 条`);
    console.log(`  支持格式: ${usPmnLimits.exportConfig?.formats?.join(', ') || '未设置'}`);
    console.log(`  允许大量导出: ${usPmnLimits.exportConfig?.allowLargeExport ? '是' : '否'}`);

    // 测试不同的限制值
    console.log('\n  测试限制验证:');
    const testCases = [undefined, 100, 5000, 10000, 60000];
    testCases.forEach(requested => {
      const actual = validateExportLimit(requested, usPmnLimits);
      console.log(`    请求: ${requested || '未指定'} -> 实际: ${actual}`);
    });

    // 测试格式支持
    console.log('\n  测试格式支持:');
    const formats = ['csv', 'excel', 'json', 'pdf', 'xml'];
    formats.forEach(format => {
      const supported = isSupportedExportFormat(format, usPmnLimits);
      console.log(`    ${format}: ${supported ? '✅ 支持' : '❌ 不支持'}`);
    });

    // 2. 测试us_class导出配置
    console.log('\n📋 测试us_class导出配置...');
    const usClassLimits = await getDatabaseExportLimits('us_class');
    
    console.log(`  最大导出: ${usClassLimits.maxExportLimit} 条`);
    console.log(`  默认导出: ${usClassLimits.defaultExportLimit} 条`);
    console.log(`  支持格式: ${usClassLimits.exportConfig?.formats?.join(', ') || '未设置'}`);
    console.log(`  允许大量导出: ${usClassLimits.exportConfig?.allowLargeExport ? '是' : '否'}`);

    // 测试不同的限制值
    console.log('\n  测试限制验证:');
    testCases.forEach(requested => {
      const actual = validateExportLimit(requested, usClassLimits);
      console.log(`    请求: ${requested || '未指定'} -> 实际: ${actual}`);
    });

    // 3. 测试不存在的数据库
    console.log('\n📋 测试不存在的数据库配置...');
    const unknownLimits = await getDatabaseExportLimits('unknown_db');
    
    console.log(`  最大导出: ${unknownLimits.maxExportLimit} 条 (默认值)`);
    console.log(`  默认导出: ${unknownLimits.defaultExportLimit} 条 (默认值)`);
    console.log(`  配置描述: ${unknownLimits.exportConfig?.description || '未设置'}`);

    // 4. 测试配置摘要
    console.log('\n📋 测试配置摘要...');
    console.log(`  ${getExportConfigSummary('us_pmn', usPmnLimits, 3000)}`);
    console.log(`  ${getExportConfigSummary('us_class', usClassLimits, 1500)}`);

    console.log('\n✅ 导出限制配置测试完成！');
    console.log('\n📊 配置对比:');
    console.log(`  us_pmn:   默认=${usPmnLimits.defaultExportLimit}, 最大=${usPmnLimits.maxExportLimit}`);
    console.log(`  us_class: 默认=${usClassLimits.defaultExportLimit}, 最大=${usClassLimits.maxExportLimit}`);

    console.log('\n🚀 API测试建议:');
    console.log('  # 测试默认限制');
    console.log('  curl "http://localhost:3000/api/export/us_pmn?format=csv"');
    console.log('  # 测试指定限制');
    console.log('  curl "http://localhost:3000/api/export/us_pmn?format=csv&limit=100"');
    console.log('  # 测试超出限制');
    console.log('  curl "http://localhost:3000/api/export/us_pmn?format=csv&limit=100000"');
    console.log('  # 测试不支持格式');
    console.log('  curl "http://localhost:3000/api/export/us_pmn?format=pdf"');

  } catch (error) {
    console.error('❌ 测试过程中出错:', error);
  }
}

// 运行测试
if (require.main === module) {
  testExportLimits();
}
