#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 分类排序管理工具
 * 用于管理数据库配置的分类排序
 */

interface CategoryConfig {
  name: string;
  order: number;
  description: string;
}

// 预定义的分类配置
const CATEGORY_CONFIGS: Record<string, CategoryConfig> = {
  'Regulation': {
    name: 'Regulation',
    order: 1,
    description: '法规类数据库 - 包含审批、分类等监管信息'
  },
  'Marked': {
    name: 'Marked', 
    order: 2,
    description: '已上市数据库 - 包含已获批上市的产品信息'
  },
  'Marketed': {
    name: 'Marketed',
    order: 2,
    description: '已上市数据库 - 包含已获批上市的产品信息'
  },
  '全球器械': {
    name: '全球器械',
    order: 3,
    description: '全球医疗器械数据库'
  },
  '药物研发': {
    name: '药物研发',
    order: 4,
    description: '药物研发相关数据库 - 包含专利、临床试验等'
  }
};

async function showCurrentSorting() {
  console.log('📋 当前分类排序状态:\n');

  const configs = await db.databaseConfig.findMany({
    where: { isActive: true },
    select: {
      code: true,
      name: true,
      category: true,
      sortOrder: true,
      exportConfig: true,
    },
    orderBy: { sortOrder: 'asc' }
  });

  // 按分类分组
  const byCategory = configs.reduce((acc, config) => {
    if (!acc[config.category]) {
      acc[config.category] = [];
    }
    acc[config.category].push(config);
    return acc;
  }, {} as Record<string, typeof configs>);

  // 按 categoryOrder 排序分类
  const sortedCategories = Object.keys(byCategory).sort((a, b) => {
    const aOrder = CATEGORY_CONFIGS[a]?.order || 99;
    const bOrder = CATEGORY_CONFIGS[b]?.order || 99;
    return aOrder - bOrder;
  });

  sortedCategories.forEach(category => {
    const categoryConfig = CATEGORY_CONFIGS[category];
    console.log(`📁 ${category} (优先级: ${categoryConfig?.order || '未定义'})`);
    console.log(`   ${categoryConfig?.description || '无描述'}\n`);

    // 按 orderInCategory 排序该分类下的数据库
    const databases = byCategory[category].sort((a, b) => {
      const aConfig = a.exportConfig as any || {};
      const bConfig = b.exportConfig as any || {};
      const aOrder = aConfig.orderInCategory || 99;
      const bOrder = bConfig.orderInCategory || 99;
      return aOrder - bOrder;
    });

    databases.forEach(db => {
      const exportConfig = db.exportConfig as any || {};
      const icon = exportConfig.icon || '📊';
      const orderInCategory = exportConfig.orderInCategory || 'N/A';
      console.log(`   ${icon} ${db.name} (分类内排序: ${orderInCategory})`);
    });
    console.log('');
  });
}

async function updateCategorySorting() {
  console.log('🔄 更新分类排序配置...\n');

  const configs = await db.databaseConfig.findMany({
    where: { isActive: true },
    select: {
      code: true,
      category: true,
      exportConfig: true,
    }
  });

  for (const config of configs) {
    const categoryConfig = CATEGORY_CONFIGS[config.category];
    if (!categoryConfig) {
      console.log(`⚠️  未知分类: ${config.category} (${config.code})`);
      continue;
    }

    const existingExportConfig = config.exportConfig as any || {};
    const updatedExportConfig = {
      ...existingExportConfig,
      categoryOrder: categoryConfig.order,
      // 保持现有的 orderInCategory，如果没有则设为1
      orderInCategory: existingExportConfig.orderInCategory || 1
    };

    await db.databaseConfig.update({
      where: { code: config.code },
      data: { exportConfig: updatedExportConfig }
    });

    console.log(`✅ ${config.code}: ${config.category} -> categoryOrder: ${categoryConfig.order}`);
  }
}

async function addNewDatabase(
  code: string,
  name: string,
  category: string,
  orderInCategory?: number
) {
  console.log(`➕ 添加新数据库: ${code}\n`);

  const categoryConfig = CATEGORY_CONFIGS[category];
  if (!categoryConfig) {
    console.log(`❌ 未知分类: ${category}`);
    console.log(`可用分类: ${Object.keys(CATEGORY_CONFIGS).join(', ')}`);
    return;
  }

  // 如果没有指定分类内排序，自动计算
  if (!orderInCategory) {
    const existingInCategory = await db.databaseConfig.count({
      where: {
        category: category,
        isActive: true
      }
    });
    orderInCategory = existingInCategory + 1;
  }

  const exportConfig = {
    icon: '📊', // 默认图标
    categoryOrder: categoryConfig.order,
    orderInCategory: orderInCategory
  };

  console.log(`分类: ${category} (优先级: ${categoryConfig.order})`);
  console.log(`分类内排序: ${orderInCategory}`);
  console.log(`\n注意: 这只是示例，实际添加需要完整的数据库配置`);
}

async function main() {
  const command = process.argv[2];

  switch (command) {
    case 'show':
      await showCurrentSorting();
      break;

    case 'update':
      await updateCategorySorting();
      console.log('\n✅ 分类排序更新完成');
      break;

    case 'add':
      const code = process.argv[3];
      const name = process.argv[4];
      const category = process.argv[5];
      const orderInCategory = process.argv[6] ? parseInt(process.argv[6]) : undefined;

      if (!code || !name || !category) {
        console.log('用法: npm run manage-sorting add <code> <name> <category> [orderInCategory]');
        console.log('示例: npm run manage-sorting add us_device "US Device" "Regulation" 2');
        return;
      }

      await addNewDatabase(code, name, category, orderInCategory);
      break;

    default:
      console.log('🔧 分类排序管理工具\n');
      console.log('可用命令:');
      console.log('  show   - 显示当前排序状态');
      console.log('  update - 更新分类排序配置');
      console.log('  add    - 添加新数据库示例\n');
      console.log('用法: npx tsx scripts/manage-category-sorting.ts <command>');
      break;
  }
}

if (require.main === module) {
  main().catch(console.error);
}
