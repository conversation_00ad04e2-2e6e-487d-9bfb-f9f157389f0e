#!/usr/bin/env tsx

/**
 * 测试导航栏排序功能
 * 验证分类和数据库在导航栏中的正确排序
 */

async function testNavigationSorting() {
  console.log('🧪 测试导航栏排序功能...\n');

  const baseUrl = 'http://localhost:3000';
  
  try {
    // 1. 获取数据库配置
    console.log('1. 获取数据库配置...');
    const response = await fetch(`${baseUrl}/api/config/databases`);
    const result = await response.json();
    
    if (!result.success) {
      console.log('❌ 获取配置失败');
      return false;
    }

    const databaseConfigs = result.data;
    console.log(`✅ 获取到 ${Object.keys(databaseConfigs).length} 个数据库配置\n`);

    // 2. 模拟导航栏排序逻辑
    console.log('2. 模拟导航栏排序逻辑...');
    
    // 创建分类到 categoryOrder 的映射
    const categoryToOrder = new Map<string, number>();
    Object.values(databaseConfigs).forEach((config: any) => {
      if (!categoryToOrder.has(config.category)) {
        const categoryOrder = config.categoryOrder || config.sortOrder || 99;
        categoryToOrder.set(config.category, categoryOrder);
      }
    });

    // 获取所有唯一分类并按 categoryOrder 排序
    const sortedCategories = Array.from(new Set(Object.values(databaseConfigs).map((config: any) => config.category)))
      .sort((a, b) => {
        const orderA = categoryToOrder.get(a) || 99;
        const orderB = categoryToOrder.get(b) || 99;
        return orderA - orderB;
      });

    console.log('📁 分类排序结果:');
    sortedCategories.forEach((category, index) => {
      const order = categoryToOrder.get(category);
      console.log(`   ${index + 1}. ${category} (categoryOrder: ${order})`);
    });

    // 3. 测试每个分类内的数据库排序
    console.log('\n3. 测试分类内数据库排序...');
    
    sortedCategories.forEach(category => {
      console.log(`\n📂 ${category}:`);
      
      // 获取该分类下的所有数据库并排序
      const databasesInCategory = Object.entries(databaseConfigs)
        .filter(([code, config]: [string, any]) => config.category === category)
        .sort(([, configA], [, configB]) => {
          const orderA = (configA as any).orderInCategory || (configA as any).sortOrder || 0;
          const orderB = (configB as any).orderInCategory || (configB as any).sortOrder || 0;
          return orderA - orderB;
        });

      databasesInCategory.forEach(([code, config]: [string, any], index) => {
        console.log(`   ${index + 1}. ${config.icon} ${config.name} (orderInCategory: ${config.orderInCategory})`);
      });
    });

    // 4. 验证排序逻辑
    console.log('\n4. 验证排序逻辑...');
    
    let isCorrect = true;
    
    // 验证分类排序
    for (let i = 0; i < sortedCategories.length - 1; i++) {
      const currentOrder = categoryToOrder.get(sortedCategories[i]) || 99;
      const nextOrder = categoryToOrder.get(sortedCategories[i + 1]) || 99;
      
      if (currentOrder > nextOrder) {
        console.log(`❌ 分类排序错误: ${sortedCategories[i]} (${currentOrder}) 应该在 ${sortedCategories[i + 1]} (${nextOrder}) 之后`);
        isCorrect = false;
      }
    }

    // 验证分类内排序
    sortedCategories.forEach(category => {
      const databasesInCategory = Object.entries(databaseConfigs)
        .filter(([code, config]: [string, any]) => config.category === category)
        .sort(([, configA], [, configB]) => {
          const orderA = (configA as any).orderInCategory || (configA as any).sortOrder || 0;
          const orderB = (configB as any).orderInCategory || (configB as any).sortOrder || 0;
          return orderA - orderB;
        });

      for (let i = 0; i < databasesInCategory.length - 1; i++) {
        const [, currentConfig] = databasesInCategory[i];
        const [, nextConfig] = databasesInCategory[i + 1];
        
        const currentOrder = (currentConfig as any).orderInCategory || (currentConfig as any).sortOrder || 0;
        const nextOrder = (nextConfig as any).orderInCategory || (nextConfig as any).sortOrder || 0;
        
        if (currentOrder > nextOrder) {
          console.log(`❌ 分类内排序错误 (${category}): ${(currentConfig as any).name} (${currentOrder}) 应该在 ${(nextConfig as any).name} (${nextOrder}) 之后`);
          isCorrect = false;
        }
      }
    });

    if (isCorrect) {
      console.log('✅ 排序逻辑验证通过');
    }

    // 5. 生成导航栏预览
    console.log('\n5. 导航栏预览 (最终效果):');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    
    sortedCategories.forEach(category => {
      console.log(`\n🔽 ${category}`);
      
      const databasesInCategory = Object.entries(databaseConfigs)
        .filter(([code, config]: [string, any]) => config.category === category)
        .sort(([, configA], [, configB]) => {
          const orderA = (configA as any).orderInCategory || (configA as any).sortOrder || 0;
          const orderB = (configB as any).orderInCategory || (configB as any).sortOrder || 0;
          return orderA - orderB;
        });

      databasesInCategory.forEach(([code, config]: [string, any]) => {
        console.log(`   ${config.icon} ${config.name}`);
      });
    });
    
    console.log('\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

    return isCorrect;

  } catch (error) {
    console.error('❌ 测试失败:', error);
    return false;
  }
}

// 执行测试
if (require.main === module) {
  testNavigationSorting()
    .then((success) => {
      if (success) {
        console.log('\n🎉 导航栏排序测试通过！');
        process.exit(0);
      } else {
        console.log('\n💥 导航栏排序测试失败！');
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('💥 测试执行失败:', error);
      process.exit(1);
    });
}

export { testNavigationSorting };
