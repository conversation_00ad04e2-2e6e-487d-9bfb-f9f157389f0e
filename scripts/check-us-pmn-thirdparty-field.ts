#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * Check the current field configuration for the thirdparty field in us_pmn database
 */

async function checkThirdpartyField() {
  console.log('🔍 Checking thirdparty field configuration for us_pmn database...');

  try {
    // Query the thirdparty field configuration
    const thirdpartyConfig = await db.fieldConfig.findFirst({
      where: {
        databaseCode: 'us_pmn',
        fieldName: 'thirdparty',
        isActive: true,
      },
      select: {
        fieldName: true,
        displayName: true,
        fieldType: true,
        filterType: true,
        searchType: true,
        isVisible: true,
        isSearchable: true,
        isFilterable: true,
        isAdvancedSearchable: true,
        isSortable: true,
        sortOrder: true,
        listOrder: true,
        detailOrder: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!thirdpartyConfig) {
      console.log('❌ No thirdparty field configuration found for us_pmn database');
      return;
    }

    console.log('\n📋 Current thirdparty field configuration:');
    console.log(`   Field Name: ${thirdpartyConfig.fieldName}`);
    console.log(`   Display Name: ${thirdpartyConfig.displayName}`);
    console.log(`   Field Type: ${thirdpartyConfig.fieldType}`);
    console.log(`   Filter Type: ${thirdpartyConfig.filterType}`);
    console.log(`   Search Type: ${thirdpartyConfig.searchType}`);
    console.log(`   Is Visible: ${thirdpartyConfig.isVisible}`);
    console.log(`   Is Searchable: ${thirdpartyConfig.isSearchable}`);
    console.log(`   Is Filterable: ${thirdpartyConfig.isFilterable}`);
    console.log(`   Is Advanced Searchable: ${thirdpartyConfig.isAdvancedSearchable}`);
    console.log(`   Is Sortable: ${thirdpartyConfig.isSortable}`);
    console.log(`   Sort Order: ${thirdpartyConfig.sortOrder}`);
    console.log(`   List Order: ${thirdpartyConfig.listOrder}`);
    console.log(`   Detail Order: ${thirdpartyConfig.detailOrder}`);
    console.log(`   Created At: ${thirdpartyConfig.createdAt.toISOString()}`);
    console.log(`   Updated At: ${thirdpartyConfig.updatedAt.toISOString()}`);

    // Check what values exist in the actual data
    console.log('\n🔍 Checking actual thirdparty values in the database...');
    const distinctValues = await db.medicalDevice_US_PMN.findMany({
      select: {
        thirdparty: true,
      },
      distinct: ['thirdparty'],
      where: {
        thirdparty: {
          not: null,
        },
      },
      take: 20, // Limit to first 20 distinct values
    });

    console.log(`\n📊 Found ${distinctValues.length} distinct thirdparty values:`);
    distinctValues.forEach((record, index) => {
      console.log(`   ${index + 1}. "${record.thirdparty}"`);
    });

    // Count total records with thirdparty values
    const totalWithThirdparty = await db.usPmn.count({
      where: {
        thirdparty: {
          not: null,
        },
      },
    });

    const totalRecords = await db.usPmn.count();
    console.log(`\n📈 Statistics:`);
    console.log(`   Total records: ${totalRecords}`);
    console.log(`   Records with thirdparty value: ${totalWithThirdparty}`);
    console.log(`   Percentage with thirdparty: ${((totalWithThirdparty / totalRecords) * 100).toFixed(2)}%`);

    // Determine if this should be multi_select based on the data
    const uniqueValueCount = distinctValues.length;
    console.log(`\n💡 Analysis:`);
    console.log(`   Unique values: ${uniqueValueCount}`);
    
    if (uniqueValueCount > 1 && uniqueValueCount <= 50) {
      console.log(`   ✅ This field is suitable for multi_select (${uniqueValueCount} options)`);
      console.log(`   🔧 Current filterType: ${thirdpartyConfig.filterType}`);
      console.log(`   🔧 Current isAdvancedSearchable: ${thirdpartyConfig.isAdvancedSearchable}`);
      
      if (thirdpartyConfig.filterType !== 'multi_select') {
        console.log(`   ⚠️  Should be changed to filterType: 'multi_select'`);
      }
      
      if (!thirdpartyConfig.isAdvancedSearchable) {
        console.log(`   ⚠️  Should be changed to isAdvancedSearchable: true`);
      }
    } else if (uniqueValueCount <= 1) {
      console.log(`   ⚠️  Very few unique values, might not be useful for filtering`);
    } else {
      console.log(`   ⚠️  Too many unique values (${uniqueValueCount}), consider using 'select' instead`);
    }

  } catch (error) {
    console.error('❌ Error checking thirdparty field:', error);
  } finally {
    await db.$disconnect();
  }
}

// Execute the check
checkThirdpartyField()
  .then(() => {
    console.log('\n✨ Check completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Check failed:', error);
    process.exit(1);
  });
