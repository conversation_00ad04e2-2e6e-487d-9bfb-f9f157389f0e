#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

async function debugFrontendRequests() {
  console.log('🔍 调试前端请求行为...');
  
  try {
    // 1. 测试页面初始加载时的请求
    console.log('\n📊 1. 测试页面初始加载时的请求...');
    
    // 模拟前端初始加载时的请求
    const initialResponse = await fetch('http://localhost:3001/api/meta/us_class/dynamic-counts?field=thirdpartyflag&filters=%7B%7D');
    const initialResult = await initialResponse.json();
    
    console.log('初始加载请求结果:');
    if (initialResult.success) {
      console.log('✅ 请求成功');
      initialResult.data.forEach((item: any) => {
        console.log(`  ${item.value}: ${item.count} 条`);
      });
    } else {
      console.log('❌ 请求失败:', initialResult.error);
    }
    
    // 2. 测试选择器械类别后的请求
    console.log('\n📊 2. 测试选择器械类别后的请求...');
    
    const filteredResponse = await fetch('http://localhost:3001/api/meta/us_class/dynamic-counts?field=thirdpartyflag&filters=%7B%22deviceclass%22%3A%5B%222%22%5D%7D');
    const filteredResult = await filteredResponse.json();
    
    console.log('选择器械类别=2后的请求结果:');
    if (filteredResult.success) {
      console.log('✅ 请求成功');
      filteredResult.data.forEach((item: any) => {
        console.log(`  ${item.value}: ${item.count} 条`);
      });
    } else {
      console.log('❌ 请求失败:', filteredResult.error);
    }
    
    // 3. 检查前端是否使用了静态计数而非动态计数
    console.log('\n📊 3. 检查静态计数数据...');
    
    // 获取静态计数（metadataWithCounts）
    const metaResponse = await fetch('http://localhost:3001/api/meta/us_class');
    const metaResult = await metaResponse.json();
    
    if (metaResult.success && metaResult.metadataWithCounts) {
      console.log('静态计数数据 (metadataWithCounts):');
      const thirdPartyStatic = metaResult.metadataWithCounts.thirdpartyflag || [];
      thirdPartyStatic.forEach((item: any) => {
        console.log(`  ${item.value}: ${item.count} 条`);
      });
    } else {
      console.log('❌ 获取静态计数失败');
    }
    
    // 4. 对比分析
    console.log('\n📊 4. 对比分析...');
    
    if (initialResult.success && filteredResult.success && metaResult.success) {
      console.log('数据对比:');
      
      const staticData = metaResult.metadataWithCounts?.thirdpartyflag || [];
      const initialDynamic = initialResult.data;
      const filteredDynamic = filteredResult.data;
      
      console.log('\n静态计数 vs 初始动态计数:');
      const staticMap = new Map(staticData.map((item: any) => [item.value, item.count]));
      const initialMap = new Map(initialDynamic.map((item: any) => [item.value, item.count]));
      
      const allValues = new Set([...staticMap.keys(), ...initialMap.keys()]);
      allValues.forEach(value => {
        const staticCount = staticMap.get(value) || 0;
        const dynamicCount = initialMap.get(value) || 0;
        const match = staticCount === dynamicCount ? '✅' : '❌';
        console.log(`  ${value}: 静态${staticCount} vs 动态${dynamicCount} ${match}`);
      });
      
      console.log('\n初始动态计数 vs 筛选后动态计数:');
      const filteredMap = new Map(filteredDynamic.map((item: any) => [item.value, item.count]));
      
      const allDynamicValues = new Set([...initialMap.keys(), ...filteredMap.keys()]);
      allDynamicValues.forEach(value => {
        const initialCount = Number(initialMap.get(value) || 0);
        const filteredCount = Number(filteredMap.get(value) || 0);
        const change = filteredCount - initialCount;
        const changeStr = change > 0 ? `+${change}` : change.toString();
        console.log(`  ${value}: ${initialCount} → ${filteredCount} (${changeStr})`);
      });
    }
    
    // 5. 检查前端可能的问题
    console.log('\n🔍 5. 可能的前端问题分析...');
    
    console.log('可能的问题:');
    console.log('1. 前端缓存: 浏览器或React状态缓存了旧数据');
    console.log('2. 状态更新时序: useEffect依赖项或执行顺序问题');
    console.log('3. 条件判断: 前端仍在使用静态计数而非动态计数');
    console.log('4. 网络请求: 前端没有发送正确的动态计数请求');
    console.log('');
    
    console.log('调试建议:');
    console.log('1. 打开浏览器开发者工具，查看Network标签');
    console.log('2. 刷新页面，观察是否有dynamic-counts请求');
    console.log('3. 选择筛选条件，观察是否触发新的dynamic-counts请求');
    console.log('4. 检查Console标签是否有错误或调试信息');
    
    // 6. 生成测试URL
    console.log('\n🌐 6. 测试URL...');
    console.log('请在浏览器中访问以下URL进行测试:');
    console.log(`页面: http://localhost:3001/data/list/us_class`);
    console.log(`初始API: http://localhost:3001/api/meta/us_class/dynamic-counts?field=thirdpartyflag&filters=%7B%7D`);
    console.log(`筛选API: http://localhost:3001/api/meta/us_class/dynamic-counts?field=thirdpartyflag&filters=%7B%22deviceclass%22%3A%5B%222%22%5D%7D`);
    
  } catch (error) {
    console.error('❌ 调试过程中发生错误:', error);
  } finally {
    await db.$disconnect();
  }
}

// 运行调试
debugFrontendRequests().catch(console.error);
