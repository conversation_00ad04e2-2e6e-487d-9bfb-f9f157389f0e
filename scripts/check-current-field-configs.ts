#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 检查当前的字段配置
 */

async function checkCurrentFieldConfigs() {
  console.log('🔍 检查当前的字段配置...');

  try {
    // 查询所有 us_class 的字段配置
    const configs = await db.fieldConfig.findMany({
      where: {
        databaseCode: 'us_class',
        isActive: true,
      },
      select: {
        fieldName: true,
        displayName: true,
        filterType: true,
        isFilterable: true,
        isVisible: true,
        sortOrder: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: { sortOrder: 'asc' },
    });

    console.log(`\n📋 找到 ${configs.length} 个 us_class 字段配置:`);
    configs.forEach((config, index) => {
      console.log(`\n   ${index + 1}. 字段名: ${config.fieldName}`);
      console.log(`      显示名: ${config.displayName}`);
      console.log(`      筛选类型: ${config.filterType}`);
      console.log(`      可筛选: ${config.isFilterable}`);
      console.log(`      可见: ${config.isVisible}`);
      console.log(`      排序: ${config.sortOrder}`);
      console.log(`      创建时间: ${config.createdAt.toISOString()}`);
      console.log(`      更新时间: ${config.updatedAt.toISOString()}`);
    });

    // 特别检查 deviceclass 相关的配置
    const deviceClassConfigs = configs.filter(c => 
      c.fieldName.toLowerCase().includes('device') && 
      c.fieldName.toLowerCase().includes('class')
    );

    if (deviceClassConfigs.length > 0) {
      console.log(`\n🎯 器械类别相关配置:`);
      deviceClassConfigs.forEach(config => {
        console.log(`   字段名: ${config.fieldName}`);
        console.log(`   显示名: ${config.displayName}`);
        console.log(`   筛选类型: ${config.filterType}`);
        console.log(`   可筛选: ${config.isFilterable}`);
      });
    }

  } catch (error) {
    console.error('❌ 查询失败:', error);
  } finally {
    await db.$disconnect();
  }
}

// 执行检查
checkCurrentFieldConfigs()
  .then(() => {
    console.log('\n✨ 检查完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 检查失败:', error);
    process.exit(1);
  });
