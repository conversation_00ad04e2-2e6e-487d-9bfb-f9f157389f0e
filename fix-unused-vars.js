#!/usr/bin/env node

/**
 * 修复未使用变量的脚本
 * 根据构建错误信息自动修复
 */

const fs = require('fs');

const fixes = [
  // DatabasePageContent.tsx
  {
    file: 'src/app/data/list/[database]/DatabasePageContent.tsx',
    fixes: [
      { from: /import.*Calendar.*from.*/, to: '' },
      { from: /import.*Skeleton.*from.*/, to: '' },
      { from: /import.*TableSkeleton.*from.*/, to: '' },
      { from: /import.*EmptyState.*from.*/, to: '' },
      { from: /import.*InlineLoader.*from.*/, to: '' },
      { from: /import.*MultiSelectOption.*from.*/, to: '' },
      { from: /const \[.*isAdvancedSearchActive.*\] = /, to: 'const [, /* isAdvancedSearchActive */] = ' },
      { from: /const getColumnWidth = /, to: 'const _getColumnWidth = ' },
    ]
  },
  
  // CollapsibleStatsPanel.tsx
  {
    file: 'src/components/CollapsibleStatsPanel.tsx',
    fixes: [
      { from: /import.*Button.*from.*/, to: '' },
      { from: /import.*TrendingUp.*from.*/, to: '' },
      { from: /import.*Building2.*from.*/, to: '' },
      { from: /import.*Calendar.*from.*/, to: '' },
      { from: /import.*Award.*from.*/, to: '' },
      { from: /import.*Users.*from.*/, to: '' },
      { from: /onToggle/, to: '_onToggle' },
    ]
  },
  
  // ConfigurableStatsPanel.tsx
  {
    file: 'src/components/ConfigurableStatsPanel.tsx',
    fixes: [
      { from: /import.*Building2.*from.*/, to: '' },
      { from: /import.*Users.*from.*/, to: '' },
      { from: /import.*Calendar.*from.*/, to: '' },
      { from: /import.*PlusCircle.*from.*/, to: '' },
      { from: /onToggle/, to: '_onToggle' },
    ]
  },
  
  // Navigation.tsx
  {
    file: 'src/components/Navigation.tsx',
    fixes: [
      { from: /const pathname = /, to: 'const _pathname = ' },
      { from: /\[code, /, to: '[_code, ' },
    ]
  },
  
  // use-dynamic-layout.tsx
  {
    file: 'src/hooks/use-dynamic-layout.tsx',
    fixes: [
      { from: /\(prev\) =>/, to: '(_prev) =>' },
    ]
  },
  
  // jwt-manager.ts
  {
    file: 'src/lib/enterprise-auth/jwt-manager.ts',
    fixes: [
      { from: /sessionId/, to: '_sessionId' },
    ]
  },
  
  // permission-helper.ts
  {
    file: 'src/lib/permission-helper.ts',
    fixes: [
      { from: /action/, to: '_action' },
    ]
  },
  
  // syncEngine-refactored.ts
  {
    file: 'src/lib/syncEngine-refactored.ts',
    fixes: [
      { from: /rowIndex/, to: '_rowIndex' },
      { from: /const uniqueFields = /, to: 'const _uniqueFields = ' },
    ]
  },
  
  // syncEngine.ts
  {
    file: 'src/lib/syncEngine.ts',
    fixes: [
      { from: /rowIndex/, to: '_rowIndex' },
      { from: /const uniqueFields = /, to: 'const _uniqueFields = ' },
    ]
  },
  
  // uniqueKeyConfig.ts
  {
    file: 'src/lib/uniqueKeyConfig.ts',
    fixes: [
      { from: /context/, to: '_context' },
    ]
  },
  
  // enterprise-auth.ts
  {
    file: 'src/middleware/enterprise-auth.ts',
    fixes: [
      { from: /requiredPermission/, to: '_requiredPermission' },
      { from: /const permissions = /, to: 'const _permissions = ' },
    ]
  },
  
  // import-csv-refactored.ts
  {
    file: 'src/scripts/import-csv-refactored.ts',
    fixes: [
      { from: /context/, to: '_context' },
    ]
  },
];

function applyFixes() {
  console.log('🔧 开始修复未使用变量...');
  
  let totalFixed = 0;
  
  fixes.forEach(({ file, fixes: fileFixes }) => {
    try {
      if (!fs.existsSync(file)) {
        console.log(`⚠️  文件不存在: ${file}`);
        return;
      }
      
      let content = fs.readFileSync(file, 'utf-8');
      let changed = false;
      
      fileFixes.forEach(({ from, to }) => {
        if (from.test && from.test(content)) {
          content = content.replace(from, to);
          changed = true;
        } else if (typeof from === 'string' && content.includes(from)) {
          content = content.replace(new RegExp(from, 'g'), to);
          changed = true;
        }
      });
      
      if (changed) {
        fs.writeFileSync(file, content, 'utf-8');
        console.log(`✅ 修复: ${file}`);
        totalFixed++;
      }
      
    } catch (error) {
      console.log(`❌ 错误处理 ${file}: ${error.message}`);
    }
  });
  
  // 移除未使用的 eslint-disable 注释
  const filesToClean = [
    'src/app/ClientBody.tsx',
    'src/app/admin/analytics/page.tsx',
    'src/app/admin/analytics-data/page.tsx',
    'src/app/data/page.tsx',
    'src/app/debug-us-class/page.tsx',
    'src/app/page.tsx',
    'src/components/SimpleAccessCheck.tsx',
    'src/components/StatsPanel.tsx',
    'src/components/ui/captcha.tsx',
    'src/components/ui/sidebar.tsx',
    'src/hooks/use-debounced-search.tsx',
    'src/hooks/use-dynamic-layout.tsx',
    'src/hooks/useResizableColumns.ts',
    'src/lib/auth.tsx',
    'src/lib/permissions.ts',
  ];
  
  filesToClean.forEach(file => {
    try {
      if (!fs.existsSync(file)) return;
      
      let content = fs.readFileSync(file, 'utf-8');
      const originalContent = content;
      
      // 移除未使用的 eslint-disable 注释
      content = content.replace(/^\s*\/\/ eslint-disable-next-line react-hooks\/exhaustive-deps\s*\n/gm, '');
      content = content.replace(/^\s*\/\/ eslint-disable-next-line @typescript-eslint\/no-explicit-any\s*\n/gm, '');
      
      if (content !== originalContent) {
        fs.writeFileSync(file, content, 'utf-8');
        console.log(`🧹 清理: ${file}`);
        totalFixed++;
      }
      
    } catch (error) {
      console.log(`❌ 清理错误 ${file}: ${error.message}`);
    }
  });
  
  console.log(`\n🎉 修复完成! 共处理 ${totalFixed} 个文件`);
}

applyFixes();
