#!/usr/bin/env node

/**
 * 调试 US PMN allFields 搜索问题
 */

const fetch = require('node-fetch');

async function debugUSPMNAllFields() {
  console.log('🔍 调试 US PMN allFields 搜索问题...\n');

  const baseUrl = 'http://localhost:3000';
  
  try {
    // 1. 测试基础数据API
    console.log('1. 测试基础数据API (无搜索)');
    const basicResponse = await fetch(`${baseUrl}/api/data/us_pmn?page=1&limit=5`);
    const basicData = await basicResponse.json();
    console.log(`   状态: ${basicData.success ? '✅' : '❌'}`);
    console.log(`   数据条数: ${basicData.data?.length || 0}`);
    console.log(`   总记录数: ${basicData.pagination?.totalCount || 0}`);
    console.log(`   配置字段数: ${basicData.config?.fields?.length || 0}\n`);

    // 2. 测试 allFields 搜索API
    console.log('2. 测试 allFields 搜索API');
    const allFieldsResponse = await fetch(`${baseUrl}/api/data/us_pmn?allFields=dental&page=1&limit=5`);
    const allFieldsData = await allFieldsResponse.json();
    console.log(`   状态: ${allFieldsData.success ? '✅' : '❌'}`);
    console.log(`   数据条数: ${allFieldsData.data?.length || 0}`);
    console.log(`   总记录数: ${allFieldsData.pagination?.totalCount || 0}`);
    
    if (allFieldsData.error) {
      console.log(`   错误: ${allFieldsData.error}`);
    }
    
    if (allFieldsData.data && allFieldsData.data.length > 0) {
      console.log(`   第一条数据字段: ${Object.keys(allFieldsData.data[0]).join(', ')}`);
    }
    console.log('');

    // 3. 测试医疗搜索API
    console.log('3. 测试医疗搜索API');
    const medicalSearchResponse = await fetch(`${baseUrl}/api/medical-search?q=dental&database=us_pmn&page=1&limit=5`);
    const medicalSearchData = await medicalSearchResponse.json();
    console.log(`   状态: ${medicalSearchData.success ? '✅' : '❌'}`);
    console.log(`   数据条数: ${medicalSearchData.data?.hits?.length || 0}`);
    console.log(`   总记录数: ${medicalSearchData.data?.total || 0}`);
    
    if (medicalSearchData.error) {
      console.log(`   错误: ${medicalSearchData.error}`);
    }
    console.log('');

    // 4. 测试统一搜索API
    console.log('4. 测试统一搜索API');
    const unifiedSearchResponse = await fetch(`${baseUrl}/api/unified-database-search/us_pmn?q=dental&page=1&limit=5`);
    const unifiedSearchData = await unifiedSearchResponse.json();
    console.log(`   状态: ${unifiedSearchData.success ? '✅' : '❌'}`);
    console.log(`   数据条数: ${unifiedSearchData.data?.length || 0}`);
    console.log(`   总记录数: ${unifiedSearchData.pagination?.total_results || 0}`);
    
    if (unifiedSearchData.error) {
      console.log(`   错误: ${unifiedSearchData.error}`);
    }
    console.log('');

    // 5. 测试前端页面
    console.log('5. 测试前端页面');
    const pageResponse = await fetch(`${baseUrl}/data/list/us_pmn?allFields=dental`);
    const pageHtml = await pageResponse.text();
    
    console.log(`   HTTP状态: ${pageResponse.status}`);
    console.log(`   页面大小: ${pageHtml.length} 字符`);
    
    // 检查页面中是否包含错误信息
    const hasError = pageHtml.includes('Loading Failed') || pageHtml.includes('Error');
    const hasLoading = pageHtml.includes('Loading...');
    const hasData = pageHtml.includes('table') && pageHtml.includes('tbody');
    
    console.log(`   包含错误: ${hasError ? '❌' : '✅'}`);
    console.log(`   包含加载中: ${hasLoading ? '⏳' : '✅'}`);
    console.log(`   包含数据表格: ${hasData ? '✅' : '❌'}`);
    console.log('');

    // 6. 检查配置
    console.log('6. 检查数据库配置');
    const configResponse = await fetch(`${baseUrl}/api/meta/us_pmn`);
    const configData = await configResponse.json();
    console.log(`   配置状态: ${configData.success ? '✅' : '❌'}`);
    
    if (configData.success && configData.config) {
      const searchableFields = configData.config.fields.filter(f => f.isSearchable);
      console.log(`   可搜索字段数: ${searchableFields.length}`);
      console.log(`   可搜索字段: ${searchableFields.map(f => f.fieldName).join(', ')}`);
    }

  } catch (error) {
    console.error('❌ 调试过程中发生错误:', error.message);
  }
}

// 运行调试
debugUSPMNAllFields().catch(console.error);
