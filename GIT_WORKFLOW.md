# Git 工作流程

## 项目状态
✅ **Git仓库已初始化**
- 初始提交：`3586c1a` - Complete Prisma ORM migration with dynamic data query system
- 67个文件，16,917行代码

## 常用Git命令

### 查看状态
```bash
git status                    # 查看当前状态
git log --oneline            # 查看提交历史
git diff                     # 查看未提交的更改
```

### 提交更改
```bash
git add .                    # 添加所有更改
git add <文件名>             # 添加特定文件
git commit -m "提交信息"     # 提交更改
```

### 分支管理
```bash
git branch                   # 查看所有分支
git branch <分支名>          # 创建新分支
git checkout <分支名>        # 切换到分支
git checkout -b <分支名>     # 创建并切换到新分支
```

### 远程仓库（可选）
```bash
git remote add origin <仓库URL>  # 添加远程仓库
git push -u origin master        # 推送到远程仓库
git pull origin master           # 从远程仓库拉取
```

## 推荐的提交信息格式

```
feat: 添加新功能
fix: 修复bug
docs: 更新文档
style: 代码格式调整
refactor: 代码重构
test: 添加测试
chore: 构建过程或辅助工具的变动
```

## 当前项目特点
- 使用Prisma ORM进行数据库操作
- 动态API路由支持多数据库查询
- Next.js 15 + TypeScript
- 完整的权限管理系统
- 动态前端筛选组件

## 注意事项
1. 确保`.env`文件不被提交（已在.gitignore中配置）
2. 提交前运行`npm run lint`检查代码质量
3. 重要更改建议创建新分支进行开发 