generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ==== 用户管理相关模型 ====
model User {
  id               String         @id @default(uuid())
  email            String         @unique @db.VarChar(255)
  password         String
  name             String         @db.VarChar(100)
  membershipType   String         @default("free") @db.VarChar(20)
  membershipExpiry DateTime?
  isActive         Boolean        @default(true)
  emailVerified    <PERSON>olean        @default(false)
  createdAt        DateTime       @default(now())
  updatedAt        DateTime       @updatedAt
  activityLogs     ActivityLog[]
  sessions         UserSession[]
  searchAnalytics  SearchAnalytics[]
}

model UserSession {
  id        String   @id @default(uuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

// ==== 配置管理模型 ====
model DatabaseConfig {
  id          String   @id @default(uuid())
  code        String   @unique @db.VarChar(50)
  name        String   @db.VarChar(100)
  category    String   @db.VarChar(50)
  description String?
  accessLevel String   @default("free") @db.VarChar(20)
  isActive    Boolean  @default(true)
  sortOrder   Int      @default(autoincrement())
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model FieldConfig {
  id             String     @id @default(uuid())
  databaseCode   String     @db.VarChar(50)
  fieldName      String     @db.VarChar(100)
  displayName    String     @db.VarChar(100)
  fieldType      FieldType  @default(text)
  isVisible      Boolean    @default(true)
  isSearchable   Boolean    @default(false)
  isFilterable   Boolean    @default(false)
  isAdvancedSearchable Boolean @default(false)
  isSortable     Boolean    @default(false)
  sortOrder      Int        @default(0)
  listOrder      Int        @default(0)
  detailOrder    Int        @default(0)
  searchType     SearchType @default(contains)
  filterType     FilterType @default(select)
  validationRules Json?
  options        Json?
  isActive       Boolean    @default(true)
  createdAt      DateTime   @default(now())
  updatedAt      DateTime   @updatedAt

  @@unique([databaseCode, fieldName])
  @@index([databaseCode])
  @@index([isActive])
}

// ==== 基础医疗器械模型（抽象模板）====
// 注意：这个模型不会直接使用，仅作为其他模型的模板参考

// ==== 中国大陆医疗器械数据表 ====
model MedicalDevice_CN_Imported {
  id                 String   @id @default(uuid())
  productName        String
  companyName        String
  registrationNumber String?  @db.VarChar(100)
  managementType     String?  @db.VarChar(50)
  approvalDate       DateTime?
  validUntil         DateTime?
  category           String?  @db.VarChar(20)
  structureOrUse     String?
  productionAddress  String?
  companyAddress     String?
  specifications     String?
  structure          String?
  scope              String?
  storageConditions  String?
  accessories        String?
  otherContent       String?
  notes              String?
  classification     String?  @db.VarChar(50)
  approvalDepartment String?
  changeHistory      String?
  isInnovative       Boolean? @default(false)
  isClinicalNeed     Boolean? @default(false)
  isChildrenSpecific Boolean? @default(false)
  isRareDisease      Boolean? @default(false)
  // 移除database字段
  businessKey        String   @unique @db.VarChar(200)
  businessKeyHash    String?  @unique
  dataVersion        Int      @default(1)
  isActive           Boolean  @default(true)
  importedAt         DateTime @default(now())
  updatedAt          DateTime @updatedAt
  createdAt          DateTime @default(now())
  
  @@map("medical_device_cn_imported")
  @@index([registrationNumber])
  @@index([businessKey])
  @@index([businessKeyHash])
  @@index([productName])
  @@index([companyName])
}

model MedicalDevice_CN_Evaluation {
  id                 String   @id @default(uuid())
  productName        String
  companyName        String
  registrationNumber String?  @db.VarChar(100)
  managementType     String?  @db.VarChar(50)
  approvalDate       DateTime?
  validUntil         DateTime?
  category           String?  @db.VarChar(20)
  structureOrUse     String?
  productionAddress  String?
  companyAddress     String?
  specifications     String?
  structure          String?
  scope              String?
  storageConditions  String?
  accessories        String?
  otherContent       String?
  notes              String?
  classification     String?  @db.VarChar(50)
  approvalDepartment String?
  changeHistory      String?
  isInnovative       Boolean? @default(false)
  isClinicalNeed     Boolean? @default(false)
  isChildrenSpecific Boolean? @default(false)
  isRareDisease      Boolean? @default(false)
  businessKey        String   @unique @db.VarChar(200)
  businessKeyHash    String?  @unique
  dataVersion        Int      @default(1)
  isActive           Boolean  @default(true)
  importedAt         DateTime @default(now())
  updatedAt          DateTime @updatedAt
  createdAt          DateTime @default(now())
  
  @@map("medical_device_cn_evaluation")
  @@index([registrationNumber])
  @@index([businessKey])
  @@index([businessKeyHash])
  @@index([productName])
  @@index([companyName])
}

model MedicalDevice_US {
  id                 String   @id @default(uuid())
  productName        String
  companyName        String
  registrationNumber String?  @db.VarChar(100)
  managementType     String?  @db.VarChar(50)
  approvalDate       DateTime?
  validUntil         DateTime?
  category           String?  @db.VarChar(20)
  structureOrUse     String?
  productionAddress  String?
  companyAddress     String?
  specifications     String?
  structure          String?
  scope              String?
  storageConditions  String?
  accessories        String?
  otherContent       String?
  notes              String?
  classification     String?  @db.VarChar(50)
  approvalDepartment String?
  changeHistory      String?
  isInnovative       Boolean? @default(false)
  isClinicalNeed     Boolean? @default(false)
  isChildrenSpecific Boolean? @default(false)
  isRareDisease      Boolean? @default(false)
  businessKey        String   @unique @db.VarChar(200)
  businessKeyHash    String?  @unique
  dataVersion        Int      @default(1)
  isActive           Boolean  @default(true)
  importedAt         DateTime @default(now())
  updatedAt          DateTime @updatedAt
  createdAt          DateTime @default(now())
  
  @@map("medical_device_us")
  @@index([registrationNumber])
  @@index([businessKey])
  @@index([businessKeyHash])
  @@index([productName])
  @@index([companyName])
}

model MedicalDevice_HK {
  id                 String   @id @default(uuid())
  productName        String
  companyName        String
  registrationNumber String?  @db.VarChar(100)
  managementType     String?  @db.VarChar(50)
  approvalDate       DateTime?
  validUntil         DateTime?
  category           String?  @db.VarChar(20)
  structureOrUse     String?
  productionAddress  String?
  companyAddress     String?
  specifications     String?
  structure          String?
  scope              String?
  storageConditions  String?
  accessories        String?
  otherContent       String?
  notes              String?
  classification     String?  @db.VarChar(50)
  approvalDepartment String?
  changeHistory      String?
  isInnovative       Boolean? @default(false)
  isClinicalNeed     Boolean? @default(false)
  isChildrenSpecific Boolean? @default(false)
  isRareDisease      Boolean? @default(false)
  businessKey        String   @unique @db.VarChar(200)
  businessKeyHash    String?  @unique
  dataVersion        Int      @default(1)
  isActive           Boolean  @default(true)
  importedAt         DateTime @default(now())
  updatedAt          DateTime @updatedAt
  createdAt          DateTime @default(now())
  
  @@map("medical_device_hk")
  @@index([registrationNumber])
  @@index([businessKey])
  @@index([businessKeyHash])
  @@index([productName])
  @@index([companyName])
}

model MedicalDevice_JP {
  id                 String   @id @default(uuid())
  productName        String
  companyName        String
  registrationNumber String?  @db.VarChar(100)
  managementType     String?  @db.VarChar(50)
  approvalDate       DateTime?
  validUntil         DateTime?
  category           String?  @db.VarChar(20)
  structureOrUse     String?
  productionAddress  String?
  companyAddress     String?
  specifications     String?
  structure          String?
  scope              String?
  storageConditions  String?
  accessories        String?
  otherContent       String?
  notes              String?
  classification     String?  @db.VarChar(50)
  approvalDepartment String?
  changeHistory      String?
  isInnovative       Boolean? @default(false)
  isClinicalNeed     Boolean? @default(false)
  isChildrenSpecific Boolean? @default(false)
  isRareDisease      Boolean? @default(false)
  businessKey        String   @unique @db.VarChar(200)
  businessKeyHash    String?  @unique
  dataVersion        Int      @default(1)
  isActive           Boolean  @default(true)
  importedAt         DateTime @default(now())
  updatedAt          DateTime @updatedAt
  createdAt          DateTime @default(now())
  
  @@map("medical_device_jp")
  @@index([registrationNumber])
  @@index([businessKey])
  @@index([businessKeyHash])
  @@index([productName])
  @@index([companyName])
}

model MedicalDevice_UK {
  id                 String   @id @default(uuid())
  productName        String
  companyName        String
  registrationNumber String?  @db.VarChar(100)
  managementType     String?  @db.VarChar(50)
  approvalDate       DateTime?
  validUntil         DateTime?
  category           String?  @db.VarChar(20)
  structureOrUse     String?
  productionAddress  String?
  companyAddress     String?
  specifications     String?
  structure          String?
  scope              String?
  storageConditions  String?
  accessories        String?
  otherContent       String?
  notes              String?
  classification     String?  @db.VarChar(50)
  approvalDepartment String?
  changeHistory      String?
  isInnovative       Boolean? @default(false)
  isClinicalNeed     Boolean? @default(false)
  isChildrenSpecific Boolean? @default(false)
  isRareDisease      Boolean? @default(false)
  businessKey        String   @unique @db.VarChar(200)
  businessKeyHash    String?  @unique
  dataVersion        Int      @default(1)
  isActive           Boolean  @default(true)
  importedAt         DateTime @default(now())
  updatedAt          DateTime @updatedAt
  createdAt          DateTime @default(now())
  
  @@map("medical_device_uk")
  @@index([registrationNumber])
  @@index([businessKey])
  @@index([businessKeyHash])
  @@index([productName])
  @@index([companyName])
}

model MedicalDevice_SG {
  id                 String   @id @default(uuid())
  productName        String
  companyName        String
  registrationNumber String?  @db.VarChar(100)
  managementType     String?  @db.VarChar(50)
  approvalDate       DateTime?
  validUntil         DateTime?
  category           String?  @db.VarChar(20)
  structureOrUse     String?
  productionAddress  String?
  companyAddress     String?
  specifications     String?
  structure          String?
  scope              String?
  storageConditions  String?
  accessories        String?
  otherContent       String?
  notes              String?
  classification     String?  @db.VarChar(50)
  approvalDepartment String?
  changeHistory      String?
  isInnovative       Boolean? @default(false)
  isClinicalNeed     Boolean? @default(false)
  isChildrenSpecific Boolean? @default(false)
  isRareDisease      Boolean? @default(false)
  businessKey        String   @unique @db.VarChar(200)
  businessKeyHash    String?  @unique
  dataVersion        Int      @default(1)
  isActive           Boolean  @default(true)
  importedAt         DateTime @default(now())
  updatedAt          DateTime @updatedAt
  createdAt          DateTime @default(now())
  
  @@map("medical_device_sg")
  @@index([registrationNumber])
  @@index([businessKey])
  @@index([businessKeyHash])
  @@index([productName])
  @@index([companyName])
}

model MedicalDevice_FreePat {
  id                 String   @id @default(uuid())
  productName        String
  companyName        String
  registrationNumber String?  @db.VarChar(100)
  managementType     String?  @db.VarChar(50)
  approvalDate       DateTime?
  validUntil         DateTime?
  category           String?  @db.VarChar(20)
  structureOrUse     String?
  productionAddress  String?
  companyAddress     String?
  specifications     String?
  structure          String?
  scope              String?
  storageConditions  String?
  accessories        String?
  otherContent       String?
  notes              String?
  classification     String?  @db.VarChar(50)
  approvalDepartment String?
  changeHistory      String?
  isInnovative       Boolean? @default(false)
  isClinicalNeed     Boolean? @default(false)
  isChildrenSpecific Boolean? @default(false)
  isRareDisease      Boolean? @default(false)
  businessKey        String   @unique @db.VarChar(200)
  businessKeyHash    String?  @unique
  dataVersion        Int      @default(1)
  isActive           Boolean  @default(true)
  importedAt         DateTime @default(now())
  updatedAt          DateTime @updatedAt
  createdAt          DateTime @default(now())
  
  @@map("medical_device_free_pat")
  @@index([registrationNumber])
  @@index([businessKey])
  @@index([businessKeyHash])
  @@index([productName])
  @@index([companyName])
}

model MedicalDevice_SubjectNewdrug {
  id                 String   @id @default(uuid())
  productName        String
  companyName        String
  registrationNumber String?  @db.VarChar(100)
  managementType     String?  @db.VarChar(50)
  approvalDate       DateTime?
  validUntil         DateTime?
  category           String?  @db.VarChar(20)
  structureOrUse     String?
  productionAddress  String?
  companyAddress     String?
  specifications     String?
  structure          String?
  scope              String?
  storageConditions  String?
  accessories        String?
  otherContent       String?
  notes              String?
  classification     String?  @db.VarChar(50)
  approvalDepartment String?
  changeHistory      String?
  isInnovative       Boolean? @default(false)
  isClinicalNeed     Boolean? @default(false)
  isChildrenSpecific Boolean? @default(false)
  isRareDisease      Boolean? @default(false)
  businessKey        String   @unique @db.VarChar(200)
  businessKeyHash    String?  @unique
  dataVersion        Int      @default(1)
  isActive           Boolean  @default(true)
  importedAt         DateTime @default(now())
  updatedAt          DateTime @updatedAt
  createdAt          DateTime @default(now())
  
  @@map("medical_device_subject_newdrug")
  @@index([registrationNumber])
  @@index([businessKey])
  @@index([businessKeyHash])
  @@index([productName])
  @@index([companyName])
}

model MedicalDevice_SubjectLicenseout {
  id                 String   @id @default(uuid())
  productName        String
  companyName        String
  registrationNumber String?  @db.VarChar(100)
  managementType     String?  @db.VarChar(50)
  approvalDate       DateTime?
  validUntil         DateTime?
  category           String?  @db.VarChar(20)
  structureOrUse     String?
  productionAddress  String?
  companyAddress     String?
  specifications     String?
  structure          String?
  scope              String?
  storageConditions  String?
  accessories        String?
  otherContent       String?
  notes              String?
  classification     String?  @db.VarChar(50)
  approvalDepartment String?
  changeHistory      String?
  isInnovative       Boolean? @default(false)
  isClinicalNeed     Boolean? @default(false)
  isChildrenSpecific Boolean? @default(false)
  isRareDisease      Boolean? @default(false)
  businessKey        String   @unique @db.VarChar(200)
  businessKeyHash    String?  @unique
  dataVersion        Int      @default(1)
  isActive           Boolean  @default(true)
  importedAt         DateTime @default(now())
  updatedAt          DateTime @updatedAt
  createdAt          DateTime @default(now())
  
  @@map("medical_device_subject_licenseout")
  @@index([registrationNumber])
  @@index([businessKey])
  @@index([businessKeyHash])
  @@index([productName])
  @@index([companyName])
}

model MedicalDevice_SubjectVbp {
  id                 String   @id @default(uuid())
  productName        String
  companyName        String
  registrationNumber String?  @db.VarChar(100)
  managementType     String?  @db.VarChar(50)
  approvalDate       DateTime?
  validUntil         DateTime?
  category           String?  @db.VarChar(20)
  structureOrUse     String?
  productionAddress  String?
  companyAddress     String?
  specifications     String?
  structure          String?
  scope              String?
  storageConditions  String?
  accessories        String?
  otherContent       String?
  notes              String?
  classification     String?  @db.VarChar(50)
  approvalDepartment String?
  changeHistory      String?
  isInnovative       Boolean? @default(false)
  isClinicalNeed     Boolean? @default(false)
  isChildrenSpecific Boolean? @default(false)
  isRareDisease      Boolean? @default(false)
  businessKey        String   @unique @db.VarChar(200)
  businessKeyHash    String?  @unique
  dataVersion        Int      @default(1)
  isActive           Boolean  @default(true)
  importedAt         DateTime @default(now())
  updatedAt          DateTime @updatedAt
  createdAt          DateTime @default(now())
  
  @@map("medical_device_subject_vbp")
  @@index([registrationNumber])
  @@index([businessKey])
  @@index([businessKeyHash])
  @@index([productName])
  @@index([companyName])
}

// ==== 美国PMN(510k)数据表 - 专用模型示例 ====
// 注意：PMN数据字段结构与常规医疗器械完全不同，必须使用专用模型
model MedicalDevice_US_PMN {
  id                 String   @id @default(uuid())
  
  // PMN核心字段（与常规医疗器械不同）
  kNumber            String   @db.VarChar(20)    // 510(k)编号，如 K123456
  deviceName         String                      // 设备名称
  applicant          String                      // 申请人/公司名称
  applicantContact   String?                     // 申请人联系信息
  
  // 时间相关字段
  dateReceived       DateTime?                   // FDA接收日期
  decisionDate       DateTime?                   // 决定日期
  decisionDateYear   Int?                        // 决定年份（用于筛选）
  
  // 审批相关字段
  decision           String?  @db.VarChar(50)    // 决定类型：Substantially Equivalent, Not Substantially Equivalent
  expeditedReview    Boolean? @default(false)    // 是否加急审查
  thirdPartyReview   Boolean? @default(false)    // 是否第三方审查
  
  // 分类相关字段
  regulationNumber   String?  @db.VarChar(50)    // 法规编号，如 21 CFR 876.1500
  classificationName String?                     // 分类名称
  productCode        String?  @db.VarChar(10)    // 产品代码，如 DXX
  medicalSpecialty   String?  @db.VarChar(100)   // 医学专科
  
  // 详细信息字段
  deviceDescription  String?  @db.Text           // 设备描述
  intendedUse        String?  @db.Text           // 预期用途
  summary            String?  @db.Text           // 510(k)摘要
  statementOfSimilarity String? @db.Text         // 相似性声明
  
  // 技术特征字段
  deviceClass        String?  @db.VarChar(10)    // 器械类别 (I, II, III)
  isExempt           Boolean? @default(false)    // 是否豁免510(k)
  panelCode          String?  @db.VarChar(10)    // 专家组代码
  
  // PMN特有的业务字段
  predicate510k      String?  @db.VarChar(20)    // 对比的510(k)编号
  clearanceLetterUrl String?  @db.VarChar(500)   // 批准信URL
  reviewType         String?  @db.VarChar(50)    // 审查类型
  
  // 标准化管理字段
  businessKey        String   @unique @db.VarChar(200)
  businessKeyHash    String?  @unique
  dataVersion        Int      @default(1)
  isActive           Boolean  @default(true)
  importedAt         DateTime @default(now())
  updatedAt          DateTime @updatedAt
  createdAt          DateTime @default(now())
  
  @@map("medical_device_us_pmn")
  @@index([kNumber])
  @@index([applicant])
  @@index([deviceName])
  @@index([productCode])
  @@index([decisionDate])
  @@index([decisionDateYear])
  @@index([decision])
  @@index([businessKey])
  @@index([businessKeyHash])
}

// ==== 公司数据（保持现有结构，因为已经按region区分）====
model Company {
  id                 String   @id @default(uuid())
  companyName        String
  companyCode        String?  @db.VarChar(50)
  companyShortName   String?
  region             String   @db.VarChar(50)
  province           String?
  city               String?
  address            String?
  phone              String?
  email              String?
  website            String?
  establishDate      DateTime?
  registeredCapital  String?
  legalRepresentative String?
  businessScope      String?
  companyType        String?
  industryCategory   String?
  isListed           Boolean? @default(false)
  stockCode          String?
  notes              String?
  // 移除database字段，改用region区分
  businessKey        String   @unique @db.VarChar(200)
  businessKeyHash    String?  @unique
  dataVersion        Int      @default(1)
  isActive           Boolean  @default(true)
  importedAt         DateTime @default(now())
  updatedAt          DateTime @updatedAt
  createdAt          DateTime @default(now())

  @@index([region])
  @@index([businessKey])
  @@index([businessKeyHash])
}

// ==== 分析和日志模型（保持现有结构，但database字段改为可选）====
model ActivityLog {
  id          String   @id @default(uuid())
  userId      String?
  ip          String   @db.VarChar(50)
  userAgent   String?  @db.VarChar(500)
  path        String   @db.VarChar(500)
  method      String   @db.VarChar(20)
  queryParams String?  @db.Text
  referer     String?  @db.VarChar(500)
  databaseCode String? @db.VarChar(50) // 重命名并改为可选，通过路径推断
  eventType   String?  @db.VarChar(50)
  sessionId   String?  @db.VarChar(100)
  createdAt   DateTime @default(now())
  user        User?    @relation(fields: [userId], references: [id])

  // 性能优化索引
  @@index([createdAt])
  @@index([eventType])
  @@index([databaseCode])
  @@index([sessionId])
  @@index([ip])
  @@index([userId])
  @@index([createdAt, eventType])
  @@index([createdAt, databaseCode])
  @@index([sessionId, eventType])
}

model SearchAnalytics {
  id            String   @id @default(uuid())
  userId        String?
  sessionId     String?  @db.VarChar(100)
  databaseCode  String   @db.VarChar(50) // 重命名，通过路径推断
  searchType    String   @db.VarChar(20) // 'simple', 'advanced', 'filter'
  searchQuery   String?  @db.Text        // 主要搜索词
  searchFields  Json?                    // 搜索的字段 {"productName": "迈瑞", "companyName": ""}
  filters       Json?                    // 应用的筛选条件
  sortBy        String?  @db.VarChar(50) // 排序字段
  sortOrder     String?  @db.VarChar(10) // 'asc', 'desc'
  resultsCount  Int?                     // 搜索结果数量
  searchTime    Int?                     // 搜索耗时(毫秒)
  ip            String   @db.VarChar(50)
  userAgent     String?  @db.VarChar(500)
  createdAt     DateTime @default(now())
  user          User?    @relation(fields: [userId], references: [id])

  // 索引优化
  @@index([createdAt])
  @@index([databaseCode])
  @@index([searchType])
  @@index([userId])
  @@index([sessionId])
  @@index([databaseCode, createdAt])
  @@index([searchType, databaseCode])
}

model BlockedIp {
  id        String   @id @default(uuid())
  ip        String   @unique @db.VarChar(50)
  reason    String?
  expiresAt DateTime
  createdAt DateTime @default(now())
}

model DataChangeLog {
  id              String   @id @default(uuid())
  businessKey     String   @db.VarChar(200)
  businessKeyHash String?
  operation       String   @db.VarChar(20)
  oldData         Json?
  newData         Json?
  changeReason    String?  @db.VarChar(500)
  importedBy      String?  @db.VarChar(100)
  importedFrom    String?  @db.VarChar(200)
  databaseCode    String?  @db.VarChar(50) // 新增，标识数据来源
  createdAt       DateTime @default(now())
  
  @@index([businessKey])
  @@index([businessKeyHash])
  @@index([operation])
  @@index([createdAt])
  @@index([databaseCode])
}

// 联系表单提交记录
model ContactSubmission {
  id        String   @id @default(uuid())
  name      String   @db.VarChar(100)
  email     String   @db.VarChar(255)
  subject   String   @db.VarChar(200)
  category  String   @db.VarChar(50)
  message   String   @db.Text
  ip        String?  @db.VarChar(45)
  userAgent String?  @db.VarChar(500)
  status    String   @default("pending") @db.VarChar(20) // pending, read, replied, closed
  adminNotes String? @db.Text
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([email])
  @@index([category])
  @@index([status])
  @@index([createdAt])
}

// ==== 枚举定义 ====
enum FieldType {
  text
  date
  number
  boolean
  select
  json
}

enum SearchType {
  exact
  contains
  range
  date_range
  starts_with
  ends_with
}

enum FilterType {
  select
  input
  date_range
  checkbox
  multi_select
  range
} 