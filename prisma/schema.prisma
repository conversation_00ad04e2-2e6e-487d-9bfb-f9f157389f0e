generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id               String            @id @default(uuid())
  email            String            @unique @db.VarChar(255)
  password         String
  name             String            @db.VarChar(100)
  membershipType   String            @default("free") @db.VarChar(20)
  membershipExpiry DateTime?
  isActive         Boolean           @default(true)
  emailVerified    Boolean           @default(false)
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
  activityLogs     ActivityLog[]
  searchAnalytics  SearchAnalytics[]
  sessions         UserSession[]
}

model DatabaseConfig {
  id                 String   @id @default(dbgenerated("gen_random_uuid()"))
  code               String   @unique @db.VarChar(50)
  name               String   @db.VarChar(100)
  category           String   @db.VarChar(50)
  description        String?
  accessLevel        String   @default("free") @db.VarChar(20)
  isActive           Boolean  @default(true)
  sortOrder          Int      @default(autoincrement())
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
  defaultSort        Json?
  modelName          String?  @db.VarChar(100)
  tableName          String?  @db.VarChar(100)
  maxExportLimit     Int?     @default(10000)
  defaultExportLimit Int?     @default(1000)
  exportConfig       Json?
}

model UserSession {
  id        String   @id @default(uuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Company {
  id                  String    @id @default(uuid())
  companyName         String
  companyCode         String?   @db.VarChar(50)
  companyShortName    String?
  region              String    @db.VarChar(50)
  province            String?
  city                String?
  address             String?
  phone               String?
  email               String?
  website             String?
  establishDate       DateTime?
  registeredCapital   String?
  legalRepresentative String?
  businessScope       String?
  companyType         String?
  industryCategory    String?
  isListed            Boolean?  @default(false)
  stockCode           String?
  notes               String?
  database            String    @db.VarChar(50)
  businessKey         String    @unique @db.VarChar(200)
  businessKeyHash     String?   @unique
  dataVersion         Int       @default(1)
  isActive            Boolean   @default(true)
  importedAt          DateTime  @default(now())
  updatedAt           DateTime  @updatedAt
  createdAt           DateTime  @default(now())

  @@index([database])
  @@index([businessKey])
  @@index([businessKeyHash])
}

model ActivityLog {
  id          String   @id @default(uuid())
  userId      String?
  ip          String   @db.VarChar(50)
  userAgent   String?  @db.VarChar(500)
  path        String   @db.VarChar(500)
  method      String   @db.VarChar(20)
  queryParams String?
  createdAt   DateTime @default(now())
  database    String?  @db.VarChar(50)
  eventType   String?  @db.VarChar(50)
  referer     String?  @db.VarChar(500)
  sessionId   String?  @db.VarChar(100)
  user        User?    @relation(fields: [userId], references: [id])

  @@index([createdAt])
  @@index([eventType])
  @@index([database])
  @@index([sessionId])
  @@index([ip])
  @@index([userId])
  @@index([createdAt, eventType])
  @@index([createdAt, database])
  @@index([sessionId, eventType])
}

model SearchAnalytics {
  id           String   @id @default(uuid())
  userId       String?
  sessionId    String?  @db.VarChar(100)
  database     String   @db.VarChar(50)
  searchType   String   @db.VarChar(20)
  searchQuery  String?
  searchFields Json?
  filters      Json?
  sortBy       String?  @db.VarChar(50)
  sortOrder    String?  @db.VarChar(10)
  resultsCount Int?
  searchTime   Int?
  ip           String   @db.VarChar(50)
  userAgent    String?  @db.VarChar(500)
  createdAt    DateTime @default(now())
  user         User?    @relation(fields: [userId], references: [id])

  @@index([createdAt])
  @@index([database])
  @@index([searchType])
  @@index([userId])
  @@index([sessionId])
  @@index([database, createdAt])
  @@index([searchType, database])
}

model BlockedIp {
  id        String   @id @default(uuid())
  ip        String   @unique @db.VarChar(50)
  reason    String?
  expiresAt DateTime
  createdAt DateTime @default(now())
}

model DataChangeLog {
  id              String   @id @default(uuid())
  businessKey     String   @db.VarChar(200)
  operation       String   @db.VarChar(20)
  oldData         Json?
  newData         Json?
  changeReason    String?  @db.VarChar(500)
  importedBy      String?  @db.VarChar(100)
  importedFrom    String?  @db.VarChar(200)
  createdAt       DateTime @default(now())
  businessKeyHash String?

  @@index([businessKey])
  @@index([businessKeyHash])
  @@index([operation])
  @@index([createdAt])
}

model FieldConfig {
  id                     String         @id @default(uuid())
  databaseCode           String         @db.VarChar(50)
  fieldName              String         @db.VarChar(100)
  displayName            String         @db.VarChar(100)
  fieldType              FieldType      @default(text)
  isVisible              Boolean        @default(true)
  isSearchable           Boolean        @default(false)
  isFilterable           Boolean        @default(false)
  isSortable             Boolean        @default(false)
  sortOrder              Int            @default(0)
  listOrder              Int            @default(0)
  detailOrder            Int            @default(0)
  searchType             SearchType     @default(contains)
  filterType             FilterType     @default(select)
  validationRules        Json?
  options                Json?
  isActive               Boolean        @default(true)
  createdAt              DateTime       @default(now())
  updatedAt              DateTime       @updatedAt
  todetail               Boolean        @default(false)
  isStatisticsEnabled    Boolean        @default(false)
  statisticsConfig       Json?
  statisticsDisplayName  String?        @db.VarChar(100)
  statisticsOrder        Int            @default(0)
  statisticsType         StatisticsType @default(count)
  statisticsSortOrder    String?        @default("desc") @db.VarChar(10)
  statisticsDefaultLimit Int            @default(5)
  statisticsMaxLimit     Int            @default(50)
  isExportable           Boolean?       @default(true)
  exportOrder            Int?           @default(0)
  exportDisplayName      String?        @db.VarChar(100)
  filterOrder            Int            @default(0)
  isAdvancedSearchable   Boolean        @default(false)

  @@unique([databaseCode, fieldName])
  @@index([databaseCode])
  @@index([isActive])
  @@index([isStatisticsEnabled])
  @@index([statisticsDefaultLimit])
  @@index([statisticsOrder])
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model USPremarketNotification {
  id               String?   @default(uuid())
  knumber          String?
  applicant        String?
  contact          String?
  street1          String?
  street2          String?
  city             String?
  state            String?
  country_code     String?
  zip              String?
  postal_code      String?
  datereceived     DateTime? @db.Date
  decisiondate     DateTime? @db.Date
  decision         String?
  reviewadvisecomm String?
  productcode      String?
  stateorsumm      String?
  classadvisecomm  String?
  sspindicator     String?
  type             String?
  thirdparty       String?
  expeditedreview  String?
  devicename       String?
  source_file      String?
  source_time      String?
  decision_year    String?

  @@map("us_pmn")
  @@ignore
}

model ContactSubmission {
  id         String   @id @default(uuid())
  name       String   @db.VarChar(100)
  email      String   @db.VarChar(255)
  subject    String   @db.VarChar(200)
  category   String   @db.VarChar(50)
  message    String
  ip         String?  @db.VarChar(45)
  userAgent  String?  @db.VarChar(500)
  status     String   @default("pending") @db.VarChar(20)
  adminNotes String?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@index([email])
  @@index([category])
  @@index([status])
  @@index([createdAt])
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model USClass {
  id                          String? @default(uuid())
  review_panel                String?
  medicalspecialty            String?
  productcode                 String?
  devicename                  String?
  deviceclass                 String?
  unclassified_reason         String?
  gmpexemptflag               String?
  thirdpartyflag              String?
  reviewcode                  String?
  regulationnumber            String?
  submission_type_id          String?
  definition                  String?
  physicalstate               String?
  technicalmethod             String?
  targetarea                  String?
  implant_flag                String?
  life_sustain_support_flag   String?
  summarymalfunctionreporting String?
  source_file                 String?
  source_time                 String?

  @@map("us_class")
  @@ignore
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model SearchConfig {
  id              String   @id
  code            String   @unique @db.VarChar(100)
  name            String   @db.VarChar(200)
  description     String?
  configType      String   @default("SIMPLE_FILTER")
  targetDatabases Json
  searchFields    Json
  displayOrder    Int      @default(0)
  filterType      String   @default("input")
  placeholder     String?  @db.VarChar(200)
  customLogic     String?
  validationRules Json?
  options         Json?
  accessLevel     String   @default("free") @db.VarChar(20)
  isActive        Boolean  @default(true)
  isAdvanced      Boolean  @default(false)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @default(now())

  @@index([configType])
  @@index([displayOrder])
  @@index([isActive])
}

enum FieldType {
  text
  date
  number
  boolean
  select
  json
}

enum SearchType {
  exact
  contains
  range
  date_range
  starts_with
  ends_with
}

enum FilterType {
  select
  input
  date_range
  checkbox
  multi_select
  range
}

enum StatisticsType {
  count
  sum
  avg
  min_max
  group_by
}
