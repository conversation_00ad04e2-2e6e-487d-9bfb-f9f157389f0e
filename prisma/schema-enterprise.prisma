generator client {
  provider = "prisma-client-js"
  output   = "./generated/enterprise"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ==== 企业级权限管理系统 ====

// 用户模型 - 简化版，专注权限
model User {
  id               String         @id @default(uuid())
  email            String         @unique @db.VarChar(255)
  password         String
  name             String         @db.VarChar(100)
  isActive         Boolean        @default(true)
  emailVerified    <PERSON>olean        @default(false)
  
  // 权限相关字段
  membershipType   MembershipType @default(FREE)
  membershipExpiry DateTime?
  lastLoginAt      DateTime?
  
  // 时间戳
  createdAt        DateTime       @default(now())
  updatedAt        DateTime       @updatedAt
  
  // 关系
  userRoles        UserRole[]
  userPermissions  UserPermission[]
  sessions         UserSession[]
  activityLogs     ActivityLog[]
  searchAnalytics  SearchAnalytics[]
  
  @@index([email])
  @@index([membershipType])
  @@index([isActive])
}

// 角色模型 - RBAC核心
model Role {
  id          String  @id @default(uuid())
  name        String  @unique @db.Var<PERSON>har(100)
  displayName String  @db.VarChar(200)
  description String?
  
  // 角色属性
  priority    Int     @default(0)              // 角色优先级
  isSystem    Boolean @default(false)          // 系统内置角色
  isActive    Boolean @default(true)
  
  // 角色继承
  parentRoleId String?
  parentRole   Role?   @relation("RoleHierarchy", fields: [parentRoleId], references: [id])
  childRoles   Role[]  @relation("RoleHierarchy")
  
  // 时间戳
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // 关系
  userRoles        UserRole[]
  rolePermissions  RolePermission[]
  
  @@index([name])
  @@index([priority])
  @@index([isActive])
}

// 权限模型 - 细粒度权限控制
model Permission {
  id          String @id @default(uuid())
  name        String @unique @db.VarChar(200)  // 权限标识符，如 "database:us_class:read"
  displayName String @db.VarChar(200)
  description String?
  
  // 权限分类
  category    String @db.VarChar(100)          // 权限分类：database, feature, admin
  resource    String @db.VarChar(100)          // 资源标识：us_class, export, analytics
  action      String @db.VarChar(50)           // 操作类型：read, write, delete, export
  
  // 权限属性
  isSystem    Boolean @default(false)          // 系统内置权限
  isActive    Boolean @default(true)
  
  // 权限条件（JSON格式存储复杂条件）
  conditions  Json?                            // 如：{"time_range": "09:00-18:00", "ip_whitelist": []}
  
  // 时间戳
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // 关系
  rolePermissions RolePermission[]
  userPermissions UserPermission[]
  
  @@unique([resource, action])
  @@index([category])
  @@index([resource])
  @@index([isActive])
}

// 用户-角色关联表
model UserRole {
  id     String @id @default(uuid())
  userId String
  roleId String
  
  // 关联属性
  grantedAt DateTime @default(now())
  grantedBy String?                    // 授权人ID
  expiresAt DateTime?                  // 角色过期时间
  isActive  Boolean  @default(true)
  
  // 关系
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  role Role @relation(fields: [roleId], references: [id], onDelete: Cascade)
  
  @@unique([userId, roleId])
  @@index([userId])
  @@index([roleId])
  @@index([expiresAt])
}

// 角色-权限关联表
model RolePermission {
  id           String @id @default(uuid())
  roleId       String
  permissionId String
  
  // 关联属性
  grantedAt DateTime @default(now())
  grantedBy String?                    // 授权人ID
  isActive  Boolean  @default(true)
  
  // 权限覆盖条件（可覆盖权限的默认条件）
  overrideConditions Json?
  
  // 关系
  role       Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  
  @@unique([roleId, permissionId])
  @@index([roleId])
  @@index([permissionId])
}

// 用户直接权限表（特殊情况下的直接授权）
model UserPermission {
  id           String @id @default(uuid())
  userId       String
  permissionId String
  
  // 关联属性
  grantedAt DateTime @default(now())
  grantedBy String?                    // 授权人ID
  expiresAt DateTime?                  // 权限过期时间
  isActive  Boolean  @default(true)
  
  // 权限覆盖条件
  overrideConditions Json?
  
  // 关系
  user       User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  
  @@unique([userId, permissionId])
  @@index([userId])
  @@index([permissionId])
  @@index([expiresAt])
}

// 用户会话模型 - 增强版
model UserSession {
  id          String   @id @default(uuid())
  userId      String
  sessionId   String   @unique                  // JWT Session ID
  
  // 会话信息
  ipAddress   String?  @db.VarChar(45)         // 支持IPv6
  userAgent   String?
  deviceInfo  Json?                            // 设备信息
  
  // 权限缓存（性能优化）
  permissionCache Json?                        // 缓存用户权限，减少查询
  cacheExpiry     DateTime?
  
  // 会话状态
  isActive    Boolean  @default(true)
  lastActivity DateTime @default(now())
  expiresAt   DateTime
  
  // 时间戳
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // 关系
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@index([userId])
  @@index([sessionId])
  @@index([expiresAt])
  @@index([lastActivity])
}

// ==== 配置管理系统 ====

// 数据库配置 - 增强版
model DatabaseConfig {
  id          String   @id @default(uuid())
  code        String   @unique @db.VarChar(50)
  name        String   @db.VarChar(100)
  category    String   @db.VarChar(50)
  description String?
  
  // 权限配置
  defaultAccessLevel MembershipType @default(PREMIUM)
  requiresAuth       Boolean        @default(true)
  
  // 数据库映射
  tableName   String?  @db.VarChar(100)        // PostgreSQL实际表名
  modelName   String?  @db.VarChar(100)        // Prisma模型名
  
  // 状态和排序
  isActive    Boolean  @default(true)
  sortOrder   Int      @default(autoincrement())
  
  // 配额限制
  quotaConfig Json?                            // 查询限制、导出限制等
  
  // 时间戳
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // 关系
  fieldConfigs FieldConfig[]
  
  @@index([code])
  @@index([category])
  @@index([isActive])
}

// 字段配置 - 保持现有结构
model FieldConfig {
  id            String      @id @default(uuid())
  databaseCode  String      @db.VarChar(50)
  fieldName     String      @db.VarChar(100)
  displayName   String      @db.VarChar(200)
  fieldType     FieldType
  
  // 显示和功能配置
  isVisible     Boolean     @default(true)
  isSearchable  Boolean     @default(false)
  isFilterable  Boolean     @default(false)
  isSortable    Boolean     @default(false)
  
  // 排序和布局
  sortOrder     Int         @default(0)
  listOrder     Int         @default(0)
  detailOrder   Int         @default(0)
  
  // 搜索和筛选配置
  searchType    SearchType  @default(CONTAINS)
  filterType    FilterType  @default(INPUT)
  
  // 状态
  isActive      Boolean     @default(true)
  
  // 时间戳
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
  
  // 关系
  database DatabaseConfig @relation(fields: [databaseCode], references: [code], onDelete: Cascade)
  
  @@unique([databaseCode, fieldName])
  @@index([databaseCode])
  @@index([isActive])
}

// ==== 审计和监控系统 ====

// 权限变更审计日志
model PermissionAuditLog {
  id          String   @id @default(uuid())
  
  // 操作信息
  operation   String   @db.VarChar(50)          // CREATE, UPDATE, DELETE, GRANT, REVOKE
  entityType  String   @db.VarChar(50)          // USER, ROLE, PERMISSION
  entityId    String                            // 被操作实体的ID
  
  // 操作者信息
  operatorId  String                            // 操作者用户ID
  operatorIp  String?  @db.VarChar(45)
  
  // 变更详情
  beforeData  Json?                             // 变更前数据
  afterData   Json?                             // 变更后数据
  reason      String?                           // 变更原因
  
  // 时间戳
  createdAt   DateTime @default(now())
  
  @@index([operation])
  @@index([entityType])
  @@index([operatorId])
  @@index([createdAt])
}

// 活动日志 - 增强版
model ActivityLog {
  id          String   @id @default(uuid())
  userId      String?
  
  // 活动信息
  action      String   @db.VarChar(100)         // 操作类型
  resource    String?  @db.VarChar(200)         // 访问资源
  method      String?  @db.VarChar(10)          // HTTP方法
  path        String?  @db.VarChar(500)         // 请求路径
  
  // 请求信息
  ipAddress   String?  @db.VarChar(45)
  userAgent   String?
  
  // 响应信息
  statusCode  Int?
  responseTime Int?                             // 响应时间（毫秒）
  
  // 额外数据
  metadata    Json?                             // 扩展信息
  
  // 时间戳
  createdAt   DateTime @default(now())
  
  // 关系
  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)
  
  @@index([userId])
  @@index([action])
  @@index([resource])
  @@index([createdAt])
  @@index([ipAddress])
}

// 搜索分析 - 保持现有结构但增强
model SearchAnalytics {
  id          String   @id @default(uuid())
  userId      String?
  
  // 搜索信息
  database    String   @db.VarChar(50)
  searchTerm  String   @db.VarChar(500)
  filters     Json?
  
  // 结果信息
  resultCount Int?
  responseTime Int?                             // 搜索响应时间
  
  // 会话信息
  sessionId   String?  @db.VarChar(255)
  ipAddress   String?  @db.VarChar(45)
  
  // 时间戳
  createdAt   DateTime @default(now())
  
  // 关系
  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)
  
  @@index([userId])
  @@index([database])
  @@index([searchTerm])
  @@index([createdAt])
}

// ==== 枚举类型定义 ====

enum MembershipType {
  FREE
  PREMIUM
  ENTERPRISE
  ADMIN
}

enum FieldType {
  text
  number
  boolean
  date
  select
  multiselect
  json
}

enum SearchType {
  EXACT
  CONTAINS
  STARTS_WITH
  ENDS_WITH
  REGEX
}

enum FilterType {
  INPUT
  SELECT
  MULTISELECT
  DATE_RANGE
  NUMBER_RANGE
  CHECKBOX
} 