import { NextRequest, NextResponse } from 'next/server';
import { JWTPayload, PermissionChecker } from '@/lib/enterprise-auth/jwt-manager';
import jwt from 'jsonwebtoken';

// 企业级权限中间件配置
const AUTH_CONFIG = {
  ACCESS_TOKEN_SECRET: process.env.JWT_ACCESS_SECRET || 'your-super-secret-access-key',
  
  // 免权限路径（公开访问）
  PUBLIC_PATHS: [
    '/api/health',
    '/api/captcha',
    '/api/contact',
    '/login',
    '/register',
    '/api/auth/login',
    '/api/auth/register',
  ],
  
  // 免费访问的数据库 - 注意：这应该从数据库动态读取
  // TODO: 重构为动态配置加载
  FREE_DATABASES: ['freepat', 'devicecnimported', 'us_class', 'us_pmn'],
  
  // API路径模式匹配
  API_PATTERNS: {
    DATA_ACCESS: /^\/api\/data\/([^\/]+)/,
    META_ACCESS: /^\/api\/meta\/([^\/]+)/,
    EXPORT_ACCESS: /^\/api\/export\/([^\/]+)/,
    STATS_ACCESS: /^\/api\/stats\/([^\/]+)/,
    SEARCH_ACCESS: /^\/api\/advanced-search\/([^\/]+)/,
  },
};

/**
 * 企业级权限中间件
 * 特点：
 * 1. 毫秒级响应时间
 * 2. 零数据库查询（基于JWT缓存）
 * 3. 细粒度权限控制
 * 4. 自动配额检查
 */
export async function enterpriseAuthMiddleware(_request: NextRequest): Promise<NextResponse | null> {
  const startTime = Date.now();
  const pathname = _request.nextUrl.pathname;
  
  try {
    // 1. 检查是否为公开路径
    if (isPublicPath(pathname)) {
      return null; // 继续处理
    }
    
    // 2. 提取并验证JWT令牌
    const token = extractAuthToken(_request);
    if (!token) {
      return createUnauthorizedResponse('缺少访问令牌');
    }
    
    // 3. 快速JWT验证（无数据库查询）
    const payload = await fastVerifyToken(token);
    if (!payload) {
      return createUnauthorizedResponse('令牌无效或已过期');
    }
    
    // 4. 提取资源和操作信息
    const resourceInfo = extractResourceInfo(pathname, _request.method);
    if (!resourceInfo) {
      return null; // 非资源访问路径，继续处理
    }
    
    // 5. 权限验证
    const hasPermission = checkPermission(payload.permissions, resourceInfo);
    if (!hasPermission) {
      return createForbiddenResponse('权限不足', resourceInfo);
    }
    
         // 6. 配额检查
     const quotaCheck = await checkQuota(payload, resourceInfo);
     if (!quotaCheck.allowed) {
       return createQuotaExceededResponse(quotaCheck.message || '配额超限');
    }
    
    // 7. 添加权限信息到请求头（供后续API使用）
    const response = NextResponse.next();
    response.headers.set('X-User-ID', payload.userId);
    response.headers.set('X-User-Permissions', JSON.stringify(payload.permissions));
    response.headers.set('X-Auth-Time', (Date.now() - startTime).toString());
    
    return response;
    
  } catch (__error) {
    console.error('[EnterpriseAuth] 权限验证失败:', __error);
    return createInternalErrorResponse();
  }
}

/**
 * 检查是否为公开路径
 */
function isPublicPath(pathname: string): boolean {
  return AUTH_CONFIG.PUBLIC_PATHS.some(path => {
    if (path.endsWith('*')) {
      return pathname.startsWith(path.slice(0, -1));
    }
    return pathname === path || pathname.startsWith(path + '/');
  });
}

/**
 * 提取认证令牌
 */
function extractAuthToken(_request: NextRequest): string | null {
  // 1. 从Authorization头获取
  const authHeader = _request.headers.get('Authorization');
  if (authHeader?.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  // 2. 从Cookie获取
  const tokenCookie = _request.cookies.get('auth-token');
  if (tokenCookie?.value) {
    return tokenCookie.value;
  }
  
  return null;
}

/**
 * 快速JWT验证（仅验证签名和过期时间，不查询数据库）
 */
async function fastVerifyToken(token: string): Promise<JWTPayload | null> {
  try {
    const payload = jwt.verify(token, AUTH_CONFIG.ACCESS_TOKEN_SECRET) as JWTPayload;
    
    // 基本验证
    if (!payload.userId || !payload.permissions || payload.exp * 1000 < Date.now()) {
      return null;
    }
    
    return payload;
  } catch (__error) {
    return null;
  }
}

/**
 * 提取资源信息
 */
function extractResourceInfo(pathname: string, method: string): {
  type: string;
  database: string;
  action: string;
} | null {
  // 数据访问
  const dataMatch = pathname.match(AUTH_CONFIG.API_PATTERNS.DATA_ACCESS);
  if (dataMatch) {
    return {
      type: 'database',
      database: dataMatch[1].toLowerCase(),
      action: method === 'GET' ? 'read' : 'write',
    };
  }
  
  // 元数据访问
  const metaMatch = pathname.match(AUTH_CONFIG.API_PATTERNS.META_ACCESS);
  if (metaMatch) {
    return {
      type: 'database',
      database: metaMatch[1].toLowerCase(),
      action: 'read',
    };
  }
  
  // 导出访问
  const exportMatch = pathname.match(AUTH_CONFIG.API_PATTERNS.EXPORT_ACCESS);
  if (exportMatch) {
    return {
      type: 'database',
      database: exportMatch[1].toLowerCase(),
      action: 'export',
    };
  }
  
  // 统计访问
  const statsMatch = pathname.match(AUTH_CONFIG.API_PATTERNS.STATS_ACCESS);
  if (statsMatch) {
    return {
      type: 'database',
      database: statsMatch[1].toLowerCase(),
      action: 'read',
    };
  }
  
  // 高级搜索
  const searchMatch = pathname.match(AUTH_CONFIG.API_PATTERNS.SEARCH_ACCESS);
  if (searchMatch) {
    return {
      type: 'database',
      database: searchMatch[1].toLowerCase(),
      action: 'search',
    };
  }
  
  return null;
}

/**
 * 权限检查
 */
function checkPermission(
  permissions: JWTPayload['permissions'], 
  resourceInfo: { type: string; database: string; action: string }
): boolean {
  if (resourceInfo.type === 'database') {
    // 检查免费数据库
    if (AUTH_CONFIG.FREE_DATABASES.includes(resourceInfo.database)) {
      return true;
    }
    
    // 检查数据库权限
    return PermissionChecker.hasDataBaseAccess(permissions, resourceInfo.database, resourceInfo.action);
  }
  
  return false;
}

/**
 * 配额检查（简化版，实际应该查询Redis计数器）
 */
async function checkQuota(
  payload: JWTPayload, 
  resourceInfo: { type: string; database: string; action: string }
): Promise<{ allowed: boolean; message?: string }> {
  // 管理员和企业版无限制
  if (payload.membershipType === 'ADMIN' || payload.membershipType === 'ENTERPRISE') {
    return { allowed: true };
  }
  
  // 导出权限检查
  if (resourceInfo.action === 'export') {
    if (payload.permissions.quotas.exportLimit === 0) {
      return { 
        allowed: false, 
        message: '您的会员级别不支持数据导出功能，请升级至高级会员' 
      };
    }
  }
  
  // 这里应该实现实际的配额计数检查
  // 例如：检查今日查询次数、导出次数等
  
  return { allowed: true };
}

/**
 * 创建未授权响应
 */
function createUnauthorizedResponse(message: string): NextResponse {
  return NextResponse.json({
    success: false,
    error: message,
    code: 'UNAUTHORIZED',
    timestamp: new Date().toISOString(),
  }, { status: 401 });
}

/**
 * 创建权限不足响应
 */
function createForbiddenResponse(message: string, resourceInfo: Record<string, unknown>): NextResponse {
  return NextResponse.json({
    success: false,
    error: message,
    code: 'FORBIDDEN',
    resource: resourceInfo,
    suggestion: '请升级您的会员级别以获得访问权限',
    timestamp: new Date().toISOString(),
  }, { status: 403 });
}

/**
 * 创建配额超限响应
 */
function createQuotaExceededResponse(message: string): NextResponse {
  return NextResponse.json({
    success: false,
    error: message,
    code: 'QUOTA_EXCEEDED',
    suggestion: 'Please upgrade your membership level or wait for quota reset',
    timestamp: new Date().toISOString(),
  }, { status: 429 });
}

/**
 * Create internal error response
 */
function createInternalErrorResponse(): NextResponse {
  return NextResponse.json({
    success: false,
    error: 'Permission verification service temporarily unavailable',
    code: 'INTERNAL_ERROR',
    timestamp: new Date().toISOString(),
  }, { status: 500 });
}

/**
 * 权限路由守卫（用于页面级权限控制）
 */
export function createPermissionGuard(___requiredPermission: {
  database?: string;
  action?: string;
  feature?: string;
  role?: string;
}) {
  return async (_request: NextRequest): Promise<NextResponse | null> => {
    const token = extractAuthToken(_request);
    if (!token) {
      // 重定向到登录页
      return NextResponse.redirect(new URL('/login', _request.url));
    }

    const payload = await fastVerifyToken(token);
    if (!payload) {
      return NextResponse.redirect(new URL('/login', _request.url));
    }
    
    // 检查权限
    let hasPermission = true;
    
    if (___requiredPermission.database && ___requiredPermission.action) {
      hasPermission = PermissionChecker.hasDataBaseAccess(
        payload.permissions,
        ___requiredPermission.database,
        ___requiredPermission.action
      );
    }

    if (___requiredPermission.feature) {
      hasPermission = hasPermission && PermissionChecker.hasFeatureAccess(
        payload.permissions,
        ___requiredPermission.feature
      );
    }

    if (___requiredPermission.role) {
      hasPermission = hasPermission && PermissionChecker.hasRole(
        payload.permissions,
        ___requiredPermission.role
      );
    }
    
    if (!hasPermission) {
      // 重定向到权限不足页面
      return NextResponse.redirect(new URL('/access-denied', _request.url));
    }
    
    return null; // 权限通过
  };
}

/**
 * API权限装饰器（用于API路由）
 */
export function withPermission(_requiredPermission: {
  database?: string;
  action?: string;
  feature?: string;
  membershipType?: 'FREE' | 'PREMIUM' | 'ENTERPRISE' | 'ADMIN';
}) {
  return function <T extends (...args: Record<string, unknown>[]) => any>(
    target: Record<string, unknown>,
    propertyName: string,
    descriptor: TypedPropertyDescriptor<T>
  ): TypedPropertyDescriptor<T> | void {
    const method = descriptor.value!;
    
    descriptor.value = (async function(this: Record<string, unknown>, request: NextRequest, ...args: Record<string, unknown>[]) {
      // 从请求头获取权限信息（由中间件设置）
      const userPermissions = request.headers.get('X-User-Permissions');
      const userId = request.headers.get('X-User-ID');

      if (!userPermissions || !userId) {
        return NextResponse.json({
          success: false,
          error: '权限验证失败',
        }, { status: 401 });
      }

      const _permissions = JSON.parse(userPermissions) as JWTPayload['permissions'];

      // 权限检查逻辑...
      // (实现具体的权限验证)

      return method.apply(this, [request as unknown as Record<string, unknown>, ...args]);
    }) as unknown as T;
    
    return descriptor;
  };
} 