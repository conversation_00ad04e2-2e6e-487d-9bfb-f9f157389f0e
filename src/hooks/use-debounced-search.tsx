import { useState, useEffect, useCallback, useRef } from 'react';

interface UseDebounceSearchProps {
  onSearch: (query: string) => void;
  delay?: number;
  minLength?: number;
}

export function useDebounceSearch({
  onSearch,
  delay = 300,
  minLength = 2
}: UseDebounceSearchProps) {
  const [query, setQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [hasUserInteracted, setHasUserInteracted] = useState(false);

  // Use ref to store the debounced function to prevent recreation
  const debouncedSearchRef = useRef<NodeJS.Timeout | null>(null);

  // 创建防抖搜索函数
  const debouncedSearch = useCallback((searchQuery: string) => {
    if (debouncedSearchRef.current) {
      clearTimeout(debouncedSearchRef.current);
    }

    debouncedSearchRef.current = setTimeout(() => {
      if (searchQuery.length >= minLength || searchQuery.length === 0) {
        onSearch(searchQuery);
      }
      setIsSearching(false);
    }, delay);
  }, [onSearch, delay, minLength]);

  // 当查询改变时触发防抖搜索
  useEffect(() => {
    // Only trigger search if user has interacted or query has content
    if (hasUserInteracted && (query.length >= minLength || query.length === 0)) {
      setIsSearching(true);
      debouncedSearch(query);
    }
  }, [query, debouncedSearch, minLength, hasUserInteracted]);

  const handleQueryChange = (newQuery: string) => {
    setHasUserInteracted(true);
    setQuery(newQuery);
  };

  const clearQuery = () => {
    setHasUserInteracted(true);
    setQuery('');
    setIsSearching(false);
  };

  return {
    query,
    setQuery: handleQueryChange,
    clearQuery,
    isSearching,
  };
}
