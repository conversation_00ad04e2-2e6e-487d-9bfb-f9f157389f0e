import { useMemo, useCallback } from 'react';
import { SearchChip } from '@/components/SearchChips';
import { SearchCondition } from '@/lib/api';
import { 
  combineSearchChips, 
  parseChipRemoval 
} from '@/lib/searchChipsUtils';

export interface UseSearchChipsProps {
  filters: Record<string, unknown>;
  advancedConditions: SearchCondition[];
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  fieldConfigs?: Array<{ fieldName: string; displayName: string }>;
  onFilterChange: (key: string, value: unknown) => void;
  onAdvancedConditionRemove: (conditionId: string) => void;
  onSortChange: (sortBy?: string, sortOrder?: 'asc' | 'desc') => void;
  onClearAll?: () => void;
}

export interface UseSearchChipsReturn {
  chips: SearchChip[];
  handleRemoveChip: (chipId: string) => void;
  handleClearAll: () => void;
  hasActiveFilters: boolean;
  chipCount: number;
}

/**
 * 搜索标签管理 Hook
 * 将各种搜索条件统一转换为可视化标签，并提供移除操作
 */
export function useSearchChips({
  filters,
  advancedConditions,
  sortBy,
  sortOrder,
  fieldConfigs,
  onFilterChange,
  onAdvancedConditionRemove,
  onSortChange,
  onClearAll,
}: UseSearchChipsProps): UseSearchChipsReturn {
  
  // 生成搜索标签
  const chips = useMemo(() => {
    return combineSearchChips(
      filters,
      advancedConditions,
      sortBy,
      sortOrder,
      fieldConfigs
    );
  }, [filters, advancedConditions, sortBy, sortOrder, fieldConfigs]);

  // 移除单个标签
  const handleRemoveChip = useCallback((chipId: string) => {
    try {
      const removal = parseChipRemoval(chipId);
      
      switch (removal.type) {
        case 'filter':
          if (removal.key) {
            onFilterChange(removal.key, undefined);
          }
          break;
          
        case 'advanced':
          if (removal.conditionId) {
            onAdvancedConditionRemove(removal.conditionId);
          }
          break;
          
        case 'sort':
          onSortChange(undefined, undefined);
          break;
          
        default:
          console.warn('Unknown chip removal type:', removal);
      }
    } catch (error) {
      console.error('Failed to remove chip:', error);
    }
  }, [onFilterChange, onAdvancedConditionRemove, onSortChange]);

  // 清除所有标签
  const handleClearAll = useCallback(() => {
    if (onClearAll) {
      onClearAll();
    } else {
      // 默认清除逻辑：逐个清除所有条件
      
      // 清除过滤器
      Object.keys(filters).forEach(key => {
        if (filters[key] !== undefined && filters[key] !== null && filters[key] !== '') {
          onFilterChange(key, undefined);
        }
      });
      
      // 清除高级搜索条件
      advancedConditions.forEach(condition => {
        onAdvancedConditionRemove(condition.id);
      });
      
      // 清除排序
      if (sortBy) {
        onSortChange(undefined, undefined);
      }
    }
  }, [filters, advancedConditions, sortBy, onFilterChange, onAdvancedConditionRemove, onSortChange, onClearAll]);

  // 计算状态
  const hasActiveFilters = chips.length > 0;
  const chipCount = chips.length;

  return {
    chips,
    handleRemoveChip,
    handleClearAll,
    hasActiveFilters,
    chipCount,
  };
}

/**
 * 简化版本的搜索标签 Hook，仅处理简单过滤器
 */
export function useSimpleSearchChips(
  filters: Record<string, unknown>,
  onFilterChange: (key: string, value: unknown) => void,
  fieldConfigs?: Array<{ fieldName: string; displayName: string }>
) {
  return useSearchChips({
    filters,
    advancedConditions: [],
    fieldConfigs,
    onFilterChange,
    onAdvancedConditionRemove: () => {},
    onSortChange: () => {},
  });
}

/**
 * 从 URL 搜索参数生成搜索标签的工具函数
 */
export function useUrlSearchChips(
  searchParams: URLSearchParams,
  onParamChange: (key: string, value: string | null) => void,
  fieldConfigs?: Array<{ fieldName: string; displayName: string }>
) {
  // 将 URLSearchParams 转换为 filters 对象
  const filters = useMemo(() => {
    const result: Record<string, unknown> = {};
    searchParams.forEach((value, key) => {
      result[key] = value;
    });
    return result;
  }, [searchParams]);

  // 处理参数变更
  const handleFilterChange = useCallback((key: string, value: unknown) => {
    if (value === undefined || value === null || value === '') {
      onParamChange(key, null); // 移除参数
    } else {
      onParamChange(key, String(value)); // 设置参数
    }
  }, [onParamChange]);

  return useSimpleSearchChips(filters, handleFilterChange, fieldConfigs);
}
