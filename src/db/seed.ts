import { db } from '../lib/prisma';

// 数据库配置数据
const dbConfigs = [
  {
    code: 'freePat',
    name: '医药专利',
    category: '药物研发',
    description: '医药专利信息及原文下载'
  },
  {
    code: 'deviceCNImported',
    name: '医疗器械模板',
    category: 'Regulation',
    description: '新建医疗器械数据库时的基础模板，包含完整的字段配置和表结构'
  },
  {
    code: 'deviceCNEvaluation',
    name: '中国大陆审评',
    category: 'Regulation',
    description: '医疗器械审评进度跟踪、审评结论查询'
  },
  {
    code: 'deviceHK',
    name: '中国香港上市',
    category: 'Regulation',
    description: '中国香港已上市的医疗器械信息'
  },
  {
    code: 'deviceUS',
    name: '美国上市',
    category: '全球器械',
    description: '美国FDA批准的医疗器械信息'
  },
  {
    code: 'deviceJP',
    name: '日本上市',
    category: '全球器械',
    description: '日本PMDA批准的医疗器械信息'
  },
  {
    code: 'deviceUK',
    name: '英国上市',
    category: '全球器械',
    description: '英国MHRA批准的医疗器械信息'
  },
  {
    code: 'subjectNewdrug',
    name: '全球获批新药',
    category: '专题数据',
    description: '全球范围内获得批准的新药信息'
  }
];

// 注意：不再插入示例医疗器械数据
// 所有医疗器械数据应通过正式的数据导入流程添加，而不是通过种子脚本

async function main() {
  try {
    console.error('🌱 Starting database seed data insertion...');

    // Insert database configurations
    console.error('📋 Inserting database configurations...');
    const createdDbConfigs = await db.databaseConfig.createMany({
      data: dbConfigs,
      skipDuplicates: true, // Skip existing records
    });

    // 不再插入医疗器械示例数据
    console.error('ℹ️ 跳过医疗器械示例数据插入 - 使用正式数据导入流程');
    const createdDevices = { count: 0 };





    console.error('✅ 种子数据插入完成！');
    console.error(`📊 插入数据库配置: ${createdDbConfigs.count} 条`);
    console.error(`🔬 插入医疗器械记录: ${createdDevices.count} 条`);

  } catch (__error) {
    console.error('❌ Seed data insertion failed:', __error);
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}

main();
