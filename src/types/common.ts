// Common type definitions for the application

// Database and API related types
export interface DatabaseConfig {
  code: string;
  name: string;
  category: string;
  accessLevel: string;
  tableName: string;
  modelName: string;
  defaultSort?: string;
  maxExportLimit?: number;
  defaultExportLimit?: number;
  isActive: boolean;
}

export interface FieldConfig {
  fieldName: string;
  displayName: string;
  fieldType: string;
  filterType?: 'select' | 'multi_select' | 'checkbox' | 'date_range' | 'text';
  isFilterable: boolean;
  isSearchable: boolean;
  isVisible: boolean;
  sortOrder: number;
  databaseCode: string;
  isActive: boolean;
}

// API Response types
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Search and Filter types
export interface SearchParams {
  query?: string;
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  filters?: Record<string, string | string[]>;
}

export interface FilterOption {
  value: string;
  label: string;
  count?: number;
}

// Analytics types
export interface AnalyticsEvent {
  event: string;
  data?: Record<string, unknown>;
  timestamp?: number;
  sessionId?: string;
  userId?: string;
}

export interface ActivityLogEntry {
  id: string;
  userId?: string | null;
  ip: string;
  userAgent?: string;
  path: string;
  method: string;
  queryParams?: string;
  referer?: string;
  database?: string;
  eventType: string;
  sessionId?: string | null;
  createdAt: Date;
}

// Component Props types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

// Form types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'select' | 'checkbox' | 'textarea';
  required?: boolean;
  placeholder?: string;
  options?: { value: string; label: string }[];
}

// Error types
export interface AppError {
  code: string;
  message: string;
  details?: Record<string, unknown>;
}

// User and Auth types
export interface User {
  id: string;
  email: string;
  name: string;
  role: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Session {
  userId: string;
  user: User;
  expires: Date;
}

// Statistics types
export interface StatsData {
  overview: {
    totalCount: number;
    activeCount: number;
    recentUpdates: number;
    inactiveCount: number;
  };
  categories: Array<{ name: string; count: number }>;
  companies: Array<{ name: string; count: number }>;
  managementTypes: Array<{ name: string; count: number }>;
  yearlyApprovals: Array<{ year: number; count: number }>;
  specialCategories: {
    innovative: number;
    clinicalNeed: number;
    childrenSpecific: number;
    rareDisease: number;
  };
}

// Table and Data Display types
export interface TableColumn {
  key: string;
  label: string;
  sortable?: boolean;
  filterable?: boolean;
  width?: number;
  align?: 'left' | 'center' | 'right';
}

export interface TableData {
  [key: string]: unknown;
}

// Export types
export interface ExportOptions {
  format: 'csv' | 'excel' | 'json';
  fields?: string[];
  filters?: Record<string, unknown>;
  limit?: number;
}

// Utility types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// Hook types
export interface UseAsyncState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

// Layout types
export interface LayoutState {
  headerTop: number;
  tableMarginTop: number;
  tableMaxHeight: string;
  isLayoutStable: boolean;
}

// Search types
export interface SearchState {
  query: string;
  isSearching: boolean;
  hasUserInteracted: boolean;
}

// Permission types
export interface Permission {
  resource: string;
  action: string;
  level: 'read' | 'write' | 'admin';
}

export interface AccessLevel {
  level: number;
  name: string;
  permissions: Permission[];
}
