/**
 * API 相关的类型定义
 * 用于替换项目中的 any 类型
 */

// 基础 API 响应类型
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 分页响应类型
export interface PaginatedResponse<T = unknown> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 数据库记录类型
export interface DatabaseRecord {
  id: string | number;
  [key: string]: unknown;
}

// 搜索参数类型
export interface SearchParams {
  query?: string;
  page?: number;
  limit?: number;
  filters?: Record<string, unknown>;
  sort?: string;
  order?: 'asc' | 'desc';
}

// 高级搜索参数
export interface AdvancedSearchParams extends SearchParams {
  fields?: string[];
  dateRange?: {
    start: string;
    end: string;
  };
  categories?: string[];
}

// 统计数据类型
export interface StatisticsData {
  [key: string]: {
    count: number;
    percentage?: number;
    label?: string;
  };
}

// 导出配置类型
export interface ExportConfig {
  format: 'csv' | 'excel' | 'json';
  fields: string[];
  limit?: number;
  filters?: Record<string, unknown>;
}

// 数据库配置类型
export interface DatabaseConfig {
  code: string;
  name: string;
  description?: string;
  accessLevel: number;
  isActive: boolean;
  fields: FieldConfig[];
}

// 字段配置类型
export interface FieldConfig {
  name: string;
  label: string;
  type: 'string' | 'number' | 'date' | 'boolean' | 'select' | 'multiselect';
  required?: boolean;
  searchable?: boolean;
  filterable?: boolean;
  sortable?: boolean;
  exportable?: boolean;
  options?: string[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
  };
}

// 用户类型
export interface User {
  id: string;
  username: string;
  email: string;
  role: string;
  permissions: string[];
  accessLevel: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// 认证相关类型
export interface LoginRequest {
  username: string;
  password: string;
  captcha?: string;
}

export interface LoginResponse {
  user: User;
  token: string;
  refreshToken?: string;
  expiresIn: number;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  captcha: string;
}

// 验证码类型
export interface CaptchaResponse {
  id: string;
  image: string;
  audio?: string;
}

// 错误类型
export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, unknown>;
  timestamp: string;
}

// 元数据类型
export interface MetaData {
  totalRecords: number;
  lastUpdated: string;
  version: string;
  schema: Record<string, unknown>;
}

// 动态计数类型
export interface DynamicCounts {
  [fieldName: string]: {
    [value: string]: number;
  };
}

// 分析数据类型
export interface AnalyticsData {
  searchQueries: {
    query: string;
    count: number;
    timestamp: string;
  }[];
  popularFilters: {
    field: string;
    value: string;
    count: number;
  }[];
  userActivity: {
    userId: string;
    action: string;
    timestamp: string;
    metadata?: Record<string, unknown>;
  }[];
}

// 配置刷新类型
export interface ConfigRefreshResult {
  success: boolean;
  refreshedConfigs: string[];
  errors: string[];
  timestamp: string;
}

// 健康检查类型
export interface HealthCheckResult {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  services: {
    database: 'up' | 'down';
    redis: 'up' | 'down';
    elasticsearch?: 'up' | 'down';
  };
  version: string;
  uptime: number;
}

// 联系表单类型
export interface ContactForm {
  name: string;
  email: string;
  subject: string;
  message: string;
  captcha: string;
}

// 权限检查类型
export interface PermissionCheck {
  userId: string;
  resource: string;
  action: string;
  context?: Record<string, unknown>;
}

export interface PermissionResult {
  allowed: boolean;
  reason?: string;
  requiredLevel?: number;
  userLevel?: number;
}

// 数据导入类型
export interface ImportResult {
  success: boolean;
  totalRows: number;
  successfulRows: number;
  failedRows: number;
  errors: {
    row: number;
    error: string;
  }[];
  duplicates: number;
  processingTime: number;
}

// 缓存相关类型
export interface CacheConfig {
  ttl: number;
  maxSize: number;
  strategy: 'lru' | 'fifo' | 'lfu';
}

export interface CacheStats {
  hits: number;
  misses: number;
  size: number;
  maxSize: number;
  hitRate: number;
}

// 通用工具类型
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

// 请求处理类型
export interface RequestContext {
  user?: User;
  permissions: string[];
  accessLevel: number;
  sessionId?: string;
  requestId: string;
  timestamp: string;
}

// 响应包装器类型
export type ApiHandler<T = unknown> = (
  request: Request,
  context: RequestContext
) => Promise<ApiResponse<T>>;

// 中间件类型
export type Middleware = (
  request: Request,
  context: RequestContext,
  next: () => Promise<Response>
) => Promise<Response>;
