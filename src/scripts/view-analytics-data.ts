import { db } from '../lib/prisma';

async function viewAnalyticsData() {
  console.error('🔍 Analytics数据查看工具\n');

  try {
    // 1. 查看ActivityLog表数据
    console.error('=== ActivityLog表 (基础活动日志) ===');
    const activityLogs = await db.activityLog.findMany({
      orderBy: { createdAt: 'desc' },
      take: 10,
      select: {
        id: true,
        eventType: true,
        database: true,
        path: true,
        queryParams: true,
        ip: true,
        createdAt: true,
        userId: true,
      }
    });

    console.error(`总记录数: ${await db.activityLog.count()}`);
    console.error('\n最近10条记录:');
    activityLogs.forEach((log, index) => {
      console.error(`${index + 1}. [${log.createdAt.toISOString()}] ${log.eventType || 'pageview'}`);
      console.error(`   数据库: ${log.database || '无'}`);
      console.error(`   路径: ${log.path}`);
      console.error(`   查询参数: ${log.queryParams || '无'}`);
      console.error(`   用户: ${log.userId || '匿名'} | IP: ${log.ip}`);
      console.error('');
    });

    // 2. 查看SearchAnalytics表数据
    console.error('\n=== SearchAnalytics表 (详细搜索分析) ===');
    const searchCount = await db.searchAnalytics.count();
    console.error(`总搜索记录数: ${searchCount}`);

    if (searchCount > 0) {
      const searchAnalytics = await db.searchAnalytics.findMany({
        orderBy: { createdAt: 'desc' },
        take: 5,
        select: {
          id: true,
          database: true,
          searchType: true,
          searchQuery: true,
          searchFields: true,
          filters: true,
          resultsCount: true,
          searchTime: true,
          createdAt: true,
          userId: true,
        }
      });

      console.error('\n最近5条搜索记录:');
      searchAnalytics.forEach((search, index) => {
        console.error(`${index + 1}. [${search.createdAt.toISOString()}] ${search.searchType}搜索`);
        console.error(`   数据库: ${search.database}`);
        console.error(`   搜索词: ${search.searchQuery || '无'}`);
        console.error(`   搜索字段: ${search.searchFields || '无'}`);
        console.error(`   筛选条件: ${search.filters || '无'}`);
        console.error(`   结果数量: ${search.resultsCount || '未知'}`);
        console.error(`   搜索耗时: ${search.searchTime || '未知'}ms`);
        console.error(`   用户: ${search.userId || '匿名'}`);
        console.error('');
      });
    } else {
      console.error('暂无搜索分析数据');
    }

    // 3. 统计数据
    console.error('\n=== 统计数据 ===');
    
    // 事件类型统计
    const eventTypeStats = await db.activityLog.groupBy({
      by: ['eventType'],
      _count: { eventType: true },
      orderBy: { _count: { eventType: 'desc' } }
    });

    console.error('\n事件类型统计:');
    eventTypeStats.forEach(stat => {
      console.error(`- ${stat.eventType || 'pageview'}: ${stat._count.eventType}次`);
    });

    // 数据库访问统计
    const databaseStats = await db.activityLog.groupBy({
      by: ['database'],
      where: { database: { not: null } },
      _count: { database: true },
      orderBy: { _count: { database: 'desc' } }
    });

    console.error('\n数据库访问统计:');
    databaseStats.forEach(stat => {
      console.error(`- ${stat.database}: ${stat._count.database}次`);
    });

    // 搜索类型统计（如果有SearchAnalytics数据）
    if (searchCount > 0) {
      const searchTypeStats = await db.searchAnalytics.groupBy({
        by: ['searchType'],
        _count: { searchType: true },
        orderBy: { _count: { searchType: 'desc' } }
      });

      console.error('\n搜索类型统计:');
      searchTypeStats.forEach(stat => {
        console.error(`- ${stat.searchType}: ${stat._count.searchType}次`);
      });

      // 热门搜索词
      const popularQueries = await db.searchAnalytics.groupBy({
        by: ['searchQuery'],
        where: { searchQuery: { not: null } },
        _count: { searchQuery: true },
        orderBy: { _count: { searchQuery: 'desc' } },
        take: 10
      });

      if (popularQueries.length > 0) {
        console.error('\n热门搜索词:');
        popularQueries.forEach((query, index) => {
          console.error(`${index + 1}. "${query.searchQuery}": ${query._count.searchQuery}次`);
        });
      }
    }

    // 4. 最近24小时活动
    console.error('\n=== 最近24小时活动 ===');
    const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const recentActivity = await db.activityLog.count({
      where: {
        createdAt: { gte: last24Hours }
      }
    });

    const recentSearches = searchCount > 0 ? await db.searchAnalytics.count({
      where: {
        createdAt: { gte: last24Hours }
      }
    }) : 0;

    console.error(`总活动: ${recentActivity}次`);
    console.error(`搜索活动: ${recentSearches}次`);

    // 5. 用户活动统计
    const userStats = await db.activityLog.groupBy({
      by: ['userId'],
      _count: { userId: true },
      orderBy: { _count: { userId: 'desc' } },
      take: 5
    });

    console.error('\n用户活动统计 (Top 5):');
    userStats.forEach((stat, _index) => {
      const userLabel = stat.userId || '匿名用户';
      console.error(`${_index + 1}. ${userLabel}: ${stat._count.userId}次活动`);
    });

  } catch (__error) {
    console.error('查询Analytics数据失败:', __error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  viewAnalyticsData()
    .catch(error => {
      console.error('脚本执行失败:', error);
      process.exit(1);
    })
    .finally(() => {
      process.exit(0);
    });
}

export { viewAnalyticsData };
