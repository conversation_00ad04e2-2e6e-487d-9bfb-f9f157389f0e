import { db } from '../lib/prisma';

// 定义类型接口
interface UniqueIpResult {
  ip: string;
}



interface PageviewResult {
  sessionId: string | null;
}

async function main(): Promise<void> {
  // 统计总访问量
  const totalVisits = await db.activityLog.count();

  // 统计独立IP数
  const uniqueIpCount = await db.activityLog.findMany({
    distinct: ['ip'],
    select: { ip: true },
  }) as UniqueIpResult[];

  // 统计热门数据库
  const dbStats = await db.activityLog.groupBy({
    by: ['database'],
    _count: { database: true },
    where: { database: { not: null } },
    orderBy: { _count: { database: 'desc' } },
  });

  // 统计跳出率（只看pageview事件，sessionId唯一且只出现一次视为跳出）
  const pageviews = await db.activityLog.findMany({
    where: { eventType: 'pageview', sessionId: { not: null } },
    select: { sessionId: true },
  }) as PageviewResult[];

  const sessionMap: Record<string, number> = {};
  for (const { sessionId } of pageviews) {
    if (sessionId) sessionMap[sessionId] = (sessionMap[sessionId] || 0) + 1;
  }
  const totalSessions = Object.keys(sessionMap).length;
  const bouncedSessions = Object.values(sessionMap).filter(count => count === 1).length;
  const bounceRate = totalSessions > 0 ? (bouncedSessions / totalSessions) * 100 : 0;

  // 输出报表
  console.error('--- Website Traffic Analysis Report ---');
  console.error('Total visits:', totalVisits);
  console.error('Unique IPs:', uniqueIpCount.length);
  console.error('Popular databases:');
  dbStats.forEach((stat) => {
    console.error(`  Database: ${stat.database || 'Unknown'} Visits: ${stat._count?.database}`);
  });
  console.error('Bounce rate:', bounceRate.toFixed(2) + '%');
}

main().catch((e: Error) => {
  console.error(e);
  process.exit(1);
});