#!/usr/bin/env tsx

import { getDynamicModel, validateDatabaseCode, isPrismaModel } from '../lib/dynamicTableMapping';
import { db } from '../lib/prisma';
import * as fs from 'fs';
import * as path from 'path';
import csv from 'csv-parser';
import { getTableConfig, validateTableConfig, ImportMode, TableConfig, UniqueKeyContext } from '../lib/uniqueKeyConfig';
import { SmartSyncEngineRefactored, SyncResult } from '../lib/syncEngine-refactored';

// 重构版本的数据转换函数 - 不再添加database字段
function transformDataByConfig(
  data: Record<string, unknown>, 
  tableConfig: TableConfig, 
  _context: UniqueKeyContext
): Record<string, any> {
  const transformedData: Record<string, any> = {};
  
  // 获取字段映射配置
  const fieldMapping = tableConfig.fieldMapping || {};
  
  // 遍历所有可能的字段映射
  for (const [sourceField, targetField] of Object.entries(fieldMapping)) {
    // 支持多种字段名格式：英文、中文、或其他自定义格式
    const value = data[targetField as keyof typeof data] || data[sourceField as keyof typeof data] || data[(sourceField as string).toLowerCase() as keyof typeof data] || data[(sourceField as string).toUpperCase() as keyof typeof data];
    if (value !== undefined) {
      transformedData[targetField as string] = value;
    }
  }
  
  // 重构版本：不再添加database字段，而是通过上下文获取数据源信息
  // transformedData.database = databaseType; // 移除这一行
  
  return transformedData;
}

async function importCSVData(
  csvFilePath: string, 
  tableConfig: TableConfig,
  context: UniqueKeyContext
): Promise<Record<string, any>[]> {
  const results: Record<string, any>[] = [];

  return new Promise((resolve, reject) => {
    fs.createReadStream(csvFilePath)
      .pipe(csv())
      .on('data', (data: Record<string, unknown>) => {
        // 使用重构版本的转换函数
        const transformedData = transformDataByConfig(data, tableConfig, context);
        results.push(transformedData);
      })
      .on('end', () => {
        resolve(results);
      })
      .on('error', (error: Record<string, unknown>) => {
        reject(error);
      });
  });
}

// 打印同步结果
function printSyncResult(result: SyncResult, mode: string) {
  console.error('\n📊 Sync Result Statistics:');
  console.error(`  Import mode: ${mode}`);
  console.error(`  Total records: ${result.totalRecords}`);
  console.error(`  Inserted records: ${result.inserted}`);
  console.error(`  Updated records: ${result.updated}`);
  console.error(`  Skipped records: ${result.skipped}`);
  console.error(`  Error records: ${result.errors}`);
  console.error(`  Processing time: ${result.processingTime}ms`);
  
  if (result.errorDetails.length > 0) {
    console.error('\n❌ Error Details:');
    result.errorDetails.forEach((error: Record<string, unknown>, index: number) => {
      console.error(`  ${index + 1}. Row ${error.row} - ${error.businessKey}: ${error.error}`);
    });
  }
  
  if (result.inserted > 0 || result.updated > 0) {
    console.error('\n✅ Data synchronization completed!');
  } else if (result.errors === 0) {
    console.error('\nℹ️ No new data to synchronize');
  }
}

async function main() {
  const csvFilePath = process.argv[2];
  const databaseCode = process.argv[3]; // 重构版本：使用databaseCode而不是databaseType
  const tableName = process.argv[4] || 'MedicalDevice'; // 支持指定表名，默认为MedicalDevice
  const importMode = process.argv[5] as ImportMode || 'upsert'; // 默认使用upsert模式

  if (!csvFilePath || !databaseCode) {
    console.error('使用方法: npm run import-csv-refactored <csv文件路径> <数据库代码> [表名] [导入模式]');
    console.error('参数说明:');
    console.error('  csv文件路径: CSV文件的完整路径');
    console.error('  数据库代码: 目标数据库代码标识（如 deviceCNImported, deviceUS 等）');
    console.error('  表名: 可选，默认为MedicalDevice');
    console.error('  导入模式: 可选，insert(仅插入) | upsert(智能同步) | replace(全量替换)');
    console.error('');
    console.error('示例:');
    console.error('  npm run import-csv-refactored ./data/devices.csv deviceCNImported');
    console.error('  npm run import-csv-refactored ./data/devices.csv deviceUS MedicalDevice upsert');
    console.error('  npm run import-csv-refactored ./data/companies.csv deviceCNImported Company insert');
    process.exit(1);
  }

  if (!fs.existsSync(csvFilePath)) {
    console.error(`CSV文件不存在: ${csvFilePath}`);
    process.exit(1);
  }

  // 验证数据库代码
  const validationError = await validateDatabaseCode(databaseCode);
  if (validationError) {
    console.error(`数据库代码验证失败: ${validationError.error}`);
    process.exit(1);
  }

  if (!validateTableConfig(tableName)) {
    console.error(`不支持的表: ${tableName}`);
    const supportedTables = ['MedicalDevice']; // 这里可以动态获取支持的表
    console.error(`支持的表: ${supportedTables.join(', ')}`);
    console.error('');
    console.error('如需添加新表支持，请在 src/lib/uniqueKeyConfig.ts 中添加相应配置');
    process.exit(1);
  }

  const tableConfig = getTableConfig(tableName);
  if (!tableConfig) {
    console.error(`表配置不存在: ${tableName}`);
    process.exit(1);
  }

  // 验证目标数据表是否存在
  const model = getDynamicModel(databaseCode);
  if (!isPrismaModel(model)) {
    console.error(`数据库代码 ${databaseCode} 对应的模型不存在或无效`);
    process.exit(1);
  }

  try {
    console.error(`📁 开始导入CSV文件: ${csvFilePath}`);
    console.error(`🗄️ 目标数据库: ${databaseCode}`);
    console.error(`📋 目标表: ${tableName}`);
    console.error(`🔄 导入模式: ${importMode}`);
    console.error(`📝 表描述: ${tableConfig.description || '无'}`);
    
    // 显示字段映射信息
    if (tableConfig.fieldMapping) {
      console.error(`🔗 字段映射配置:`);
      Object.entries(tableConfig.fieldMapping).forEach(([source, target]) => {
        console.error(`   ${source} → ${target}`);
      });
    }

    // 创建上下文对象 - 重构版本：从路由/参数获取数据源信息
    const context: UniqueKeyContext = {
      databaseCode: databaseCode,
      tableName: tableName,
      importSource: path.basename(csvFilePath)
    };

    // 读取CSV数据
    const records = await importCSVData(csvFilePath, tableConfig, context);
    console.error(`📊 读取到 ${records.length} 条记录`);

    if (records.length === 0) {
      console.error('❌ CSV文件中没有数据');
      process.exit(1);
    }

    // 显示数据样本
    console.error('\n📋 数据样本:');
    const sampleRecord = records[0];
    Object.entries(sampleRecord).slice(0, 5).forEach(([key, value]) => {
      console.error(`   ${key}: ${value}`);
    });
    if (Object.keys(sampleRecord).length > 5) {
      console.error(`   ... 还有 ${Object.keys(sampleRecord).length - 5} 个字段`);
    }

         // 创建智能同步引擎 - 重构版本：使用新的构造函数和上下文系统
     const syncEngine = new SmartSyncEngineRefactored(
       context,
       'CSV_IMPORT', 
       path.basename(csvFilePath)
     );
    syncEngine.setTableConfig(tableConfig);

    console.error('\n🚀 开始智能同步...');
    const result = await syncEngine.syncData(records, importMode);
    
    // 打印结果
    printSyncResult(result, importMode);

  } catch (__error) {
    console.error('❌ 导入失败:', __error);
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}

if (require.main === module) {
  main();
} 