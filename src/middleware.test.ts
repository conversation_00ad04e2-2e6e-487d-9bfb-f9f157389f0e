import { describe, it, expect, vi, beforeEach } from 'vitest';
import { NextRequest } from 'next/server';

// Define BlockedIp type for testing
interface BlockedIp {
  id: number;
  ip: string;
  reason: string;
  createdAt: Date;
  expiresAt: Date;
}

// Mock Next.js session functions
vi.mock('@/lib/session', () => ({
  updateSession: vi.fn().mockResolvedValue(undefined),
}));

// 模拟数据库模块
vi.mock('@/lib/prisma', () => {
    const mockDb = {
        blockedIp: {
            findFirst: vi.fn().mockResolvedValue(null),
            upsert: vi.fn().mockResolvedValue({}),
        },
        activityLog: {
            create: vi.fn().mockResolvedValue({}),
            count: vi.fn().mockResolvedValue(0),
        },
    };
    return { db: mockDb };
});

describe('middleware', () => {
    let db: any;
    let middleware: any;

    beforeEach(async () => {
        // 在每个测试前，重新导入最新的模拟db对象并重置
        db = (await import('@/lib/prisma')).db;
        // 动态导入 middleware 以确保模拟生效
        const middlewareModule = await import('./middleware');
        middleware = middlewareModule.middleware;
        vi.clearAllMocks();
    });

    it('should log user activity', async () => {
        const request = new NextRequest('http://localhost:3000/some-path?query=123', {
            headers: { 'x-forwarded-for': '***************' },
        });
        await middleware(request);
        expect(db.activityLog.create).toHaveBeenCalledWith(expect.objectContaining({
            data: expect.objectContaining({
                ip: '***************',
                path: '/some-path',
            })
        }));
    });

    it('should block a blocked IP address', async () => {
        const blockedIp: BlockedIp = { id: 1, ip: '***********', reason: 'Test', createdAt: new Date(), expiresAt: new Date(Date.now() + 360000) };
        db.blockedIp.findFirst.mockResolvedValue(blockedIp);
        const request = new NextRequest('http://localhost:3000/', {
            headers: { 'x-forwarded-for': '***********' },
        });
        const response = await middleware(request);
        expect(response.status).toBe(429);
    });

    it('should trigger rate limit and block IP', async () => {
        const userIp = '********';
        // Mock blocked IP check to return null (not blocked)
        db.blockedIp.findFirst.mockResolvedValue(null);
        // Mock activity log count to return high number (exceeding rate limit)
        db.activityLog.count.mockResolvedValue(50);

        const request = new NextRequest('http://localhost:3000/page', {
            headers: { 'x-forwarded-for': userIp },
        });
        await middleware(request);

        // Should create activity log
        expect(db.activityLog.create).toHaveBeenCalled();
        // Should upsert blocked IP due to rate limit
        expect(db.blockedIp.upsert).toHaveBeenCalledWith(expect.objectContaining({
            where: { ip: userIp },
            create: expect.objectContaining({
                ip: userIp,
                reason: 'Rate limit exceeded',
            }),
        }));
    });
}); 