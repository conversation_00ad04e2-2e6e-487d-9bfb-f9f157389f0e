"use client";

import { useState, useEffect, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { Filter, Plus, X, Search, ArrowDown, Info } from "lucide-react";
import { convertFiltersToConditions, mergeFilterAndAdvancedConditions, isActiveFilter } from '@/lib/filterToConditionConverter';
import { Alert, AlertDescription } from '@/components/ui/alert';

export interface SearchCondition {
  id: string;
  field: string;
  operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'greaterThan' | 'lessThan' | 'between' | 'in';
  value: string | string[] | number | number[] | Date | Date[] | { from?: string; to?: string };
  logic?: 'AND' | 'OR';
}

interface AdvancedSearchProps {
  onSearch: (conditions: SearchCondition[]) => void;
  onClear: () => void;
  availableFields: Array<{
    fieldName: string;
    displayName: string;
    fieldType: string;
    filterType?: string;
    searchType?: string;
  }>;
  currentConditions?: SearchCondition[];
  metadata?: Record<string, string[]>;
  // New props for filter integration
  currentFilters?: Record<string, unknown>;
  showFilterIntegration?: boolean;
}

const OPERATORS = {
  text: [
    { value: 'contains', label: 'Contains' },
    { value: 'equals', label: 'Equals' },
    { value: 'startsWith', label: 'Starts with' },
    { value: 'endsWith', label: 'Ends with' },
    { value: 'notContains', label: 'Does not contain' },
  ],
  date: [
    { value: 'equals', label: 'Equals' },
    { value: 'before', label: 'Before' },
    { value: 'after', label: 'After' },
    { value: 'between', label: 'Between' },
  ],
  select: [
    { value: 'equals', label: 'Equals' },
    { value: 'notEquals', label: 'Not equals' },
  ],
  boolean: [
    { value: 'equals', label: 'Is' },
  ],
};

const LOGIC_OPERATORS = [
  { value: 'AND', label: 'AND' },
  { value: 'OR', label: 'OR' },
  { value: 'NOT', label: 'NOT' },
];

export default function AdvancedSearch({
  onSearch,
  onClear,
  availableFields,
  currentConditions = [],
  metadata = {},
  currentFilters = {},
  showFilterIntegration = true
}: AdvancedSearchProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [conditions, setConditions] = useState<SearchCondition[]>(currentConditions);
  const [_showFilterPopulation, _setShowFilterPopulation] = useState(false);
  const [isDatePickerActive, setIsDatePickerActive] = useState(false);
  const [preventClose, setPreventClose] = useState(false);
  const [datePickerInteractionCount, setDatePickerInteractionCount] = useState(0);
  const [forcePreventClose, _setForcePreventClose] = useState(false);

  // Check if there are active filters that could be populated
  const hasActiveFilters = useMemo(() => {
    return Object.values(currentFilters).some(value => isActiveFilter(value));
  }, [currentFilters]);

  // 实现我们自己的点击外部检测逻辑
  useEffect(() => {
    if (!isOpen) return;

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;

      console.log('[AdvancedSearch] Custom click outside handler triggered', {
        target: target.tagName,
        type: target.getAttribute?.('type'),
        className: target.className,
        preventClose,
        isDatePickerActive,
        datePickerInteractionCount,
        forcePreventClose
      });

      // 如果有任何保护状态激活，不关闭模态框，同时阻断事件冒泡，防止击穿到底层页面
      if (preventClose || isDatePickerActive || datePickerInteractionCount > 0 || forcePreventClose) {
        console.log('[AdvancedSearch] Not closing due to protection states', {
          preventClose,
          isDatePickerActive,
          datePickerInteractionCount,
          forcePreventClose
        });
        event.stopPropagation();
        event.preventDefault();
        return;
      }

      // 检查是否点击了模态框内容区域
      const dialogContent = document.querySelector('[data-radix-dialog-content]');
      if (dialogContent && dialogContent.contains(target)) {
        console.log('[AdvancedSearch] Click inside dialog content, not closing');
        return;
      }

      // 检查是否点击了日期选择器面板
      if (target.closest('[data-date-picker-panel]')) {
        console.log('[AdvancedSearch] Click on date picker panel, not closing');
        return;
      }

      // 检查是否点击了 Radix Select 下拉内容
      if (target.closest('[data-advanced-select-content]')) {
        console.log('[AdvancedSearch] Click on select content, not closing');
        return;
      }

      // 检查是否点击了日期输入或相关元素
      if (target.tagName === 'INPUT' && target.getAttribute('type') === 'date') {
        console.log('[AdvancedSearch] Click on date input, not closing');
        return;
      }

      // 检查是否点击了日历图标
      if (target.closest('.lucide-calendar') || target.closest('svg[class*="lucide"]')) {
        console.log('[AdvancedSearch] Click on calendar icon, not closing');
        return;
      }

      // 检查是否是浏览器原生日期选择器的一部分
      if (target.closest('[role="dialog"]:not([data-radix-dialog-content])') ||
          target.closest('[role="listbox"]') ||
          target.closest('[role="grid"]') ||
          target.closest('[role="gridcell"]') ||
          target.matches('[class*="date"]') ||
          target.matches('[class*="calendar"]') ||
          target.matches('[class*="picker"]') ||
          target.matches('[aria-label*="date"]') ||
          target.matches('[aria-label*="calendar"]')) {
        console.log('[AdvancedSearch] Click on native date picker element, not closing');
        return;
      }

      // 如果到达这里，说明是真正的外部点击，关闭模态框
      console.log('[AdvancedSearch] Legitimate outside click, closing modal');
      setIsOpen(false);
    };

    // 延迟添加事件监听器，避免立即触发
    const timer = setTimeout(() => {
      document.addEventListener('mousedown', handleClickOutside, true);
      document.addEventListener('click', handleClickOutside, true);
    }, 100);

    return () => {
      clearTimeout(timer);
      document.removeEventListener('mousedown', handleClickOutside, true);
      document.removeEventListener('click', handleClickOutside, true);
    };
  }, [isOpen, preventClose, isDatePickerActive, datePickerInteractionCount, forcePreventClose]);

  // Convert current filters to conditions for potential population
  const filterConditions = useMemo(() => {
    if (!showFilterIntegration || !hasActiveFilters) return [];
    return convertFiltersToConditions(currentFilters, availableFields as any);
  }, [currentFilters, availableFields, showFilterIntegration, hasActiveFilters]);

  // Update conditions when currentConditions prop changes
  useEffect(() => {
    setConditions(currentConditions);
  }, [currentConditions]);

  // Function to populate conditions from current filters
  const populateFromFilters = () => {
    const mergedConditions = mergeFilterAndAdvancedConditions(filterConditions, conditions);
    setConditions(mergedConditions);
    _setShowFilterPopulation(false);
  };

  const addCondition = () => {
    const newCondition: SearchCondition = {
      id: Date.now().toString(),
      field: availableFields[0]?.fieldName || '',
      operator: 'contains',
      value: '',
      logic: conditions.length > 0 ? 'AND' : undefined,
    };
    setConditions([...conditions, newCondition]);
  };

  const removeCondition = (id: string) => {
    const newConditions = conditions.filter(c => c.id !== id);
    // Remove logic from first condition if it exists
    if (newConditions.length > 0 && newConditions[0].logic) {
      newConditions[0] = { ...newConditions[0], logic: undefined };
    }
    setConditions(newConditions);
  };

  const updateCondition = (id: string, updates: Partial<SearchCondition>) => {
    setConditions(conditions.map(c => {
      if (c.id === id) {
        const updatedCondition = { ...c, ...updates };

        // 如果更新了字段，检查操作符是否与字段类型兼容
        if (updates.field) {
          const field = availableFields.find(f => f.fieldName === updates.field);
          if (field && isDateField(field)) {
            const dateOperators = OPERATORS.date.map(op => op.value);
            if (!dateOperators.includes(updatedCondition.operator)) {
              // 如果当前操作符不适用于日期字段，默认设置为 'between' for date_range fields
              updatedCondition.operator = field.searchType === 'date_range' ? 'between' : 'equals';
              updatedCondition.value = field.searchType === 'date_range' ? { from: '', to: '' } : '';
            }
          }
        }

        // 如果更新了操作符，确保值的格式正确
        if (updates.operator) {
          const field = availableFields.find(f => f.fieldName === updatedCondition.field);
          if (field && isDateField(field)) {
            if (updates.operator === 'between') {
              // 确保 between 操作符使用正确的值格式
              if (typeof updatedCondition.value !== 'object') {
                updatedCondition.value = { from: '', to: '' };
              }
            } else {
              // 其他日期操作符使用字符串值
              if (typeof updatedCondition.value === 'object') {
                updatedCondition.value = '';
              }
            }
          }
        }

        return updatedCondition;
      }
      return c;
    }));
  };

  const handleSearch = () => {
    const validConditions = conditions.filter(c => {
      if (typeof c.value === 'string') {
        return c.value.trim() !== '';
      }
      if (Array.isArray(c.value)) {
        return c.value.length > 0;
      }
      if (typeof c.value === 'object' && c.value !== null) {
        const obj = c.value as { from?: string; to?: string };
        return obj.from || obj.to;
      }
      if (typeof c.value === 'number') {
        return true;
      }
      return false;
    });

    console.log('[AdvancedSearch] 执行搜索:', {
      totalConditions: conditions.length,
      validConditions: validConditions.length,
      conditions: validConditions
    });

    onSearch(validConditions);
    setIsOpen(false);
  };

  const handleClear = () => {
    setConditions([]);
    onClear();
    setIsOpen(false);
  };

  const getOperators = (field: Record<string, unknown>) => {
    // 优先使用 searchType，然后是 fieldType
    const type = field.searchType || field.fieldType;

    switch (type) {
      case 'date_range':
      case 'date':
        return OPERATORS.date;
      case 'boolean':
        return OPERATORS.boolean;
      case 'exact':
      case 'select':
        return OPERATORS.select;
      case 'contains':
      case 'text':
      default:
        return OPERATORS.text;
    }
  };

  const isDateField = (field: Record<string, unknown>) => {
    const type = field.searchType || field.fieldType;
    return type === 'date' || type === 'date_range' || field.fieldType === 'date';
  };

  const renderValueInput = (condition: SearchCondition, field: Record<string, unknown>) => {
    // 处理日期字段
    if (isDateField(field)) {
      // 对于 between 操作符，使用日期范围选择器
      if (condition.operator === 'between') {
        const dateValue = typeof condition.value === "object" ? condition.value : { from: '', to: '' };
        return (
          <DateRangePicker
            startDate={dateValue.from || ''}
            endDate={dateValue.to || ''}
            onStartDateChange={(date) => updateCondition(condition.id, {
              value: { ...dateValue, from: date }
            })}
            onEndDateChange={(date) => updateCondition(condition.id, {
              value: { ...dateValue, to: date }
            })}
            onInteractionStart={() => setPreventClose(true)}
            onInteractionEnd={() => setPreventClose(false)}
            onInteractionCountChange={(count) => setDatePickerInteractionCount(count)}
            placeholder="Select date range" className="w-full" />
        );
      }

      // 对于其他日期操作符（equals, before, after），使用单个日期输入
      return (
        <Input
          type="date"
          value={typeof condition.value === "string" ? condition.value : ''}
          onChange={(e) => updateCondition(condition.id, { value: e.target.value })}
          onClick={(e) => {
            e.stopPropagation();
            setIsDatePickerActive(true);
          }}
          onFocus={(e) => {
            e.stopPropagation();
            setIsDatePickerActive(true);
          }}
          onBlur={(e) => {
            e.stopPropagation();
            // 延迟重置状态，给原生日期选择器时间完成操作
            setTimeout(() => setIsDatePickerActive(false), 300);
          }}
          onMouseDown={(e) => e.stopPropagation()}
          className="w-full" />
      );
    }

    if (field.fieldType === 'boolean') {
      return (
        <Select
          value={typeof condition.value ==="string" ? condition.value : '__none__'}
          onValueChange={(value) => updateCondition(condition.id, { value: value ==="__none__" ? '' : value })}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select value" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="__none__">Select value</SelectItem>
            <SelectItem value="true">True</SelectItem>
            <SelectItem value="false">False</SelectItem>
          </SelectContent>
        </Select>
      );
    }

    if (field.fieldType ==="select" || field.filterType === 'select') {
      const options = (metadata as any)[field.fieldName as string] || [];
      const validOptions = options.filter((option: unknown) =>
        typeof option === 'string' && option.trim() !== ''
      );

      return (
        <Select
          value={typeof condition.value ==="string" ? condition.value : '__all__'}
          onValueChange={(value) => updateCondition(condition.id, { value: value ==="__all__" ? '' : value })}
        >
          <SelectTrigger className="w-full">
            <SelectValue placeholder={`Select ${field.displayName}`} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="__all__">All</SelectItem>
            {validOptions.map((option: unknown) => (
              <SelectItem key={String(option)} value={String(option)}>
                {String(option)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      );
    }

    return (
      <Input
        placeholder="Enter value" value={typeof condition.value ==="string" ? condition.value : ''}
        onChange={(e) => updateCondition(condition.id, { value: e.target.value })}
        className="w-full" />
    );
  };

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        // 在日期交互期间，阻止对话框被关闭（包括 Radix 内部触发的关闭）
        if (!open) {
          if (preventClose || isDatePickerActive || datePickerInteractionCount > 0 || forcePreventClose) {
            console.log('[AdvancedSearch] Prevent close due to active date interaction', {
              preventClose,
              isDatePickerActive,
              datePickerInteractionCount,
              forcePreventClose
            });
            return;
          }
        }
        setIsOpen(open);
      }}
    >
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="h-8 text-xs">
          <Filter className="mr-1 h-3 w-3" />
          Advanced Search
          {currentConditions.length > 0 && (
            <Badge variant="secondary" className="ml-2 h-4 text-xs">
              {currentConditions.length}
            </Badge>
          )}
        </Button>
      </DialogTrigger>
      <DialogContent
        className="max-w-4xl max-h-[80vh] overflow-y-auto"
        onInteractOutside={(e) => {
          // 完全阻止默认的 onInteractOutside 行为
          // 我们将实现自己的点击外部检测
          e.preventDefault();
        }}
        onClickCapture={(e) => {
          // 日期交互期间，屏蔽除日期面板之外的所有点击，避免误触触发底部按钮
          if (preventClose || isDatePickerActive || datePickerInteractionCount > 0 || forcePreventClose) {
            const target = e.target as HTMLElement;
            if (!target.closest('[data-date-picker-panel]')) {
              console.log('[AdvancedSearch] Blocking click during active date interaction');
              e.stopPropagation();
              e.preventDefault();
            }
          }
        }}
        onEscapeKeyDown={(e) => {
          // 在日期交互期间，阻止 ESC 关闭
          if (preventClose || isDatePickerActive || datePickerInteractionCount > 0 || forcePreventClose) {
            console.log('[AdvancedSearch] Prevent close via ESC due to active date interaction', {
              preventClose,
              isDatePickerActive,
              datePickerInteractionCount,
              forcePreventClose
            });
            e.preventDefault();
          }
        }}
      >
        <DialogHeader>
          <DialogTitle>Advanced Search</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Filter Population Alert */}
          {showFilterIntegration && hasActiveFilters && filterConditions.length > 0 && (
            <Alert className="border-blue-200 bg-blue-50">
              <Info className="h-4 w-4 text-blue-600" />
              <AlertDescription className="text-blue-800">
                <div className="flex items-center justify-between">
                  <div>
                    <strong>Current filters detected:</strong> {filterConditions.length} filter(s) can be added to advanced search.
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={populateFromFilters}
                    className="ml-2 border-blue-300 text-blue-700 hover:bg-blue-100"
                  >
                    <ArrowDown className="h-3 w-3 mr-1" />
                    Add Filters
                  </Button>
                </div>
              </AlertDescription>
            </Alert>
          )}

          {conditions.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Search className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No search conditions added yet.</p>
              <p className="text-sm">Click &quot;Add Condition&quot; to start building your search.</p>
              {hasActiveFilters && (
                <p className="text-sm text-blue-600 mt-2">
                  Or use the &quot;Add Filters&quot; button above to populate from current filters.
                </p>
              )}
            </div>
          ) : (
            <div className="space-y-3">
              {conditions.map((condition, _index) => {
                const field = availableFields.find(f => f.fieldName === condition.field);
                if (!field) return null;

                return (
                  <div key={condition.id} className="border rounded-lg p-4 bg-gray-50">
                    <div className="flex items-center gap-3 mb-3">
                      {_index > 0 && (
                        <Select
                          value={condition.logic || 'AND'}
                          onValueChange={(value) => updateCondition(condition.id, {
                            logic: value as 'AND' | 'OR' | 'NOT'
                          })}
                        >
                          <SelectTrigger className="w-20">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {LOGIC_OPERATORS.map(op => (
                              <SelectItem key={op.value} value={op.value}>
                                {op.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                      <Button
                        variant="ghost" size="sm" onClick={() => removeCondition(condition.id)}
                        className="ml-auto text-red-600 hover:text-red-800 hover:bg-red-50">
                        <X className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                      {/* Field Selection */}
                      <div>
                        <label className="block text-sm font-medium mb-1">Field</label>
                        <Select
                          value={condition.field}
                          onValueChange={(value) => updateCondition(condition.id, {
                            field: value,
                            operator: getOperators(availableFields.find(f => f.fieldName === value) || {})[0].value
                          })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {availableFields.map(field => (
                              <SelectItem key={field.fieldName} value={field.fieldName}>
                                {field.displayName}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Operator Selection */}
                      <div>
                        <label className="block text-sm font-medium mb-1">Operator</label>
                        <Select
                          value={condition.operator}
                          onValueChange={(value) => updateCondition(condition.id, { operator: value })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {getOperators(field).map(op => (
                              <SelectItem key={op.value} value={op.value}>
                                {op.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Value Input */}
                      <div>
                        <label className="block text-sm font-medium mb-1">Value</label>
                        {renderValueInput(condition, field)}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}

          <div className="flex justify-between items-center pt-4 border-t">
            <Button
              variant="outline" onClick={addCondition}
              className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add Condition
            </Button>

            <div className="flex gap-2">
              <Button
                variant="ghost"
                onClick={handleClear}
                disabled={preventClose || isDatePickerActive || datePickerInteractionCount > 0 || forcePreventClose}
              >
                Clear All
              </Button>
              <Button
                onClick={handleSearch}
                disabled={conditions.length === 0 || preventClose || isDatePickerActive || datePickerInteractionCount > 0 || forcePreventClose}
              >
                <Search className="mr-2 h-4 w-4" />
                Search
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
