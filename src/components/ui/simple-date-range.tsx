"use client";

import { Calendar } from "lucide-react";
import { useRef } from "react";

interface SimpleDateRangeProps {
  startDate?: string;
  endDate?: string;
  onStartDateChange: (date: string) => void;
  onEndDateChange: (date: string) => void;
  placeholder?: string;
  className?: string;
}

export function SimpleDateRange({
  startDate = "",
  endDate = "",
  onStartDateChange,
  onEndDateChange,
  placeholder = "Select date range",
  className = ""
}: SimpleDateRangeProps) {
  const startDateRef = useRef<HTMLInputElement>(null);
  const endDateRef = useRef<HTMLInputElement>(null);

  const formatDisplayDate = (dateStr: string, fieldType: 'start' | 'end') => {
    if (!dateStr) {
      return fieldType === 'start' ? "Start Date" : "End Date";
    }
    try {
      const date = new Date(dateStr);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return dateStr;
    }
  };

  const handleStartDateClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (startDateRef.current) {
      startDateRef.current.showPicker?.();
    }
  };

  const handleEndDateClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (endDateRef.current) {
      endDateRef.current.showPicker?.();
    }
  };

  return (
    <div className={className}>
      {/* Single row layout with integrated labels */}
      <div className="flex gap-3">
        {/* Start Date */}
        <div className="flex-1">
          <div className="relative">
            {/* Hidden native date input */}
            <input
              ref={startDateRef}
              type="date"
              value={startDate}
              onChange={(e) => {
                e.stopPropagation();
                onStartDateChange(e.target.value);
              }}
              onFocus={(e) => e.stopPropagation()}
              onBlur={(e) => e.stopPropagation()}
              className="absolute inset-0 w-full h-full opacity-0 pointer-events-none"
              tabIndex={-1}
            />
            {/* Visible button with integrated label */}
            <button
              type="button"
              onClick={handleStartDateClick}
              onMouseDown={(e) => e.stopPropagation()}
              className="flex items-center justify-start w-full h-9 px-3 py-2 text-sm font-normal text-left border border-input rounded-md bg-transparent shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-1 focus:ring-ring focus:border-ring transition-colors"
            >
              <Calendar className="mr-2 h-3 w-3 text-gray-400" />
              <span className={startDate ? "text-gray-700" : "text-gray-500"}>
                {formatDisplayDate(startDate, 'start')}
              </span>
            </button>
          </div>
        </div>

        {/* End Date */}
        <div className="flex-1">
          <div className="relative">
            {/* Hidden native date input */}
            <input
              ref={endDateRef}
              type="date"
              value={endDate}
              onChange={(e) => {
                e.stopPropagation();
                onEndDateChange(e.target.value);
              }}
              onFocus={(e) => e.stopPropagation()}
              onBlur={(e) => e.stopPropagation()}
              className="absolute inset-0 w-full h-full opacity-0 pointer-events-none"
              tabIndex={-1}
            />
            {/* Visible button with integrated label */}
            <button
              type="button"
              onClick={handleEndDateClick}
              onMouseDown={(e) => e.stopPropagation()}
              className="flex items-center justify-start w-full h-9 px-3 py-2 text-sm font-normal text-left border border-input rounded-md bg-transparent shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-1 focus:ring-ring focus:border-ring transition-colors"
            >
              <Calendar className="mr-2 h-3 w-3 text-gray-400" />
              <span className={endDate ? "text-gray-700" : "text-gray-500"}>
                {formatDisplayDate(endDate, 'end')}
              </span>
            </button>
          </div>
        </div>
      </div>

      {/* Display selected range */}
      {startDate && endDate && (
        <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded mt-2">
          Selected: {formatDisplayDate(startDate, 'start')} - {formatDisplayDate(endDate, 'end')}
        </div>
      )}
    </div>
  );
}
