import * as React from "react"
import { X } from "lucide-react"

import { cn } from "@/lib/utils"

export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  showClearButton?: boolean
  onClear?: () => void
}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, showClearButton = true, onClear, value, onChange, ...props }, ref) => {
    const hasValue = value !== undefined && value !== null && String(value).length > 0
    const shouldShowClearButton = showClearButton && hasValue && !props.disabled && !props.readOnly

    const handleClear = () => {
      if (onClear) {
        onClear()
      } else if (onChange) {
        // Create a synthetic event for the onChange handler
        const syntheticEvent = {
          target: { value: '' },
          currentTarget: { value: '' }
        } as React.ChangeEvent<HTMLTextAreaElement>
        onChange(syntheticEvent)
      }
    }

    return (
      <div className="relative">
        <textarea
          className={cn(
            "flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
            shouldShowClearButton && "pr-8",
            className
          )}
          ref={ref}
          value={value}
          onChange={onChange}
          {...props}
        />
        {shouldShowClearButton && (
          <button
            type="button"
            onClick={handleClear}
            className="absolute right-2 top-2 h-4 w-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none"
            tabIndex={-1}
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Clear textarea</span>
          </button>
        )}
      </div>
    )
  }
)
Textarea.displayName = "Textarea"
export { Textarea }
