"use client";

import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Search, Database, ExternalLink, X } from 'lucide-react';
import Link from 'next/link';

// 搜索结果接口
interface SearchResult {
  database: string;
  count: number;
}

// 数据库配置接口
interface DatabaseConfig {
  name: string;
  category: string;
  description: string;
  accessLevel: string;
}

export default function MedicalSearchExample() {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [databaseConfigs, setDatabaseConfigs] = useState<Record<string, DatabaseConfig>>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取数据库配置
  useEffect(() => {
    const fetchConfigs = async () => {
      try {
        const response = await fetch('/api/config/databases');
        const data = await response.json();
        if (data.success) {
          setDatabaseConfigs(data.data);
        }
      } catch (err) {
        console.error('Failed to fetch database configs:', err);
      }
    };

    fetchConfigs();
  }, []);

  // 执行搜索
  const performSearch = async () => {
    if (!searchQuery.trim()) {
      setSearchResults([]);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/unified-global-search?q=${encodeURIComponent(searchQuery)}`);
      const data = await response.json();

      if (data.success) {
        setSearchResults(data.data);
      } else {
        setError(data.error || 'Search failed');
      }
    } catch (err) {
      console.error('Search error:', err);
      setError('Network error occurred');
    } finally {
      setLoading(false);
    }
  };

  // 处理键盘事件
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      performSearch();
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Medical Device Database Search
          </CardTitle>
          <p className="text-gray-600">
            Search across medical device databases using Elasticsearch multi_match query
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 搜索框 */}
          <div className="flex items-center space-x-2">
            <div className="relative flex-1">
              <Input
                type="text"
                placeholder="Enter product name, company name, application number, registration number..."
                className="pr-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={handleKeyPress}
                showClearButton={false}
              />
              {searchQuery && (
                <button
                  type="button"
                  onClick={() => setSearchQuery("")}
                  className="absolute right-8 top-1/2 transform -translate-y-1/2 h-4 w-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none"
                  tabIndex={-1}
                >
                  <X className="h-4 w-4" />
                  <span className="sr-only">Clear search</span>
                </button>
              )}
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            </div>
            <Button 
              onClick={performSearch}
              disabled={!searchQuery.trim() || loading}
            >
              {loading ? 'Searching...' : 'Search'}
            </Button>
          </div>

          {/* 错误提示 */}
          {error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-600">{error}</p>
            </div>
          )}

          {/* 搜索结果 */}
          {searchResults.length > 0 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">
                Search Results for "{searchQuery}"
              </h3>
              
              <div className="grid gap-4 md:grid-cols-2">
                {searchResults.map((result) => {
                  const config = databaseConfigs[result.database];
                  const databaseName = config?.name || result.database;
                  
                  return (
                    <Card key={result.database} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <h4 className="font-medium text-gray-900">
                              {databaseName}
                            </h4>
                            {config?.description && (
                              <p className="text-sm text-gray-600 mt-1">
                                {config.description}
                              </p>
                            )}
                            <div className="flex items-center gap-2 mt-2">
                              <span className="text-sm text-gray-500">
                                Category: {config?.category || 'Unknown'}
                              </span>
                              <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded">
                                {config?.accessLevel || 'free'}
                              </span>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-2xl font-bold text-blue-600">
                              {result.count.toLocaleString()}
                            </div>
                            <div className="text-sm text-gray-500">results</div>
                          </div>
                        </div>
                        
                        {/* 跳转链接 */}
                        {result.count > 0 && (
                          <div className="mt-3 pt-3 border-t border-gray-100">
                            <Link
                              href={`/data/list/${result.database}?allFields=${encodeURIComponent(searchQuery)}`}
                              className="inline-flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800 hover:underline"
                            >
                              View detailed results
                              <ExternalLink className="h-3 w-3" />
                            </Link>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </div>
          )}

          {/* 无结果提示 */}
          {searchResults.length === 0 && searchQuery && !loading && (
            <div className="text-center py-8">
              <p className="text-gray-500">No results found for "{searchQuery}"</p>
              <p className="text-sm text-gray-400 mt-1">
                Try different keywords or check your spelling
              </p>
            </div>
          )}

          {/* 技术说明 */}
          <div className="mt-8 p-4 bg-gray-50 rounded-md">
            <h4 className="font-medium text-gray-900 mb-2">Technical Implementation</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Uses Elasticsearch <code>medical_index</code> with multi_match query</li>
              <li>• Searches across: <code>registration_no.*</code>, <code>product_combined.*</code>, <code>company_combined.*</code></li>
              <li>• Supports Chinese, English, and fuzzy matching</li>
              <li>• Aggregates results by <code>table_code</code> field</li>
              <li>• Falls back to Prisma search if Elasticsearch fails</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
