/**
 * 权限调试工具
 * 用于诊断前端权限检查失败的原因
 */

// 简化版权限检查，避免复杂的依赖
export function debugPermissionCheck(database: string, userMembershipType?: string | null) {
  console.error('🔍 [Permission Debug] Starting permission check...', {
    database,
    userMembershipType,
    timestamp: new Date().toISOString(),
  });

  // 注意：这里应该从数据库配置中动态读取，而不是硬编码
  // TODO: 重构为从 DatabaseConfig 表读取配置
  console.warn('⚠️ [Permission Debug] Using hardcoded database list - should be dynamic');

  // 临时硬编码列表（应该被移除）
  const freeAccessDatabases = ['us_class', 'us_pmn', 'freepat', 'devicecnimported'];

  // 检查是否为免费数据库
  const isFreeDatabase = freeAccessDatabases.includes(database.toLowerCase());
  
  console.error('📋 [Permission Debug] Database analysis:', {
    database: database.toLowerCase(),
    isFreeDatabase,
    freeAccessDatabases,
  });

  // us_class 应该是免费访问的
  if (database.toLowerCase() === 'us_class') {
    console.error('✅ [Permission Debug] us_class is a free database, access should be allowed');
    return true;
  }

  // 其他免费数据库
  if (isFreeDatabase) {
    console.error('✅ [Permission Debug] This is a free database, access allowed');
    return true;
  }

  // 需要登录的数据库
  if (!userMembershipType) {
    console.error('❌ [Permission Debug] User not logged in, cannot access paid databases');
    return false;
  }

  // 高级用户权限检查
  if (userMembershipType === 'premium' || userMembershipType === 'enterprise') {
    console.error('✅ [Permission Debug] User is premium/enterprise, access allowed');
    return true;
  }

  console.error('❌ [Permission Debug] Insufficient user permissions');
  return false;
}

// 模拟获取数据库配置的简化版本
export async function debugGetDatabaseConfig(database: string) {
  console.error('🔍 [Config Debug] Getting database configuration...', { database });
  
  try {
    const response = await fetch(`/api/config/databases`);
    const result = await response.json();
    
    console.error('📡 [Config Debug] API response:', {
      success: result.success,
      hasData: !!result.data,
      databases: Object.keys(result.data || {}),
    });
    
    if (result.success && result.data) {
      const config = result.data[database];
      console.error('📋 [Config Debug] Database configuration:', {
        database,
        found: !!config,
        config
      });
      return config;
    }
    
    console.error('❌ [Config Debug] API returned failure');
    return null;
  } catch (__error) {
    console.error('💥 [Config Debug] API call failed:', __error);
    return null;
  }
}

// 简化版的API调用，用于测试
export async function debugSearchData(database: string) {
  console.error('🔍 [API Debug] Calling data API...', { database });
  
  try {
    const url = `/api/data/${database}?page=1&limit=5`;
    console.error('📡 [API Debug] Request URL:', url);
    
    const response = await fetch(url);
    
    console.error('📡 [API Debug] Response status:', {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok,
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ [API Debug] Request failed:', errorText);
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }
    
    const result = await response.json();
    console.error('✅ [API Debug] Request successful:', {
      success: result.success,
      dataLength: result.data?.length,
      totalCount: result.pagination?.totalCount,
    });
    
    return result;
  } catch (__error) {
    console.error('💥 [API Debug] API call failed:', __error);
    throw __error;
  }
}

// 全面的权限和数据访问测试
export async function fullPermissionDebug(database: string, userMembershipType?: string | null) {
  console.error('🚀 [全面调试] 开始完整的权限和数据访问测试...', {
    database,
    userMembershipType,
  });

  // 1. 权限检查
  const hasPermission = debugPermissionCheck(database, userMembershipType);
  console.error('1️⃣ [Full Debug] Permission check result:', hasPermission);

  // 2. 配置获取
  const config = await debugGetDatabaseConfig(database);
  console.error('2️⃣ [Full Debug] Configuration retrieval result:', !!config);

  // 3. 数据API测试
  try {
    const data = await debugSearchData(database);
    console.error('3️⃣ [Full Debug] Data API test result: Success');
    return {
      hasPermission,
      hasConfig: !!config,
      hasData: true,
      config,
      dataCount: data.pagination?.totalCount,
    };
  } catch (__error) {
    console.error('3️⃣ [Full Debug] Data API test result: Failed', __error);
    return {
      hasPermission,
      hasConfig: !!config,
      hasData: false,
      config,
      error: __error instanceof Error ? __error.message : String(__error),
    };
  }
} 