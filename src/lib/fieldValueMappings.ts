/**
 * Field Value Mappings
 * 
 * This module provides mappings between user-friendly display values
 * and actual database values for specific fields.
 */

export interface FieldValueMapping {
  displayValue: string;
  databaseValue: string;
  description?: string;
}

export interface DatabaseFieldMappings {
  [fieldName: string]: FieldValueMapping[];
}

export interface DatabaseMappings {
  [databaseCode: string]: DatabaseFieldMappings;
}

/**
 * Mappings for US PMN database fields
 */
const US_PMN_MAPPINGS: DatabaseFieldMappings = {
  reviewadvisecomm: [
    { displayValue: "Anesthesiology", databaseValue: "AN", description: "Anesthesiology Advisory Committee" },
    { displayValue: "Cardiovascular", databaseValue: "CV", description: "Cardiovascular Advisory Committee" },
    { displayValue: "Chemistry", databaseValue: "CH", description: "Chemistry Advisory Committee" },
    { displayValue: "Dental", databaseValue: "DE", description: "Dental Advisory Committee" },
    { displayValue: "Ear, Nose, and Throat", databaseValue: "EN", description: "Ear, Nose, and Throat Advisory Committee" },
    { displayValue: "Gastroenterology", databaseValue: "GU", description: "Gastroenterology Advisory Committee" },
    { displayValue: "Hematology", databaseValue: "HE", description: "Hematology Advisory Committee" },
    { displayValue: "Hospital", databaseValue: "HO", description: "Hospital Advisory Committee" },
    { displayValue: "Immunology", databaseValue: "IM", description: "Immunology Advisory Committee" },
    { displayValue: "Microbiology", databaseValue: "MI", description: "Microbiology Advisory Committee" },
    { displayValue: "Neurology", databaseValue: "NE", description: "Neurology Advisory Committee" },
    { displayValue: "Obstetrics and Gynecology", databaseValue: "OB", description: "Obstetrics and Gynecology Advisory Committee" },
    { displayValue: "Ophthalmic", databaseValue: "OP", description: "Ophthalmic Advisory Committee" },
    { displayValue: "Orthopedic", databaseValue: "OR", description: "Orthopedic Advisory Committee" },
    { displayValue: "Pathology", databaseValue: "PA", description: "Pathology Advisory Committee" },
    { displayValue: "Physical Medicine", databaseValue: "PM", description: "Physical Medicine Advisory Committee" },
    { displayValue: "Radiology", databaseValue: "RA", description: "Radiology Advisory Committee" },
    { displayValue: "Surgery", databaseValue: "SU", description: "Surgery Advisory Committee" },
    { displayValue: "Toxicology", databaseValue: "TX", description: "Toxicology Advisory Committee" },
    { displayValue: "Molecular Genetics", databaseValue: "MG", description: "Molecular Genetics Advisory Committee" },
    
    // Add common search terms that users might use
    { displayValue: "Review Advisory Committee", databaseValue: "SU", description: "Most common - Surgery Advisory Committee" },
    { displayValue: "Advisory Committee", databaseValue: "SU", description: "Default to Surgery Advisory Committee" },
    { displayValue: "Surgery Advisory Committee", databaseValue: "SU", description: "Surgery Advisory Committee" },
    { displayValue: "Cardiovascular Advisory Committee", databaseValue: "CV", description: "Cardiovascular Advisory Committee" },
    { displayValue: "Hospital Advisory Committee", databaseValue: "HO", description: "Hospital Advisory Committee" },
    { displayValue: "Orthopedic Advisory Committee", databaseValue: "OR", description: "Orthopedic Advisory Committee" },
    { displayValue: "Radiology Advisory Committee", databaseValue: "RA", description: "Radiology Advisory Committee" },
  ],
  
  classadvisecomm: [
    // Similar mappings for classification advisory committee if needed
    { displayValue: "Classification Advisory Committee", databaseValue: "SU", description: "Default classification committee" },
  ]
};

/**
 * All database mappings
 */
export const FIELD_VALUE_MAPPINGS: DatabaseMappings = {
  us_pmn: US_PMN_MAPPINGS,
  // Add other databases as needed
};

/**
 * Get mapped database value for a field
 * @param databaseCode Database code (e.g., 'us_pmn')
 * @param fieldName Field name (e.g., 'reviewadvisecomm')
 * @param displayValue User input value
 * @returns Mapped database value or original value if no mapping found
 */
export function getMappedValue(
  databaseCode: string, 
  fieldName: string, 
  displayValue: string
): string {
  const databaseMappings = FIELD_VALUE_MAPPINGS[databaseCode];
  if (!databaseMappings) {
    return displayValue;
  }
  
  const fieldMappings = databaseMappings[fieldName];
  if (!fieldMappings) {
    return displayValue;
  }
  
  // Try exact match first
  const exactMatch = fieldMappings.find(
    mapping => mapping.displayValue.toLowerCase() === displayValue.toLowerCase()
  );
  
  if (exactMatch) {
    return exactMatch.databaseValue;
  }
  
  // Try partial match
  const partialMatch = fieldMappings.find(
    mapping => mapping.displayValue.toLowerCase().includes(displayValue.toLowerCase()) ||
               displayValue.toLowerCase().includes(mapping.displayValue.toLowerCase())
  );
  
  if (partialMatch) {
    return partialMatch.databaseValue;
  }
  
  return displayValue;
}

/**
 * Get all available options for a field
 * @param databaseCode Database code
 * @param fieldName Field name
 * @returns Array of available options
 */
export function getFieldOptions(
  databaseCode: string, 
  fieldName: string
): FieldValueMapping[] {
  const databaseMappings = FIELD_VALUE_MAPPINGS[databaseCode];
  if (!databaseMappings) {
    return [];
  }
  
  return databaseMappings[fieldName] || [];
}

/**
 * Check if a field has value mappings
 * @param databaseCode Database code
 * @param fieldName Field name
 * @returns True if mappings exist
 */
export function hasFieldMappings(
  databaseCode: string, 
  fieldName: string
): boolean {
  const databaseMappings = FIELD_VALUE_MAPPINGS[databaseCode];
  if (!databaseMappings) {
    return false;
  }
  
  const fieldMappings = databaseMappings[fieldName];
  return fieldMappings && fieldMappings.length > 0;
}
