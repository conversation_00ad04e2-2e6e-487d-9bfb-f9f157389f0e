import { db } from './prisma';

/**
 * 导出配置相关工具函数
 */

export interface ExportLimits {
  maxExportLimit: number;
  defaultExportLimit: number;
  exportConfig?: {
    formats?: string[];
    allowLargeExport?: boolean;
    description?: string;
    [key: string]: unknown;
  };
}

/**
 * 获取数据库的导出限制配置
 */
export async function getDatabaseExportLimits(databaseCode: string): Promise<ExportLimits> {
  try {
    const config = await db.databaseConfig.findUnique({
      where: { code: databaseCode, isActive: true },
      select: {
        maxExportLimit: true,
        defaultExportLimit: true,
        exportConfig: true,
      },
    });

    if (!config) {
      // 返回默认配置
      return {
        maxExportLimit: 10000,
        defaultExportLimit: 1000,
        exportConfig: {
          formats: ['csv', 'excel', 'json'],
          allowLargeExport: false,
          description: '默认导出配置',
        },
      };
    }

    return {
      maxExportLimit: config.maxExportLimit || 10000,
      defaultExportLimit: config.defaultExportLimit || 1000,
      exportConfig: config.exportConfig as any || {
        formats: ['csv', 'excel', 'json'],
        allowLargeExport: false,
      },
    };
  } catch (__error) {
    console.warn(`获取导出配置失败 ${databaseCode}:`, __error);
    // 返回默认配置
    return {
      maxExportLimit: 10000,
      defaultExportLimit: 1000,
      exportConfig: {
        formats: ['csv', 'excel', 'json'],
        allowLargeExport: false,
        description: '默认导出配置（获取失败回退）',
      },
    };
  }
}

/**
 * 验证和调整导出限制
 */
export function validateExportLimit(
  requestedLimit: number | undefined,
  exportLimits: ExportLimits
): number {
  // 如果没有指定限制，使用默认值
  if (!requestedLimit || requestedLimit <= 0) {
    return exportLimits.defaultExportLimit;
  }

  // 如果请求的限制超过最大值，使用最大值
  if (requestedLimit > exportLimits.maxExportLimit) {
    return exportLimits.maxExportLimit;
  }

  return requestedLimit;
}

/**
 * 检查导出格式是否支持
 */
export function isSupportedExportFormat(
  format: string,
  exportLimits: ExportLimits
): boolean {
  const supportedFormats = exportLimits.exportConfig?.formats || ['csv', 'excel', 'json'];
  return supportedFormats.includes(format.toLowerCase());
}

/**
 * 获取导出配置摘要（用于日志和调试）
 */
export function getExportConfigSummary(
  databaseCode: string,
  exportLimits: ExportLimits,
  actualLimit: number
): string {
  return `${databaseCode} 导出配置: 请求=${actualLimit}, 默认=${exportLimits.defaultExportLimit}, 最大=${exportLimits.maxExportLimit}`;
}
