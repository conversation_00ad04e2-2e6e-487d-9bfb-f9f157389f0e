/**
 * Search Performance Optimizations
 * 
 * This module provides utilities to optimize search performance
 * and improve user experience across the search system.
 */

import { SearchCondition } from '@/lib/api';

// Cache for search results
const searchCache = new Map<string, { data: any; timestamp: number; ttl: number }>();

// Cache TTL in milliseconds (5 minutes)
const DEFAULT_CACHE_TTL = 5 * 60 * 1000;

/**
 * Generates a cache key for search parameters
 */
export function generateSearchCacheKey(params: {
  database?: string;
  query?: string;
  filters?: Record<string, unknown>;
  conditions?: SearchCondition[];
  sortBy?: string;
  sortOrder?: string;
  page?: number;
  limit?: number;
}): string {
  const normalized = {
    database: params.database || '',
    query: params.query || '',
    filters: JSON.stringify(params.filters || {}),
    conditions: JSON.stringify(params.conditions || []),
    sortBy: params.sortBy || '',
    sortOrder: params.sortOrder || 'desc',
    page: params.page || 1,
    limit: params.limit || 20
  };
  
  return btoa(JSON.stringify(normalized)).replace(/[^a-zA-Z0-9]/g, '');
}

/**
 * Caches search results with TTL
 */
export function cacheSearchResult(key: string, data: any, ttl: number = DEFAULT_CACHE_TTL): void {
  searchCache.set(key, {
    data,
    timestamp: Date.now(),
    ttl
  });
  
  // Clean up expired entries periodically
  if (searchCache.size > 100) {
    cleanExpiredCache();
  }
}

/**
 * Retrieves cached search results if valid
 */
export function getCachedSearchResult(key: string): any | null {
  const cached = searchCache.get(key);
  
  if (!cached) {
    return null;
  }
  
  const now = Date.now();
  if (now - cached.timestamp > cached.ttl) {
    searchCache.delete(key);
    return null;
  }
  
  return cached.data;
}

/**
 * Clears expired cache entries
 */
export function cleanExpiredCache(): void {
  const now = Date.now();
  
  for (const [key, cached] of searchCache.entries()) {
    if (now - cached.timestamp > cached.ttl) {
      searchCache.delete(key);
    }
  }
}

/**
 * Clears all cached search results
 */
export function clearSearchCache(): void {
  searchCache.clear();
}

/**
 * Debounces search function calls
 */
export function debounceSearch<T extends (...args: any[]) => any>(
  func: T,
  delay: number = 300
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
}

/**
 * Optimizes filter objects by removing empty values
 */
export function optimizeFilters(filters: Record<string, unknown>): Record<string, unknown> {
  const optimized: Record<string, unknown> = {};
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      if (Array.isArray(value)) {
        if (value.length > 0) {
          optimized[key] = value;
        }
      } else if (typeof value === 'object') {
        const obj = value as Record<string, unknown>;
        const hasValidValues = Object.values(obj).some(v => 
          v !== undefined && v !== null && v !== ''
        );
        if (hasValidValues) {
          optimized[key] = value;
        }
      } else {
        optimized[key] = value;
      }
    }
  });
  
  return optimized;
}

/**
 * Validates and normalizes search conditions
 */
export function normalizeSearchConditions(conditions: SearchCondition[]): SearchCondition[] {
  return conditions
    .filter(condition => {
      // Remove invalid conditions
      if (!condition.field || !condition.operator) {
        return false;
      }
      
      // Check if value is meaningful
      if (typeof condition.value === 'string') {
        return condition.value.trim() !== '';
      }
      
      if (Array.isArray(condition.value)) {
        return condition.value.length > 0;
      }
      
      if (typeof condition.value === 'object' && condition.value !== null) {
        const obj = condition.value as { from?: string; to?: string };
        return obj.from || obj.to;
      }
      
      return condition.value !== undefined && condition.value !== null;
    })
    .map((condition, index) => ({
      ...condition,
      // Normalize logic for proper chaining
      logic: index === 0 ? undefined : (condition.logic || 'AND')
    }));
}

/**
 * Estimates search complexity for performance optimization
 */
export function estimateSearchComplexity(params: {
  query?: string;
  filters?: Record<string, unknown>;
  conditions?: SearchCondition[];
}): 'low' | 'medium' | 'high' {
  let complexity = 0;
  
  // Query complexity
  if (params.query) {
    complexity += params.query.length > 50 ? 2 : 1;
  }
  
  // Filter complexity
  if (params.filters) {
    const filterCount = Object.keys(optimizeFilters(params.filters)).length;
    complexity += filterCount;
  }
  
  // Advanced conditions complexity
  if (params.conditions) {
    const validConditions = normalizeSearchConditions(params.conditions);
    complexity += validConditions.length * 2; // Advanced conditions are more complex
  }
  
  if (complexity <= 3) return 'low';
  if (complexity <= 8) return 'medium';
  return 'high';
}

/**
 * Suggests performance optimizations based on search parameters
 */
export function getPerformanceRecommendations(params: {
  query?: string;
  filters?: Record<string, unknown>;
  conditions?: SearchCondition[];
  resultCount?: number;
}): string[] {
  const recommendations: string[] = [];
  const complexity = estimateSearchComplexity(params);
  
  if (complexity === 'high') {
    recommendations.push('Consider using more specific filters to narrow results');
  }
  
  if (params.query && params.query.length < 3) {
    recommendations.push('Use longer search terms for better performance');
  }
  
  if (params.conditions && params.conditions.length > 5) {
    recommendations.push('Consider combining similar conditions for better performance');
  }
  
  if (params.resultCount && params.resultCount > 1000) {
    recommendations.push('Use additional filters to reduce result set size');
  }
  
  return recommendations;
}

/**
 * Search performance metrics tracking
 */
export interface SearchMetrics {
  searchTime: number;
  resultCount: number;
  cacheHit: boolean;
  complexity: 'low' | 'medium' | 'high';
  recommendations: string[];
}

/**
 * Tracks search performance metrics
 */
export function trackSearchMetrics(
  startTime: number,
  params: any,
  resultCount: number,
  cacheHit: boolean = false
): SearchMetrics {
  const searchTime = performance.now() - startTime;
  const complexity = estimateSearchComplexity(params);
  const recommendations = getPerformanceRecommendations({
    ...params,
    resultCount
  });
  
  return {
    searchTime,
    resultCount,
    cacheHit,
    complexity,
    recommendations
  };
}

/**
 * Intelligent cache warming for popular searches
 */
export function warmSearchCache(popularSearches: Array<{
  database: string;
  query?: string;
  filters?: Record<string, unknown>;
}>): void {
  // This would be implemented to pre-cache popular search combinations
  // Could be called during low-traffic periods or on application startup
  console.log('Cache warming for', popularSearches.length, 'popular searches');
}
