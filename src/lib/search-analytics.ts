import { db } from '@/lib/prisma';
import { Prisma } from '@prisma/client';

export interface SearchAnalyticsData {
  userId?: string;
  sessionId?: string;
  database: string;
  searchType: 'simple' | 'advanced' | 'filter';
  searchQuery?: string;
  searchFields?: Record<string, any>;
  filters?: Record<string, any>;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  resultsCount?: number;
  searchTime?: number;
  ip?: string;
  userAgent?: string;
}

// 记录详细的搜索分析数据
export async function recordSearchAnalytics(data: SearchAnalyticsData) {
  try {
    await db.searchAnalytics.create({
      data: {
        userId: data.userId,
        sessionId: data.sessionId,
        database: data.database,
        searchType: data.searchType,
        searchQuery: data.searchQuery,
        searchFields: data.searchFields ? JSON.stringify(data.searchFields) : undefined,
        filters: data.filters ? JSON.stringify(data.filters) : undefined,
        sortBy: data.sortBy,
        sortOrder: data.sortOrder,
        resultsCount: data.resultsCount,
        searchTime: data.searchTime,
        ip: data.ip || '127.0.0.1',
        userAgent: data.userAgent,
      },
    });
  } catch (__error) {
    console.error('Failed to record search analytics:', __error);
    // 不抛出错误，避免影响主要功能
  }
}

// 获取搜索分析统计
export async function getSearchAnalytics(options: {
  database?: string;
  userId?: string;
  timeRange?: string; // '1d', '7d', '30d', '90d'
  limit?: number;
}) {
  const { database, userId, timeRange = '7d', limit = 100 } = options;

  // 计算时间范围
  const now = new Date();
  let startDate: Date;
  switch (timeRange) {
    case '1d':
      startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      break;
    case '7d':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case '30d':
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    case '90d':
      startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      break;
    default:
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  }

  const whereCondition: Record<string, unknown> = {
    createdAt: {
      gte: startDate,
      lte: now,
    },
  };

  if (database) {
    whereCondition.database = database;
  }

  if (userId) {
    whereCondition.userId = userId;
  }

  try {
    // 获取搜索记录
    const searches = await db.searchAnalytics.findMany({
      where: whereCondition,
      orderBy: { createdAt: 'desc' },
      take: limit,
      select: {
        id: true,
        database: true,
        searchType: true,
        searchQuery: true,
        searchFields: true,
        filters: true,
        resultsCount: true,
        searchTime: true,
        createdAt: true,
        userId: true,
      },
    });

    // 热门搜索词统计
    const topSearchQueries = await db.searchAnalytics.groupBy({
      by: ['searchQuery'],
      where: {
        ...whereCondition,
        searchQuery: { not: null },
      },
      _count: { searchQuery: true },
      orderBy: { _count: { searchQuery: 'desc' } },
      take: 20,
    });

    // 搜索类型统计
    const searchTypeStats = await db.searchAnalytics.groupBy({
      by: ['searchType'],
      where: whereCondition,
      _count: { searchType: true },
      orderBy: { _count: { searchType: 'desc' } },
    });

    // 数据库搜索统计
    const databaseStats = await db.searchAnalytics.groupBy({
      by: ['database'],
      where: whereCondition,
      _count: { database: true },
      orderBy: { _count: { database: 'desc' } },
    });

    // 搜索字段使用统计
    const searchFieldsUsage = await getSearchFieldsUsage(whereCondition);

    return {
      searches: searches.map(search => ({
        ...search,
        searchFields: search.searchFields ? JSON.parse(search.searchFields as string) : null,
        filters: search.filters ? JSON.parse(search.filters as string) : null,
      })),
      statistics: {
        topSearchQueries: topSearchQueries.map(item => ({
          query: item.searchQuery,
          count: item._count.searchQuery,
        })),
        searchTypeStats: searchTypeStats.map(item => ({
          type: item.searchType,
          count: item._count.searchType,
        })),
        databaseStats: databaseStats.map(item => ({
          database: item.database,
          count: item._count.database,
        })),
        searchFieldsUsage,
      },
    };
  } catch (__error) {
    console.error('Failed to get search analytics:', __error);
    return {
      searches: [],
      statistics: {
        topSearchQueries: [],
        searchTypeStats: [],
        databaseStats: [],
        searchFieldsUsage: [],
      },
    };
  }
}

// 获取搜索字段使用统计
async function getSearchFieldsUsage(whereCondition: Record<string, unknown>) {
  try {
    const searches = await db.searchAnalytics.findMany({
      where: {
        ...whereCondition,
        searchFields: { not: Prisma.DbNull },
      },
      select: { searchFields: true },
    });

    const fieldUsage: Record<string, number> = {};

    searches.forEach(search => {
      if (search.searchFields) {
        try {
          const fields = JSON.parse(search.searchFields as string);
          Object.keys(fields).forEach(fieldName => {
            if (fields[fieldName] && fields[fieldName].toString().trim()) {
              fieldUsage[fieldName] = (fieldUsage[fieldName] || 0) + 1;
            }
          });
        } catch (_e) {
          // 忽略JSON解析错误
        }
      }
    });

    return Object.entries(fieldUsage)
      .map(([field, count]) => ({ field, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  } catch (__error) {
    console.error('Failed to get search fields usage:', __error);
    return [];
  }
}

// 获取用户搜索历史
export async function getUserSearchHistory(userId: string, limit = 0) {
  try {
    const searches = await db.searchAnalytics.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: limit,
      select: {
        id: true,
        database: true,
        searchType: true,
        searchQuery: true,
        searchFields: true,
        filters: true,
        resultsCount: true,
        createdAt: true,
      },
    });

    return searches.map(search => ({
      ...search,
      searchFields: search.searchFields ? JSON.parse(search.searchFields as string) : null,
      filters: search.filters ? JSON.parse(search.filters as string) : null,
    }));
  } catch (__error) {
    console.error('Failed to get user search history:', __error);
    return [];
  }
}

// 获取热门搜索词
export async function getPopularSearchQueries(database?: string, limit = 0) {
  try {
    const whereCondition: Record<string, unknown> = {
      searchQuery: { not: null },
      createdAt: {
        gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 最近30天
      },
    };

    if (database) {
      whereCondition.database = database;
    }

    const popularQueries = await db.searchAnalytics.groupBy({
      by: ['searchQuery'],
      where: whereCondition,
      _count: { searchQuery: true },
      orderBy: { _count: { searchQuery: 'desc' } },
      take: limit,
    });

    return popularQueries.map(item => ({
      query: item.searchQuery,
      count: item._count.searchQuery,
    }));
  } catch (__error) {
    console.error('Failed to get popular search queries:', __error);
    return [];
  }
}
