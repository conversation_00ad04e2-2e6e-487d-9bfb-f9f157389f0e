/**
 * 统计配置模板系统
 * 提供常用的统计配置模板，便于快速为新数据库配置统计功能
 */

export interface StatisticsFieldConfig {
  fieldName: string;
  isStatisticsEnabled: boolean;
  statisticsOrder: number;
  statisticsType: 'count' | 'sum' | 'avg' | 'min_max' | 'group_by';
  statisticsDisplayName: string;
  statisticsConfig: Record<string, unknown>;
}

export interface StatisticsTemplate {
  name: string;
  description: string;
  fields: StatisticsFieldConfig[];
}

/**
 * 医疗器械数据库统计模板
 */
export const MEDICAL_DEVICE_STATISTICS_TEMPLATE: StatisticsTemplate = {
  name: '医疗器械数据库统计',
  description: '适用于医疗器械注册、审批等相关数据库的统计配置',
  fields: [
    {
      fieldName: 'category',
      isStatisticsEnabled: true,
      statisticsOrder: 1,
      statisticsType: 'group_by',
      statisticsDisplayName: '产品类别分布',
      statisticsConfig: {
        limit: 10,
        description: '按产品类别统计数量分布',
        showPercentage: true
      }
    },
    {
      fieldName: 'companyName',
      isStatisticsEnabled: true,
      statisticsOrder: 2,
      statisticsType: 'group_by',
      statisticsDisplayName: '主要企业',
      statisticsConfig: {
        limit: 8,
        description: '按企业统计产品数量',
        showPercentage: false
      }
    },
    {
      fieldName: 'deviceClass',
      isStatisticsEnabled: true,
      statisticsOrder: 3,
      statisticsType: 'group_by',
      statisticsDisplayName: '器械类别',
      statisticsConfig: {
        limit: 5,
        description: '按器械类别统计分布'
      }
    },
    {
      fieldName: 'managementType',
      isStatisticsEnabled: true,
      statisticsOrder: 4,
      statisticsType: 'group_by',
      statisticsDisplayName: '管理类别',
      statisticsConfig: {
        limit: 5,
        description: '按管理类别统计分布'
      }
    },
    {
      fieldName: 'approvalDate',
      isStatisticsEnabled: true,
      statisticsOrder: 5,
      statisticsType: 'min_max',
      statisticsDisplayName: '批准日期范围',
      statisticsConfig: {
        description: '显示最早和最晚的批准日期',
        dateFormat: 'YYYY-MM-DD'
      }
    }
  ]
};

/**
 * 美国器械分类数据库统计模板
 */
export const US_CLASS_STATISTICS_TEMPLATE: StatisticsTemplate = {
  name: '美国器械分类统计',
  description: '专门为美国FDA器械分类数据库设计的统计配置',
  fields: [
    {
      fieldName: 'deviceclass',
      isStatisticsEnabled: true,
      statisticsOrder: 1,
      statisticsType: 'group_by',
      statisticsDisplayName: '器械类别分布',
      statisticsConfig: {
        limit: 10,
        description: '按器械类别统计数量分布',
        showPercentage: true
      }
    },
    {
      fieldName: 'medicalspecialty',
      isStatisticsEnabled: true,
      statisticsOrder: 2,
      statisticsType: 'group_by',
      statisticsDisplayName: '医学专科分布',
      statisticsConfig: {
        limit: 8,
        description: '按医学专科统计数量分布',
        showPercentage: false
      }
    },
    {
      fieldName: 'productcode',
      isStatisticsEnabled: true,
      statisticsOrder: 3,
      statisticsType: 'count',
      statisticsDisplayName: '产品代码统计',
      statisticsConfig: {
        description: '产品代码总数统计'
      }
    },
    {
      fieldName: 'regulationnumber',
      isStatisticsEnabled: true,
      statisticsOrder: 4,
      statisticsType: 'count',
      statisticsDisplayName: '法规编号统计',
      statisticsConfig: {
        description: '法规编号总数统计'
      }
    }
  ]
};

/**
 * 药品数据库统计模板
 */
export const DRUG_STATISTICS_TEMPLATE: StatisticsTemplate = {
  name: '药品数据库统计',
  description: '适用于药品注册、审批等相关数据库的统计配置',
  fields: [
    {
      fieldName: 'drugType',
      isStatisticsEnabled: true,
      statisticsOrder: 1,
      statisticsType: 'group_by',
      statisticsDisplayName: '药品类型分布',
      statisticsConfig: {
        limit: 8,
        description: '按药品类型统计数量分布'
      }
    },
    {
      fieldName: 'activeIngredient',
      isStatisticsEnabled: true,
      statisticsOrder: 2,
      statisticsType: 'group_by',
      statisticsDisplayName: '活性成分分布',
      statisticsConfig: {
        limit: 10,
        description: '按活性成分统计药品数量'
      }
    },
    {
      fieldName: 'manufacturer',
      isStatisticsEnabled: true,
      statisticsOrder: 3,
      statisticsType: 'group_by',
      statisticsDisplayName: '主要制造商',
      statisticsConfig: {
        limit: 8,
        description: '按制造商统计药品数量'
      }
    },
    {
      fieldName: 'approvalStatus',
      isStatisticsEnabled: true,
      statisticsOrder: 4,
      statisticsType: 'group_by',
      statisticsDisplayName: '审批状态',
      statisticsConfig: {
        limit: 5,
        description: '按审批状态统计分布'
      }
    }
  ]
};

/**
 * 基础统计模板
 */
export const BASIC_STATISTICS_TEMPLATE: StatisticsTemplate = {
  name: '基础统计模板',
  description: '适用于大多数数据库的基础统计配置',
  fields: [
    {
      fieldName: 'category',
      isStatisticsEnabled: true,
      statisticsOrder: 1,
      statisticsType: 'group_by',
      statisticsDisplayName: '分类统计',
      statisticsConfig: {
        limit: 10,
        description: '按分类统计数量分布'
      }
    },
    {
      fieldName: 'status',
      isStatisticsEnabled: true,
      statisticsOrder: 2,
      statisticsType: 'group_by',
      statisticsDisplayName: '状态分布',
      statisticsConfig: {
        limit: 5,
        description: '按状态统计分布'
      }
    },
    {
      fieldName: 'createdAt',
      isStatisticsEnabled: true,
      statisticsOrder: 3,
      statisticsType: 'min_max',
      statisticsDisplayName: '创建时间范围',
      statisticsConfig: {
        description: '显示最早和最晚的创建时间',
        dateFormat: 'YYYY-MM-DD'
      }
    }
  ]
};

/**
 * 所有可用的统计模板
 */
export const STATISTICS_TEMPLATES = {
  medical_device: MEDICAL_DEVICE_STATISTICS_TEMPLATE,
  us_class: US_CLASS_STATISTICS_TEMPLATE,
  drug: DRUG_STATISTICS_TEMPLATE,
  basic: BASIC_STATISTICS_TEMPLATE,
};

/**
 * 根据数据库类型获取推荐的统计模板
 */
export function getRecommendedStatisticsTemplate(databaseCode: string): StatisticsTemplate {
  // 根据数据库代码推荐合适的模板
  if (databaseCode.includes('us_class')) {
    return US_CLASS_STATISTICS_TEMPLATE;
  } else if (databaseCode.includes('drug') || databaseCode.includes('medicine')) {
    return DRUG_STATISTICS_TEMPLATE;
  } else if (databaseCode.includes('device') || databaseCode.includes('medical')) {
    return MEDICAL_DEVICE_STATISTICS_TEMPLATE;
  } else {
    return BASIC_STATISTICS_TEMPLATE;
  }
}

/**
 * 应用统计模板到数据库配置
 */
export function applyStatisticsTemplate(
  template: StatisticsTemplate,
  existingFields: string[]
): StatisticsFieldConfig[] {
  // 只返回在现有字段中存在的统计配置
  return template.fields.filter(field => 
    existingFields.includes(field.fieldName)
  );
}
