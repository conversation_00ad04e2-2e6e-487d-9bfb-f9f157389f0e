import 'server-only';

import { getSession } from '../session';
import type { User } from '../auth';
import { MEMBERSHIP_BENEFITS } from '../permissions';

/**
 * 检查用户是否有权访问某个特定的资源。
 *
 * @param requiredAccessLevel - 访问资源所需的最低权限级别。
 * @returns {Promise<boolean>} - 如果用户有权访问则返回 true，否则返回 false。
 */
export async function checkPermissions(
  requiredAccessLevel: 'free' | 'premium' | 'enterprise'
): Promise<boolean> {
  // 规则1: 如果资源是免费的，则任何用户（包括未登录用户）都可以访问。
  if (requiredAccessLevel === 'free') {
    return true;
  }
  
  const session = await getSession();
  if (!session?.userId) {
    // 未登录用户不能访问付费资源
    return false;
  }

  const user = session.userDetails as User;
  const userLevel = user.membershipType;

  // 确保 userLevel 是有效的会员类型
  if (!userLevel || !(userLevel in MEMBERSHIP_BENEFITS)) {
    return false;
  }

  const levelHierarchy = {
    free: 0,
    premium: 1,
    enterprise: 2,
  };

  const required = levelHierarchy[requiredAccessLevel];
  const current = levelHierarchy[userLevel];

  return current >= required;
} 