import { db } from './prisma';

/**
 * 数据库代码到模型映射的接口定义
 */
export interface TableMapping {
  modelName: string;
  tableName: string;
  displayName: string;
  category: string;
  description: string;
}

/**
 * 动态表映射服务
 * 从数据库DatabaseConfig表读取表映射配置，替代硬编码映射
 */
export class DynamicTableMappingService {
  private static configCache: Record<string, TableMapping> = {};
  private static cacheExpiry = 0;
  private static readonly CACHE_TTL = 300000; // 5分钟缓存

  /**
   * 从数据库获取表映射配置
   * @param databaseCode 数据库代码
   * @returns 映射配置对象
   */
  static async getTableMapping(databaseCode: string): Promise<TableMapping | null> {
    // 检查缓存
    if (this.configCache[databaseCode] && Date.now() < this.cacheExpiry) {
      return this.configCache[databaseCode];
    }

    try {
      // 从数据库读取配置
      const config = await db.databaseConfig.findUnique({
        where: { 
          code: databaseCode, 
          isActive: true 
        },
        select: {
          code: true,
          name: true,
          category: true,
          description: true,
          tableName: true,
          modelName: true,
        }
      });

      if (!config || !config.tableName || !config.modelName) {
        console.warn(`[DynamicTableMappingService] 数据库代码 ${databaseCode} 的表映射配置不完整或不存在`);
        return null;
      }

      const mapping: TableMapping = {
        modelName: config.modelName,
        tableName: config.tableName,
        displayName: config.name,
        category: config.category,
        description: config.description || ''
      };

      // 更新缓存
      this.configCache[databaseCode] = mapping;
      this.cacheExpiry = Date.now() + this.CACHE_TTL;

      return mapping;
    } catch (__error) {
      console.error(`[DynamicTableMappingService] 获取表映射配置失败 ${databaseCode}:`, __error);
      return null;
    }
  }

  /**
   * 动态获取Prisma模型实例
   * @param databaseCode 数据库代码
   * @returns Prisma模型实例
   */
  static async getDynamicModel(databaseCode: string) {
    const mapping = await this.getTableMapping(databaseCode);
    if (!mapping) {
      throw new Error(`不支持的数据库代码: ${databaseCode}. 请检查DatabaseConfig表中的配置`);
    }

    // 通过动态属性访问获取对应的Prisma模型
    const model = (db as any)[mapping.modelName];
    if (!model) {
      console.error(`[DynamicTableMappingService] 找不到Prisma模型: ${mapping.modelName}`);
      throw new Error(`找不到模型: ${mapping.modelName}. 请检查Prisma schema定义`);
    }

    return model;
  }

  /**
   * 验证数据库代码是否有效
   * @param databaseCode 数据库代码
   * @returns 验证结果
   */
  static async validateDatabaseCode(databaseCode: string): Promise<{ isValid: boolean; error?: string; status?: number }> {
    if (!databaseCode || typeof databaseCode !== 'string') {
      return {
        isValid: false,
        error: '数据库代码不能为空',
        status: 400
      };
    }

    try {
      const mapping = await this.getTableMapping(databaseCode);
      if (!mapping) {
        return {
          isValid: false,
          error: `不支持的数据库代码: ${databaseCode}`,
          status: 404
        };
      }

      return { isValid: true };
    } catch (__error) {
      return {
        isValid: false,
        error: '验证数据库代码时发生错误',
        status: 500
      };
    }
  }

  /**
   * 获取所有可用的数据库代码
   * @returns 数据库代码数组
   */
  static async getAllDatabaseCodes(): Promise<string[]> {
    try {
      const configs = await db.databaseConfig.findMany({
        where: { 
          isActive: true,
          tableName: { not: null },
          modelName: { not: null }
        },
        select: { code: true },
        orderBy: { sortOrder: 'asc' }
      });

      return configs.map(config => config.code);
    } catch (__error) {
      console.error('[DynamicTableMappingService] 获取数据库代码列表失败:', __error);
      return [];
    }
  }

  /**
   * 检查Prisma模型是否有效
   * @param model Prisma模型实例
   * @returns 是否为有效的Prisma模型
   */
  static isPrismaModel(model: unknown): model is { findMany: (args: unknown) => unknown; groupBy: (args: unknown) => unknown } {
    return typeof model === 'object' && model !== null && 'findMany' in model && 'groupBy' in model;
  }

  /**
   * 清空缓存
   */
  static clearCache(): void {
    this.configCache = {};
    this.cacheExpiry = 0;
    console.error('[DynamicTableMappingService] 缓存已清空');
  }

  /**
   * 获取缓存状态信息
   * @returns 缓存状态
   */
  static getCacheInfo(): { size: number; expiry: number; isExpired: boolean } {
    return {
      size: Object.keys(this.configCache).length,
      expiry: this.cacheExpiry,
      isExpired: Date.now() >= this.cacheExpiry
    };
  }
}

// 向后兼容的导出函数，保持现有API不变
export async function getTableMapping(databaseCode: string): Promise<TableMapping | null> {
  return DynamicTableMappingService.getTableMapping(databaseCode);
}

export async function getDynamicModel(databaseCode: string) {
  return DynamicTableMappingService.getDynamicModel(databaseCode);
}

export async function validateDatabaseCode(databaseCode: string) {
  return DynamicTableMappingService.validateDatabaseCode(databaseCode);
}

export async function getAllDatabaseCodes(): Promise<string[]> {
  return DynamicTableMappingService.getAllDatabaseCodes();
}

export function isPrismaModel(model: unknown): model is { findMany: (args: unknown) => unknown; groupBy: (args: unknown) => unknown } {
  return DynamicTableMappingService.isPrismaModel(model);
} 