import { randomUUID } from 'crypto';

/**
 * 生成UUID v4字符串
 * @returns 36字符的UUID v4字符串
 */
export function generateUUID(): string {
  return randomUUID();
}

/**
 * 验证字符串是否为有效的UUID格式
 * @param id 要验证的字符串
 * @returns 是否为有效的UUID
 */
export function isValidUUID(id: string): boolean {
  if (!id || typeof id !== 'string') return false;

  // UUID v4 格式：xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(id);
}

/**
 * @deprecated 为了向后兼容保留的函数，请使用 generateUUID
 */
export function generateULID(): string {
  return generateUUID();
}

/**
 * @deprecated 为了向后兼容保留的函数，请使用 isValidUUID
 */
export function isValidULID(id: string): boolean {
  return isValidUUID(id);
}

/**
 * 从ULID中提取时间戳
 * @param ulidString ULID字符串
 * @returns 时间戳（毫秒）
 */
export function getTimestampFromULID(ulidString: string): number {
  if (!isValidULID(ulidString)) {
    throw new Error('Invalid ULID format');
  }
  
  // ULID的前10个字符是时间戳部分
  const timestampPart = ulidString.substring(0, 10);
  
  // 将base32编码的时间戳转换为数字
  const base32Chars = '0123456789ABCDEFGHJKMNPQRSTVWXYZ';
  let timestamp = 0;
  
  for (let i = 0; i < timestampPart.length; i++) {
    const char = timestampPart[i];
    const value = base32Chars.indexOf(char);
    if (value === -1) {
      throw new Error('Invalid ULID timestamp');
    }
    timestamp = timestamp * 32 + value;
  }
  
  return timestamp;
}

/**
 * 从ULID中提取创建时间
 * @param ulidString ULID字符串
 * @returns Date对象
 */
export function getDateFromULID(ulidString: string): Date {
  const timestamp = getTimestampFromULID(ulidString);
  return new Date(timestamp);
} 