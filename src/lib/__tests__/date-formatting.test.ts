import {
  formatDate,
  formatTableDate,
  formatExportDate,
  formatDetailDate,
  formatDateAuto,
  isValidDateString,
  detectDateField,
  smartFormatDate
} from '../utils';

describe('Date Formatting Functions', () => {
  const testDate = '2024-01-15';
  const testTimestamp = 1705276800000; // 2024-01-15
  const testDateObj = new Date('2024-01-15');

  describe('formatDate', () => {
    it('should format date string correctly in YYYY-MM-DD format', () => {
      const result = formatDate(testDate);
      expect(result).toBe('2024-01-15');
    });

    it('should handle null/undefined gracefully', () => {
      expect(formatDate(null)).toBe('');
      expect(formatDate(undefined)).toBe('');
      expect(formatDate('')).toBe('');
    });

    it('should format all dates in YYYY-MM-DD format regardless of options', () => {
      const result1 = formatDate(testDate, { format: 'medium' });
      const result2 = formatDate(testDate, { format: 'long' });
      const result3 = formatDate(testDate, { format: 'iso' });
      expect(result1).toBe('2024-01-15');
      expect(result2).toBe('2024-01-15');
      expect(result3).toBe('2024-01-15');
    });
  });

  describe('isValidDateString', () => {
    it('should validate date strings correctly', () => {
      expect(isValidDateString('2024-01-15')).toBe(true);
      expect(isValidDateString('2024/01/15')).toBe(true);
      expect(isValidDateString('Jan 15, 2024')).toBe(true);
      expect(isValidDateString(testDateObj)).toBe(true);
      expect(isValidDateString(testTimestamp)).toBe(true);
    });

    it('should reject invalid dates and small numbers', () => {
      expect(isValidDateString('invalid-date')).toBe(false);
      expect(isValidDateString('')).toBe(false);
      expect(isValidDateString(null)).toBe(false);
      expect(isValidDateString(undefined)).toBe(false);
      // 重要：应该拒绝小数字，避免误识别
      expect(isValidDateString('2')).toBe(false);
      expect(isValidDateString('123')).toBe(false);
      expect(isValidDateString(2)).toBe(false);
      expect(isValidDateString(123)).toBe(false);
    });
  });

  describe('detectDateField', () => {
    it('should detect date fields by name', () => {
      const result = detectDateField('approvalDate', ['2024-01-15']);
      expect(result.isLikelyDate).toBe(true);
      expect(result.confidence).toBeGreaterThan(0.5);
    });

    it('should detect date fields by content', () => {
      const result = detectDateField('someField', ['2024-01-15', '2024-01-16', '2024-01-17']);
      expect(result.isLikelyDate).toBe(true);
    });

    it('should not detect non-date fields', () => {
      const result = detectDateField('productName', ['Product A', 'Product B']);
      expect(result.isLikelyDate).toBe(false);
    });
  });

  describe('smartFormatDate', () => {
    it('should format when fieldType is date in YYYY-MM-DD format', () => {
      const result = smartFormatDate(testDate, { fieldType: 'date' });
      expect(result).toBe('2024-01-15');
    });

    it('should detect and format date-like fields in YYYY-MM-DD format', () => {
      const result = smartFormatDate(testDate, { fieldName: 'approvalDate' });
      expect(result).toBe('2024-01-15');
    });

    it('should return string for non-date values', () => {
      const result = smartFormatDate('Product Name', { fieldType: 'text' });
      expect(result).toBe('Product Name');
    });

    it('should format timestamp in YYYY-MM-DD format', () => {
      const result = smartFormatDate(testTimestamp, { fieldType: 'date' });
      expect(result).toBe('2024-01-15');
    });

    it('should format Date object in YYYY-MM-DD format', () => {
      const result = smartFormatDate(testDateObj, { fieldType: 'date' });
      expect(result).toBe('2024-01-15');
    });
  });

  describe('Specialized formatters', () => {
    it('formatTableDate should return YYYY-MM-DD format', () => {
      const result = formatTableDate(testDate);
      expect(result).toBe('2024-01-15');
    });

    it('formatExportDate should return YYYY-MM-DD format', () => {
      const result = formatExportDate(testDate);
      expect(result).toBe('2024-01-15');
    });

    it('formatDetailDate should return YYYY-MM-DD format', () => {
      const result = formatDetailDate(testDate);
      expect(result).toBe('2024-01-15');
    });

    it('formatDateAuto should return YYYY-MM-DD format', () => {
      const result = formatDateAuto(testDate);
      expect(result).toBe('2024-01-15');
    });
  });
});
