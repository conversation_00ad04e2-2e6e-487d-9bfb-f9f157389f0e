/**
 * @deprecated 此文件已被 syncEngine-refactored.ts 替代
 * 保留此文件仅为了向后兼容，新代码请使用 SmartSyncEngineRefactored
 */

import { db } from './prisma';
import { TableConfig, ImportMode, UniqueKeyContext } from './uniqueKeyConfig';
import { generateBusinessKeyInfo } from './utils';

// 同步结果统计
export interface SyncResult {
  totalRecords: number;
  inserted: number;
  updated: number;
  skipped: number;
  errors: number;
  errorDetails: Array<{
    row: number;
    businessKey: string;
    error: string;
  }>;
  processingTime: number;
}

// 数据变更日志
export interface ChangeLog {
  businessKey: string;
  businessKeyHash?: string;
  operation: 'CREATE' | 'UPDATE' | 'DELETE' | 'DEACTIVATE';
  oldData?: Record<string, any>;
  newData?: Record<string, any>;
  changeReason?: string;
  importedBy?: string;
  importedFrom?: string;
}

/**
 * @deprecated 智能同步引擎 - 旧版本
 * 请使用 SmartSyncEngineRefactored 替代
 */
export class SmartSyncEngine {
  private tableName: string;
  private tableConfig!: TableConfig; // 使用!断言，因为会在setTableConfig中设置
  private changeLogs: ChangeLog[] = [];
  private importedBy?: string;
  private importedFrom?: string;

  constructor(tableName: string, importedBy?: string, importedFrom?: string) {
    this.tableName = tableName;
    this.importedBy = importedBy;
    this.importedFrom = importedFrom;
  }

  // 设置表配置
  setTableConfig(config: TableConfig): void {
    this.tableConfig = config;
  }

  // 同步数据的主要方法
  async syncData(records: Record<string, any>[], mode: ImportMode = 'upsert'): Promise<SyncResult> {
    console.warn('⚠️ 使用已废弃的 SmartSyncEngine，请迁移到 SmartSyncEngineRefactored');
    
    const startTime = Date.now();
    const result: SyncResult = {
      totalRecords: records.length,
      inserted: 0,
      updated: 0,
      skipped: 0,
      errors: 0,
      errorDetails: [],
      processingTime: 0
    };

    if (!this.tableConfig) {
      throw new Error('表配置未设置，请先调用 setTableConfig()');
    }

    try {
      // 获取Prisma模型
      const model = (db as any)[this.tableName];
      if (!model) {
        throw new Error(`Prisma模型 ${this.tableName} 不存在`);
      }

      // 处理每条记录
      for (let i = 0; i < records.length; i++) {
        const record = records[i];
        
        try {
          // 生成业务键
          const context: UniqueKeyContext = {
            databaseCode: 'unknown', // 旧版本无法获取数据库代码
            tableName: this.tableName,
            importSource: this.importedFrom
          };
          const businessKeyInfo = generateBusinessKeyInfo(record, (row) => this.tableConfig.uniqueKeyRule(row, context), 'md5');
          const businessKey = businessKeyInfo.businessKey;
          
          // 检查记录是否已存在
          const existingRecord = await this.findExistingRecord(model, businessKeyInfo);
          
          if (existingRecord) {
            if (mode === 'insert') {
              result.skipped++;
              continue;
            }
            
            // 更新记录
            await model.update({
              where: { id: existingRecord.id },
              data: record
            });
            
            result.updated++;
            
            // 记录变更日志
            this.changeLogs.push({
              businessKey,
              businessKeyHash: businessKeyInfo.businessKeyHash,
              operation: 'UPDATE',
              oldData: existingRecord,
              newData: record,
              changeReason: `导入更新 - ${mode}`,
              importedBy: this.importedBy,
              importedFrom: this.importedFrom
            });
            
          } else {
            if (mode === 'replace') {
              result.skipped++;
              continue;
            }
            
            // 插入新记录
            await model.create({
              data: record
            });
            
            result.inserted++;
            
            // 记录变更日志
            this.changeLogs.push({
              businessKey,
              businessKeyHash: businessKeyInfo.businessKeyHash,
              operation: 'CREATE',
              newData: record,
              changeReason: `导入创建 - ${mode}`,
              importedBy: this.importedBy,
              importedFrom: this.importedFrom
            });
          }
          
        } catch (error) {
          result.errors++;
          result.errorDetails.push({
            row: i + 1,
            businessKey: 'unknown',
            error: error instanceof Error ? error.message : String(error)
          });
        }
      }
      
    } catch (error) {
      throw new Error(`同步失败: ${error instanceof Error ? error.message : String(error)}`);
    }

    result.processingTime = Date.now() - startTime;
    return result;
  }

  // 查找现有记录 - 简化版本
  private async findExistingRecord(model: any, businessKeyInfo: any): Promise<any> {
    try {
      // 尝试使用哈希查找
      if (this.tableConfig.hashConfig?.enabled && businessKeyInfo.businessKeyHash) {
        return await model.findFirst({
          where: { businessKeyHash: businessKeyInfo.businessKeyHash }
        });
      }

      // 如果没有哈希，返回null（旧版本不支持复杂查找）
      return null;
    } catch (error) {
      console.warn('Failed to find existing record:', error);
      return null;
    }
  }

  // 获取变更日志
  getChangeLogs(): ChangeLog[] {
    return this.changeLogs;
  }

  // 清空变更日志
  clearChangeLogs(): void {
    this.changeLogs = [];
  }
}

// 导出兼容性别名
export { SmartSyncEngine as SmartSyncEngineDeprecated };
