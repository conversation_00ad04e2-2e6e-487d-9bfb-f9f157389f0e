import { Client } from "@elastic/elasticsearch";

/**
 * Elasticsearch client 单例。
 *
 * 环境变量：
 * ELASTICSEARCH_NODE 例如 https://localhost:9200
 * ELASTICSEARCH_USERNAME / ELASTICSEARCH_PASSWORD 可选
 */
export const esClient = new Client({
  node: process.env.ELASTICSEARCH_NODE || "http://localhost:9200",
  auth: process.env.ELASTICSEARCH_USERNAME
    ? {
        username: process.env.ELASTICSEARCH_USERNAME,
        password: process.env.ELASTICSEARCH_PASSWORD || "",
      }
    : undefined,
  tls: {
    // 部分自签名证书环境下忽略校验；生产环境请根据需要调整
    rejectUnauthorized: false,
  },
}); 