import { db } from './prisma';
import { DynamicTableMappingService, type TableMapping } from './dynamicTableMappingService';

/**
 * 静态表映射服务
 * 在应用启动时预加载所有配置到内存中，提供高性能的配置访问
 * 与动态服务配合工作，提供缓存和初始化管理
 */
export class StaticTableMappingService {
  private static configCache: Record<string, TableMapping> = {};
  private static initialized = false;
  private static initializationPromise: Promise<void> | null = null;

  /**
   * 初始化服务，预加载所有配置
   */
  static async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    this.initializationPromise = this._doInitialize();
    return this.initializationPromise;
  }

  private static async _doInitialize(): Promise<void> {
    try {
      console.log('[StaticTableMappingService] 🚀 开始初始化静态配置...');

      // 从数据库加载所有活跃的配置
      const configs = await db.databaseConfig.findMany({
        where: { 
          isActive: true,
          tableName: { not: null },
          modelName: { not: null }
        },
        select: {
          code: true,
          name: true,
          category: true,
          description: true,
          tableName: true,
          modelName: true,
        }
      });

      // 构建配置缓存
      this.configCache = {};
      for (const config of configs) {
        if (config.tableName && config.modelName) {
          this.configCache[config.code] = {
            modelName: config.modelName,
            tableName: config.tableName,
            displayName: config.name,
            category: config.category,
            description: config.description || ''
          };
        }
      }

      this.initialized = true;
      this.initializationPromise = null;
      
      console.log(`[StaticTableMappingService] ✅ 初始化完成，加载了 ${Object.keys(this.configCache).length} 个配置`);

    } catch (error) {
      this.initialized = false;
      this.initializationPromise = null;
      console.error('[StaticTableMappingService] ❌ 初始化失败:', error);
      throw error;
    }
  }

  /**
   * 刷新配置（重新初始化）
   */
  static async refresh(): Promise<void> {
    console.log('[StaticTableMappingService] 🔄 刷新配置...');
    this.initialized = false;
    this.initializationPromise = null;
    this.configCache = {};
    await this.initialize();
  }

  /**
   * 获取表映射配置
   * @param databaseCode 数据库代码
   * @returns 映射配置对象
   */
  static getTableMapping(databaseCode: string): TableMapping | null {
    // 确保已初始化
    if (!this.initialized) {
      console.warn('[StaticTableMappingService] 服务未初始化，返回null');
      return null;
    }

    return this.configCache[databaseCode] || null;
  }

  /**
   * 获取动态模型实例
   * @param databaseCode 数据库代码
   * @returns Prisma模型实例
   */
  static getDynamicModel(databaseCode: string) {
    const mapping = this.getTableMapping(databaseCode);
    if (!mapping) {
      throw new Error(`不支持的数据库代码: ${databaseCode}. 请检查DatabaseConfig表中的配置`);
    }

    // 通过动态属性访问获取对应的Prisma模型
    const model = (db as any)[mapping.modelName];
    if (!model) {
      console.error(`[StaticTableMappingService] 找不到Prisma模型: ${mapping.modelName}`);
      throw new Error(`找不到模型: ${mapping.modelName}. 请检查Prisma schema定义`);
    }

    return model;
  }

  /**
   * 获取所有映射配置
   * @returns 所有映射配置
   */
  static getAllMappings(): Record<string, TableMapping> {
    return { ...this.configCache };
  }

  /**
   * 获取初始化状态
   * @returns 初始化状态信息
   */
  static getInitializationStatus(): {
    initialized: boolean;
    configCount: number;
    configCodes: string[];
  } {
    return {
      initialized: this.initialized,
      configCount: Object.keys(this.configCache).length,
      configCodes: Object.keys(this.configCache)
    };
  }

  /**
   * 检查Prisma模型是否有效
   * @param model Prisma模型实例
   * @returns 是否为有效的Prisma模型
   */
  static isPrismaModel(model: unknown): model is { findMany: (args: unknown) => unknown; groupBy: (args: unknown) => unknown } {
    return typeof model === 'object' && model !== null && 'findMany' in model && 'groupBy' in model;
  }

  /**
   * 清空缓存
   */
  static clearCache(): void {
    this.configCache = {};
    this.initialized = false;
    this.initializationPromise = null;
    console.log('[StaticTableMappingService] 缓存已清空');
  }
}

// 向后兼容的导出函数，保持现有API不变
export async function getDynamicModel(databaseCode: string) {
  // 确保服务已初始化
  if (!StaticTableMappingService.getInitializationStatus().initialized) {
    await StaticTableMappingService.initialize();
  }
  
  try {
    return StaticTableMappingService.getDynamicModel(databaseCode);
  } catch (error) {
    // 如果静态服务失败，回退到动态服务
    console.warn('[getDynamicModel] 静态服务失败，使用动态服务:', error);
    return DynamicTableMappingService.getDynamicModel(databaseCode);
  }
}

export async function validateDatabaseCode(databaseCode: string): Promise<{ isValid: boolean; error?: string; status?: number }> {
  if (!databaseCode || typeof databaseCode !== 'string') {
    return {
      isValid: false,
      error: '数据库代码不能为空',
      status: 400
    };
  }

  // 确保服务已初始化
  if (!StaticTableMappingService.getInitializationStatus().initialized) {
    await StaticTableMappingService.initialize();
  }

  const mapping = StaticTableMappingService.getTableMapping(databaseCode);
  if (mapping) {
    return { isValid: true };
  }

  // 如果静态服务中没有找到，尝试动态服务
  try {
    return await DynamicTableMappingService.validateDatabaseCode(databaseCode);
  } catch (_error) {
    return {
      isValid: false,
      error: `不支持的数据库代码: ${databaseCode}`,
      status: 404
    };
  }
}

export function isPrismaModel(model: unknown): model is { findMany: (args: unknown) => unknown; groupBy: (args: unknown) => unknown } {
  return StaticTableMappingService.isPrismaModel(model);
}

export async function getTableMapping(databaseCode: string): Promise<TableMapping | null> {
  // 确保服务已初始化
  if (!StaticTableMappingService.getInitializationStatus().initialized) {
    await StaticTableMappingService.initialize();
  }

  const staticMapping = StaticTableMappingService.getTableMapping(databaseCode);
  if (staticMapping) {
    return staticMapping;
  }

  // 如果静态服务中没有找到，尝试动态服务
  try {
    return await DynamicTableMappingService.getTableMapping(databaseCode);
  } catch (_error) {
    console.warn('[getTableMapping] 动态服务也失败:', _error);
    return null;
  }
}

export async function getAllDatabaseCodes(): Promise<string[]> {
  // 确保服务已初始化
  if (!StaticTableMappingService.getInitializationStatus().initialized) {
    await StaticTableMappingService.initialize();
  }

  const staticCodes = StaticTableMappingService.getInitializationStatus().configCodes;
  if (staticCodes.length > 0) {
    return staticCodes;
  }

  // 如果静态服务中没有数据，尝试动态服务
  try {
    return await DynamicTableMappingService.getAllDatabaseCodes();
  } catch (error) {
    console.warn('[getAllDatabaseCodes] 动态服务失败:', error);
    return [];
  }
}
