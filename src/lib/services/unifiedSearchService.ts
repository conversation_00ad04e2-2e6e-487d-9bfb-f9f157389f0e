import { ElasticsearchService, type ESSearchParams } from './elasticsearchService';
import { PrismaFetchService, type BatchFetchParams } from './prismaFetchService';
import { getAllDatabaseCodes } from '@/lib/dynamicTableMapping';

/**
 * 统一搜索结果接口
 */
export interface UnifiedSearchResult {
  success: boolean;
  data: {
    results: Array<{
      table_code: string;
      data: unknown[];
      total: number;
      es_total: number; // ES命中的总数
    }>;
    pagination: {
      page: number;
      limit: number;
      total_pages: number;
      total_results: number;
    };
    search_info: {
      query: string;
      search_time: number;
      es_took: number;
      missing_ids_count: number;
    };
  };
  error?: string;
}

/**
 * 全站搜索结果接口
 */
export interface GlobalSearchResult {
  success: boolean;
  data: Array<{
    database: string;
    count: number;
  }>;
  search_info: {
    query: string;
    search_time: number;
    es_took: number;
  };
  error?: string;
}

/**
 * 统一搜索参数接口
 */
export interface UnifiedSearchParams {
  query: string;
  table_code?: string; // 限定特定数据库（页内搜索）
  filters?: Record<string, unknown>;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

/**
 * 统一搜索服务
 * 整合ES搜索 + Prisma回捞，提供完整的搜索解决方案
 */
export class UnifiedSearchService {
  
  /**
   * 全站综合搜索
   * 仅返回各数据库的统计数量，用于首页搜索框
   * @param query 搜索关键词
   * @returns 各数据库的匹配数量
   */
  static async globalSearch(query: string): Promise<GlobalSearchResult> {
    const startTime = performance.now();
    
    try {
      console.log('[UnifiedSearchService] 全站搜索:', { query });
      
      if (!query.trim()) {
        return {
          success: false,
          data: [],
          search_info: {
            query,
            search_time: 0,
            es_took: 0
          },
          error: '搜索关键词不能为空'
        };
      }
      
      // 获取所有数据库代码
      const databaseCodes = await getAllDatabaseCodes();
      if (databaseCodes.length === 0) {
        return {
          success: false,
          data: [],
          search_info: {
            query,
            search_time: performance.now() - startTime,
            es_took: 0
          },
          error: '没有可搜索的数据库'
        };
      }
      
      // 使用ES按数据库分组搜索
      const esResults = await ElasticsearchService.searchByDatabases(query, databaseCodes);
      
      const searchTime = performance.now() - startTime;
      
      return {
        success: true,
        data: esResults,
        search_info: {
          query,
          search_time: searchTime,
          es_took: 0 // 这里可以从ES结果中获取
        }
      };
      
    } catch (error) {
      console.error('[UnifiedSearchService] 全站搜索失败:', error);
      
      return {
        success: false,
        data: [],
        search_info: {
          query,
          search_time: performance.now() - startTime,
          es_took: 0
        },
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }
  
  /**
   * 统一搜索（全站或页内）
   * ES先命中 -> Prisma回捞 -> 二次筛选排序分页
   * @param params 搜索参数
   * @returns 完整的搜索结果
   */
  static async unifiedSearch(params: UnifiedSearchParams): Promise<UnifiedSearchResult> {
    const startTime = performance.now();
    const { query, table_code, filters = {}, sort_by, sort_order = 'desc', page = 1, limit = 20 } = params;
    
    try {
      console.log('[UnifiedSearchService] 统一搜索:', { query, table_code, page, limit });
      
      if (!query.trim()) {
        // 如果没有搜索词但有筛选条件，直接走Prisma
        if (Object.keys(filters).length > 0 && table_code) {
          return await this.prismaOnlySearch({
            table_code,
            filters,
            sort_by,
            sort_order,
            page,
            limit
          });
        }
        
        return {
          success: false,
          data: {
            results: [],
            pagination: {
              page,
              limit,
              total_pages: 0,
              total_results: 0
            },
            search_info: {
              query,
              search_time: 0,
              es_took: 0,
              missing_ids_count: 0
            }
          },
          error: '搜索关键词不能为空'
        };
      }
      
      // 第一步：ES搜索获取相关ID
      const esParams: ESSearchParams = {
        query,
        table_code,
        from: 0,
        size: 10000 // 获取足够多的ID用于后续筛选
      };
      
      const esResult = await ElasticsearchService.search(esParams);
      
      if (esResult.hits.length === 0) {
        return {
          success: true,
          data: {
            results: [],
            pagination: {
              page,
              limit,
              total_pages: 0,
              total_results: 0
            },
            search_info: {
              query,
              search_time: performance.now() - startTime,
              es_took: esResult.took,
              missing_ids_count: 0
            }
          }
        };
      }
      
      // 第二步：按table_code分组ID
      const idGroups = this.groupIdsByTableCode(esResult.hits);
      
      // 第三步：Prisma批量回捞 + 二次筛选排序分页
      let fetchResults;
      if (table_code) {
        // 页内搜索：单库回捞
        const targetGroup = idGroups.find(g => g.table_code === table_code);
        if (!targetGroup) {
          return {
            success: true,
            data: {
              results: [],
              pagination: {
                page,
                limit,
                total_pages: 0,
                total_results: 0
              },
              search_info: {
                query,
                search_time: performance.now() - startTime,
                es_took: esResult.took,
                missing_ids_count: 0
              }
            }
          };
        }
        
        const singleResult = await PrismaFetchService.singleFetch({
          table_code,
          ids: targetGroup.ids,
          filters,
          sort_by,
          sort_order,
          page,
          limit
        });
        
        fetchResults = [singleResult];
      } else {
        // 全站搜索：多库回捞（不分页，返回每库的前几条）
        const batchParams: BatchFetchParams = {
          id_groups: idGroups,
          filters,
          sort_by,
          sort_order
        };
        
        fetchResults = await PrismaFetchService.batchFetch(batchParams);
      }
      
      // 第四步：处理缺失ID
      const totalMissingIds = fetchResults.reduce((sum, r) => sum + r.missing_ids.length, 0);
      if (totalMissingIds > 0) {
        // 记录缺失ID，触发同步任务
        for (const result of fetchResults) {
          if (result.missing_ids.length > 0) {
            await PrismaFetchService.logMissingIds(result.missing_ids, result.table_code);
          }
        }
      }
      
      // 第五步：构建返回结果
      const results = fetchResults.map(r => ({
        table_code: r.table_code,
        data: r.data,
        total: r.total,
        es_total: idGroups.find(g => g.table_code === r.table_code)?.ids.length || 0
      }));
      
      const totalResults = results.reduce((sum, r) => sum + r.total, 0);
      const totalPages = Math.ceil(totalResults / limit);
      
      const searchTime = performance.now() - startTime;
      
      return {
        success: true,
        data: {
          results,
          pagination: {
            page,
            limit,
            total_pages: totalPages,
            total_results: totalResults
          },
          search_info: {
            query,
            search_time: searchTime,
            es_took: esResult.took,
            missing_ids_count: totalMissingIds
          }
        }
      };
      
    } catch (error) {
      console.error('[UnifiedSearchService] 统一搜索失败:', error);
      
      return {
        success: false,
        data: {
          results: [],
          pagination: {
            page,
            limit,
            total_pages: 0,
            total_results: 0
          },
          search_info: {
            query,
            search_time: performance.now() - startTime,
            es_took: 0,
            missing_ids_count: 0
          }
        },
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }
  
  /**
   * 纯Prisma搜索（当没有搜索词只有筛选条件时使用）
   * @param params 搜索参数
   * @returns 搜索结果
   */
  private static async prismaOnlySearch(params: {
    table_code: string;
    filters: Record<string, unknown>;
    sort_by?: string;
    sort_order?: 'asc' | 'desc';
    page: number;
    limit: number;
  }): Promise<UnifiedSearchResult> {
    const startTime = performance.now();
    
    try {
      console.log('[UnifiedSearchService] 纯Prisma搜索:', params);
      
      // 这里需要实现纯Prisma的搜索逻辑
      // 可以复用现有的数据查询API逻辑
      
      // TODO: 实现纯Prisma搜索
      
      return {
        success: false,
        data: {
          results: [],
          pagination: {
            page: params.page,
            limit: params.limit,
            total_pages: 0,
            total_results: 0
          },
          search_info: {
            query: '',
            search_time: performance.now() - startTime,
            es_took: 0,
            missing_ids_count: 0
          }
        },
        error: '纯Prisma搜索功能待实现'
      };
      
    } catch (error) {
      console.error('[UnifiedSearchService] 纯Prisma搜索失败:', error);
      
      return {
        success: false,
        data: {
          results: [],
          pagination: {
            page: params.page,
            limit: params.limit,
            total_pages: 0,
            total_results: 0
          },
          search_info: {
            query: '',
            search_time: performance.now() - startTime,
            es_took: 0,
            missing_ids_count: 0
          }
        },
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }
  
  /**
   * 按table_code分组ID
   * @param hits ES搜索结果
   * @returns 分组后的ID列表
   */
  private static groupIdsByTableCode(hits: Array<{ id: string; table_code: string }>): Array<{
    table_code: string;
    ids: string[];
  }> {
    const groups = new Map<string, string[]>();
    
    for (const hit of hits) {
      if (!groups.has(hit.table_code)) {
        groups.set(hit.table_code, []);
      }
      groups.get(hit.table_code)!.push(hit.id);
    }
    
    return Array.from(groups.entries()).map(([table_code, ids]) => ({
      table_code,
      ids
    }));
  }
}
