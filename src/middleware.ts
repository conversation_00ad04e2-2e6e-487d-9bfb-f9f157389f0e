import { type NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { updateSession } from '@/lib/session';

// 配置中间件适用的路由
export const config = {
  matcher: [
    /*
     * 匹配除了以下路径之外的所有请求路径:
     * - _next/static (静态文件)
     * - _next/image (图片优化文件)
     * - favicon.ico (网站图标)
     * - /api/auth/ (认证相关接口，避免重复记录)
     */
    '/((?!_next/static|_next/image|favicon.ico|api/auth/).*)',
  ],
};

const RATE_LIMIT_WINDOW = 60 * 1000; // 1分钟
const RATE_LIMIT_COUNT = 30; // 1分钟内最多30次请求

function getIP(_request: NextRequest) {
  const headers = _request.headers;
  // 在 Edge 环境中，x-forwarded-for 是获取真实 IP 的标准方式。
  // request.ip 在非 Node.js 环境中可能不存在。
  const ip = headers.get('x-forwarded-for')?.split(',')[0].trim() || headers.get('x-real-ip')?.trim();
  return ip || '127.0.0.1'; // 如果都获取不到，则使用本地地址作为备用
}

export async function middleware(_request: NextRequest) {
  // 在开发环境中，跳过日志记录和速率限制，但保留会话更新
  if (process.env.NODE_ENV === 'development') {
    // 在开发环境中仍然需要更新会话以保持认证状态
    try {
      await updateSession();
    } catch (error) {
      console.warn('[Middleware] Session update failed in development:', error);
    }
    return NextResponse.next();
  }

  const ip = getIP(_request);

  try {
    // 1. 检查IP是否被屏蔽
    const blockedIp = await db.blockedIp.findFirst({
      where: {
        ip: ip,
        expiresAt: {
          gt: new Date(),
        },
      },
    });

    if (blockedIp) {
      console.error(`[Middleware] Blocked IP access: ${ip}`);
      return new NextResponse('Too many requests', { status: 429 });
    }

    // 并行处理日志记录和速率限制
    await Promise.all([
      logActivity(_request, ip),
      checkRateLimit(ip)
    ]);

  } catch (__error) {
    console.error('[Middleware] Error:', __error);
    // 即使中间件出错，也应保证网站核心功能可访问
  }

  // 尝试更新会话，不阻塞主流程
  await updateSession();

  // 正常处理请求
  return NextResponse.next();
}

async function checkRateLimit(ip: string) {
  const windowStart = new Date(Date.now() - RATE_LIMIT_WINDOW);
  
  const requestCount = await db.activityLog.count({
    where: {
      ip: ip,
      createdAt: {
        gt: windowStart,
      },
    },
  });

  if (requestCount > RATE_LIMIT_COUNT) {
    console.warn(`[Middleware] Rate limit exceeded for IP: ${ip}. Blocking now.`);
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // Block for 5 minutes
    
    await db.blockedIp.upsert({
      where: { ip: ip },
      update: { expiresAt },
      create: { ip, reason: 'Rate limit exceeded', expiresAt },
    });
  }
}

async function logActivity(request: NextRequest, ip: string) {
  const { pathname, search } = request.nextUrl;
  // Collect referer, database, eventType, sessionId and other fields
  const referer = request.headers.get('referer') || undefined;
  // Database parameter can be extracted from path or query parameters, simple extraction for example
  const url = new URL(request.url);
  const database = url.searchParams.get('database') || undefined;
  // eventType and sessionId can be passed through header or cookie, compatible collection for example
  const eventType = request.headers.get('x-event-type') || undefined;
  const sessionId = request.cookies.get('sessionId')?.value || undefined;

  await db.activityLog.create({
    data: {
      ip,
      userAgent: request.headers.get('user-agent'),
      path: pathname,
      method: request.method,
      queryParams: search ? search.slice(1) : undefined,
      referer,
      database,
      eventType,
      sessionId,
    }
  });
} 