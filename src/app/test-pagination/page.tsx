'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';

interface PaginationData {
  page: number;
  limit: number;
  totalCount: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
  maxPages: number;
  isAtMaxPages: boolean;
  maxPageSize: number;
  defaultPageSize: number;
}

interface ApiResponse {
  success: boolean;
  data: any[];
  pagination: PaginationData;
}

export default function TestPaginationPage() {
  const [currentPage, setCurrentPage] = useState(1);
  const [apiResponse, setApiResponse] = useState<ApiResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async (page: number) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/data/us_pmn?allFields=dental&page=${page}&limit=20`);
      const data = await response.json();
      
      if (data.success) {
        setApiResponse(data);
        setCurrentPage(data.pagination.page);
      } else {
        setError(data.error || 'Unknown error');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Network error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData(1);
  }, []);

  const handlePageChange = (newPage: number) => {
    if (apiResponse?.pagination) {
      const effectiveMaxPages = Math.min(
        apiResponse.pagination.totalPages, 
        apiResponse.pagination.maxPages
      );
      
      if (newPage >= 1 && newPage <= effectiveMaxPages) {
        fetchData(newPage);
      }
    }
  };

  const canGoPrev = apiResponse?.pagination?.hasPrev && !loading;
  const canGoNext = apiResponse?.pagination?.hasNext && !loading;

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">Pagination Test</h1>
      
      {loading && (
        <div className="text-blue-600 mb-4">Loading...</div>
      )}
      
      {error && (
        <div className="text-red-600 mb-4 p-4 bg-red-50 rounded">
          Error: {error}
        </div>
      )}
      
      {apiResponse && (
        <div className="space-y-6">
          <div className="bg-gray-50 p-4 rounded">
            <h2 className="font-semibold mb-2">Pagination Info:</h2>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>Current Page: {apiResponse.pagination.page}</div>
              <div>Total Pages: {apiResponse.pagination.totalPages}</div>
              <div>Total Records: {apiResponse.pagination.totalCount}</div>
              <div>Max Pages Limit: {apiResponse.pagination.maxPages}</div>
              <div>Has Next: {apiResponse.pagination.hasNext ? 'Yes' : 'No'}</div>
              <div>Has Prev: {apiResponse.pagination.hasPrev ? 'Yes' : 'No'}</div>
              <div>At Max Pages: {apiResponse.pagination.isAtMaxPages ? 'Yes' : 'No'}</div>
              <div>Records per Page: {apiResponse.pagination.limit}</div>
            </div>
          </div>
          
          <div className="bg-blue-50 p-4 rounded">
            <h2 className="font-semibold mb-2">Data Info:</h2>
            <div>Records returned: {apiResponse.data.length}</div>
            <div>
              Showing records {(apiResponse.pagination.page - 1) * apiResponse.pagination.limit + 1} - {Math.min(apiResponse.pagination.page * apiResponse.pagination.limit, apiResponse.pagination.totalCount)} of {apiResponse.pagination.totalCount}
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <Button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={!canGoPrev}
              variant="outline"
            >
              Previous
            </Button>
            
            <span className="text-sm">
              Page {currentPage} of {Math.min(apiResponse.pagination.totalPages, apiResponse.pagination.maxPages)}
              {apiResponse.pagination.totalPages > apiResponse.pagination.maxPages && (
                <span className="text-gray-500"> (of {apiResponse.pagination.totalPages} total)</span>
              )}
            </span>
            
            <Button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={!canGoNext}
              variant="outline"
            >
              Next
            </Button>
          </div>
          
          <div className="flex items-center space-x-2">
            <span className="text-sm">Jump to page:</span>
            <input
              type="number"
              min="1"
              max={Math.min(apiResponse.pagination.totalPages, apiResponse.pagination.maxPages)}
              className="w-20 px-2 py-1 border rounded"
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  const target = parseInt((e.target as HTMLInputElement).value);
                  if (!isNaN(target)) {
                    handlePageChange(target);
                  }
                }
              }}
            />
            <span className="text-xs text-gray-500">
              (1 - {Math.min(apiResponse.pagination.totalPages, apiResponse.pagination.maxPages)})
            </span>
          </div>
          
          {apiResponse.pagination.totalPages > apiResponse.pagination.maxPages && (
            <div className="text-amber-600 text-sm p-3 bg-amber-50 rounded">
              ⚠️ Showing first {apiResponse.pagination.maxPages} pages only. 
              Total dataset has {apiResponse.pagination.totalPages} pages.
            </div>
          )}
          
          <div className="space-y-2">
            <h3 className="font-semibold">Quick Test Buttons:</h3>
            <div className="flex flex-wrap gap-2">
              <Button size="sm" onClick={() => handlePageChange(1)}>Page 1</Button>
              <Button size="sm" onClick={() => handlePageChange(2)}>Page 2</Button>
              <Button size="sm" onClick={() => handlePageChange(50)}>Page 50</Button>
              <Button size="sm" onClick={() => handlePageChange(99)}>Page 99</Button>
              <Button size="sm" onClick={() => handlePageChange(100)}>Page 100</Button>
              <Button size="sm" onClick={() => handlePageChange(101)} variant="destructive">
                Page 101 (Should be limited to 100)
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
