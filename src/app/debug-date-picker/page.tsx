"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { DateRangePicker } from "@/components/ui/date-range-picker";

export default function DebugDatePicker() {
  const [isOpen, setIsOpen] = useState(false);
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [interactionCount, setInteractionCount] = useState(0);
  const [isInteracting, setIsInteracting] = useState(false);

  const handleInteractionStart = () => {
    console.log('🟢 [DEBUG] Interaction started');
    setIsInteracting(true);
  };

  const handleInteractionEnd = () => {
    console.log('🔴 [DEBUG] Interaction ended');
    setIsInteracting(false);
  };

  const handleInteractionCountChange = (count: number) => {
    console.log('📊 [DEBUG] Interaction count changed:', count);
    setInteractionCount(count);
  };

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-8">Debug DateRangePicker</h1>
      
      <div className="mb-4 p-4 bg-blue-50 rounded">
        <h3 className="font-medium mb-2">Debug Status:</h3>
        <p className="text-sm">Modal Open: {isOpen ? '✅ Yes' : '❌ No'}</p>
        <p className="text-sm">Is Interacting: {isInteracting ? '✅ Yes' : '❌ No'}</p>
        <p className="text-sm">Interaction Count: {interactionCount}</p>
        <p className="text-sm">Start Date: {startDate || 'Not set'}</p>
        <p className="text-sm">End Date: {endDate || 'Not set'}</p>
      </div>
      
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          <Button variant="outline">
            Open Advanced Search Debug
          </Button>
        </DialogTrigger>
        <DialogContent 
          className="max-w-4xl"
          onInteractOutside={(e) => {
            console.log('🚫 [DEBUG] onInteractOutside triggered, preventing default');
            e.preventDefault();
          }}
        >
          <DialogHeader>
            <DialogTitle>Advanced Search Debug</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">
                Date Range (Between Operator Test)
              </label>
              <DateRangePicker
                startDate={startDate}
                endDate={endDate}
                onStartDateChange={setStartDate}
                onEndDateChange={setEndDate}
                onInteractionStart={handleInteractionStart}
                onInteractionEnd={handleInteractionEnd}
                onInteractionCountChange={handleInteractionCountChange}
                placeholder="Select date range"
                className="w-full"
              />
            </div>
            
            <div className="mt-4 p-4 bg-yellow-50 rounded">
              <h3 className="font-medium mb-2">Test Steps:</h3>
              <ol className="list-decimal list-inside space-y-1 text-sm">
                <li>Click on the date range input to open the panel</li>
                <li><strong>Click on "Start Date" input - watch the debug status above</strong></li>
                <li><strong>Click on "End Date" input - watch the debug status above</strong></li>
                <li>Check browser console for detailed logs</li>
                <li>Modal should NOT close when clicking date inputs</li>
              </ol>
            </div>
            
            <div className="mt-4 p-4 bg-green-50 rounded">
              <h3 className="font-medium mb-2">Expected Behavior:</h3>
              <ul className="list-disc list-inside space-y-1 text-sm">
                <li>Interaction Count should increase when clicking date inputs</li>
                <li>Is Interacting should show "Yes" during date interactions</li>
                <li>Modal should remain open throughout the process</li>
                <li>Console should show detailed interaction logs</li>
              </ul>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
