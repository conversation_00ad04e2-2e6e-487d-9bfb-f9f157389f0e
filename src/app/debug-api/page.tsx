"use client";

import { useEffect, useState } from 'react';

export default function DebugAPIPage() {
  const [apiResponse, setApiResponse] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('开始调试API...');
        const response = await fetch('/api/data/us_pmn?allFields=dental&page=1&limit=5');
        
        console.log('Response status:', response.status);
        console.log('Response headers:', Object.fromEntries(response.headers.entries()));
        
        const text = await response.text();
        console.log('Raw response text:', text);
        
        try {
          const json = JSON.parse(text);
          setApiResponse(json);
          console.log('Parsed JSON:', json);
        } catch (parseError) {
          setError(`JSON解析错误: ${parseError}`);
          setApiResponse({ rawText: text });
        }
        
      } catch (err) {
        const errorMsg = err instanceof Error ? err.message : '网络错误';
        setError(errorMsg);
        console.error('请求失败:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">API调试页面</h1>
        <div className="text-blue-600">正在调试API...</div>
      </div>
    );
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">API调试页面</h1>
      
      {error && (
        <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
          <strong>错误:</strong> {error}
        </div>
      )}
      
      {apiResponse && (
        <div className="space-y-4">
          <div className="p-4 bg-gray-100 rounded">
            <h3 className="font-bold mb-2">API响应结构:</h3>
            <div className="text-sm">
              <p><strong>success:</strong> {String(apiResponse.success)}</p>
              <p><strong>data length:</strong> {apiResponse.data?.length || 'N/A'}</p>
              <p><strong>error:</strong> {apiResponse.error || 'None'}</p>
              <p><strong>pagination:</strong> {apiResponse.pagination ? 'Present' : 'Missing'}</p>
            </div>
          </div>
          
          <div className="p-4 bg-gray-50 rounded">
            <h3 className="font-bold mb-2">完整响应:</h3>
            <pre className="text-xs overflow-auto max-h-96 bg-white p-2 border rounded">
              {JSON.stringify(apiResponse, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
}
