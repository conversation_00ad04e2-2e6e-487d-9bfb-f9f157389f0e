"use client";

import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export default function TestClearButtonPage() {
  const [inputValue, setInputValue] = useState("");
  const [textareaValue, setTextareaValue] = useState("");
  const [emailValue, setEmailValue] = useState("");
  const [passwordValue, setPasswordValue] = useState("");
  const [disabledValue, setDisabledValue] = useState("This is disabled");
  const [readonlyValue, setReadonlyValue] = useState("This is readonly");

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Clear Button Test Page</h1>
        <p className="text-gray-600">
          Test the clear button functionality on various input types and scenarios.
        </p>
      </div>

      <div className="grid gap-6">
        {/* Basic Input Tests */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Input Fields</CardTitle>
            <CardDescription>
              Test clear buttons on different input types
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="basic-input">Basic Text Input</Label>
              <Input
                id="basic-input"
                type="text"
                placeholder="Type something to see the clear button..."
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                className="mt-1"
              />
              <p className="text-sm text-gray-500 mt-1">
                Value: "{inputValue}"
              </p>
            </div>

            <div>
              <Label htmlFor="email-input">Email Input</Label>
              <Input
                id="email-input"
                type="email"
                placeholder="Enter your email..."
                value={emailValue}
                onChange={(e) => setEmailValue(e.target.value)}
                className="mt-1"
              />
              <p className="text-sm text-gray-500 mt-1">
                Value: "{emailValue}"
              </p>
            </div>

            <div>
              <Label htmlFor="password-input">Password Input</Label>
              <Input
                id="password-input"
                type="password"
                placeholder="Enter your password..."
                value={passwordValue}
                onChange={(e) => setPasswordValue(e.target.value)}
                className="mt-1"
              />
              <p className="text-sm text-gray-500 mt-1">
                Value: "{passwordValue}"
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Textarea Test */}
        <Card>
          <CardHeader>
            <CardTitle>Textarea Field</CardTitle>
            <CardDescription>
              Test clear button on textarea component
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div>
              <Label htmlFor="textarea-input">Message</Label>
              <Textarea
                id="textarea-input"
                placeholder="Type a longer message here..."
                value={textareaValue}
                onChange={(e) => setTextareaValue(e.target.value)}
                className="mt-1"
                rows={4}
              />
              <p className="text-sm text-gray-500 mt-1">
                Value: "{textareaValue}"
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Special Cases */}
        <Card>
          <CardHeader>
            <CardTitle>Special Cases</CardTitle>
            <CardDescription>
              Test clear button behavior in different states
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="disabled-input">Disabled Input (no clear button)</Label>
              <Input
                id="disabled-input"
                type="text"
                value={disabledValue}
                onChange={(e) => setDisabledValue(e.target.value)}
                disabled
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="readonly-input">Readonly Input (no clear button)</Label>
              <Input
                id="readonly-input"
                type="text"
                value={readonlyValue}
                onChange={(e) => setReadonlyValue(e.target.value)}
                readOnly
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="no-clear-input">Input with Clear Button Disabled</Label>
              <Input
                id="no-clear-input"
                type="text"
                placeholder="This input won't show a clear button..."
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                showClearButton={false}
                className="mt-1"
              />
            </div>
          </CardContent>
        </Card>

        {/* Custom Clear Handler */}
        <Card>
          <CardHeader>
            <CardTitle>Custom Clear Handler</CardTitle>
            <CardDescription>
              Test input with custom clear functionality
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div>
              <Label htmlFor="custom-clear-input">Input with Custom Clear</Label>
              <Input
                id="custom-clear-input"
                type="text"
                placeholder="Type something and clear it..."
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onClear={() => {
                  setInputValue("");
                  alert("Custom clear handler called!");
                }}
                className="mt-1"
              />
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <Card>
          <CardHeader>
            <CardTitle>Test Actions</CardTitle>
          </CardHeader>
          <CardContent className="flex gap-2">
            <Button 
              onClick={() => {
                setInputValue("Sample text");
                setTextareaValue("Sample textarea content");
                setEmailValue("<EMAIL>");
                setPasswordValue("password123");
              }}
            >
              Fill All Fields
            </Button>
            <Button 
              variant="outline"
              onClick={() => {
                setInputValue("");
                setTextareaValue("");
                setEmailValue("");
                setPasswordValue("");
              }}
            >
              Clear All Fields
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
