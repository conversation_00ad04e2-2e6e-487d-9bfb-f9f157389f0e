"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import Navigation from "@/components/Navigation";

export default function DataPage() {
  const router = useRouter();
  useEffect(() => {
    // 重定向到首页
    router.push("/");
  }, [router]);

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      <div className="max-w-4xl mx-auto pt-16 px-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">正在跳转...</h1>
          <p className="text-gray-600">如果页面没有自动跳转，请点击返回首页</p>
        </div>
      </div>
    </div>
  );
}
