'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

export default function TestUSClassSearchPage() {
  const [searchQuery, setSearchQuery] = useState('a');
  const [results, setResults] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const testStandardAPI = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/data/us_class?allFields=${encodeURIComponent(searchQuery)}&page=1&limit=5`);
      const data = await response.json();
      setResults({ type: 'Standard API', data });
    } catch (error) {
      setResults({ type: 'Standard API', error: error instanceof Error ? error.message : String(error) });
    } finally {
      setLoading(false);
    }
  };

  const testESAPI = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/medical-search?q=${encodeURIComponent(searchQuery)}&database=us_class&page=1&limit=5`);
      const data = await response.json();
      setResults({ type: 'ES API', data });
    } catch (error) {
      setResults({ type: 'ES API', error: error instanceof Error ? error.message : String(error) });
    } finally {
      setLoading(false);
    }
  };

  const testFrontendLogic = async () => {
    setLoading(true);
    try {
      // 模拟前端的搜索逻辑
      console.log('Testing frontend search logic for allFields:', searchQuery);

      // 首先尝试 ES 搜索
      const esResponse = await fetch(
        `/api/medical-search?q=${encodeURIComponent(searchQuery)}&database=us_class&page=1&limit=5`
      );

      if (esResponse.ok) {
        const esData = await esResponse.json();

        if (esData.success && esData.data) {
          console.log('ES search successful, results:', esData.data.total);

          // 转换 ES 结果为标准格式 - 这里可能有问题
          const transformedData = esData.data.hits.map((hit: any) => ({
            id: hit._source.id,
            ...hit._source
          }));

          setResults({ 
            type: 'Frontend Logic (ES)', 
            data: { 
              success: true, 
              data: transformedData,
              originalESData: esData.data 
            } 
          });
          return;
        }
      }

      // 回退到标准搜索
      console.log('Falling back to standard search');
      const standardResponse = await fetch(`/api/data/us_class?allFields=${encodeURIComponent(searchQuery)}&page=1&limit=5`);
      const standardData = await standardResponse.json();
      
      setResults({ 
        type: 'Frontend Logic (Standard)', 
        data: standardData 
      });

    } catch (error) {
      setResults({ type: 'Frontend Logic', error: error instanceof Error ? error.message : String(error) });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">US Class Search Test</h1>
      
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Search Input</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2 mb-4">
            <Input
              placeholder="Enter search query"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="flex-1"
            />
          </div>
          <div className="flex gap-2">
            <Button onClick={testStandardAPI} disabled={loading}>
              Test Standard API
            </Button>
            <Button onClick={testESAPI} disabled={loading} variant="outline">
              Test ES API
            </Button>
            <Button onClick={testFrontendLogic} disabled={loading} variant="secondary">
              Test Frontend Logic
            </Button>
          </div>
        </CardContent>
      </Card>

      {results && (
        <Card>
          <CardHeader>
            <CardTitle>Results: {results.type}</CardTitle>
          </CardHeader>
          <CardContent>
            {results.error ? (
              <div className="text-red-600">
                <strong>Error:</strong> {results.error}
              </div>
            ) : (
              <div>
                <h3 className="font-semibold mb-2">Response Data:</h3>
                <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96">
                  {JSON.stringify(results.data, null, 2)}
                </pre>
                
                {results.data?.data && (
                  <div className="mt-4">
                    <h3 className="font-semibold mb-2">First Record Analysis:</h3>
                    <div className="bg-blue-50 p-3 rounded">
                      {results.data.data[0] ? (
                        <div>
                          <p><strong>ID:</strong> {results.data.data[0].id}</p>
                          <p><strong>devicename:</strong> {results.data.data[0].devicename || 'undefined'}</p>
                          <p><strong>productcode:</strong> {results.data.data[0].productcode || 'undefined'}</p>
                          <p><strong>table_code:</strong> {results.data.data[0].table_code || 'undefined'}</p>
                          <p><strong>product_combined:</strong> {results.data.data[0].product_combined || 'undefined'}</p>
                        </div>
                      ) : (
                        <p>No data found</p>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
