"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useAuth } from "@/lib/auth";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Loader2, UserPlus, Check, Crown, Building } from "lucide-react";
import { MEMBERSHIP_BENEFITS } from "@/lib/permissions";

export default function RegisterPage() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [name, setName] = useState("");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);

  const { register } = useAuth();
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setLoading(true);

    // 基本验证
    if (!email || !password || !confirmPassword || !name) {
      setError("Please fill in all fields");
      setLoading(false);
      return;
    }

    if (password !== confirmPassword) {
      setError("Passwords do not match");
      setLoading(false);
      return;
    }

    if (password.length < 6) {
      setError("Password must be at least 6 characters");
      setLoading(false);
      return;
    }

    const result = await register(email, password, name);

    if (result.success) {
      router.push("/"); // 注册成功后跳转到首页
    } else {
      setError(result.error || "Registration failed");
    }

    setLoading(false);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8">
          <Link href="/" className="inline-block">
            <div className="text-blue-600 font-bold text-3xl">DataQuery</div>
          </Link>
          <h2 className="mt-6 text-3xl font-bold text-gray-900">
            Create New Account
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Already have an account?{' '}
            <Link href="/login" className="font-medium text-blue-600 hover:text-blue-500">
              Sign in now
            </Link>
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Registration Form */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <UserPlus className="h-5 w-5 mr-2" />
                  Create Account
                </CardTitle>
                <CardDescription>
                  After registration, you will get free user privileges to access basic databases
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  {error && (
                    <Alert variant="destructive">
                      <AlertDescription>{error}</AlertDescription>
                    </Alert>
                  )}

                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="name">Name</Label>
                      <Input
                        id="name" type="text" value={name}
                        onChange={(e) => setName(e.target.value)}
                        placeholder="Enter your name" className="mt-1" disabled={loading}
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="email">Email Address</Label>
                      <Input
                        id="email" type="email" value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder="Enter your email" className="mt-1" disabled={loading}
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="password">Password</Label>
                      <Input
                        id="password" type="password" value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        placeholder="Enter password (at least 6 characters)" className="mt-1" disabled={loading}
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="confirmPassword">Confirm Password</Label>
                      <Input
                        id="confirmPassword" type="password" value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        placeholder="Enter password again" className="mt-1" disabled={loading}
                        required
                      />
                    </div>
                  </div>

                  <Button
                    type="submit" className="w-full" disabled={loading}
                  >
                    {loading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Registering...
                      </>
                    ) : (
                      <>
                        <UserPlus className="mr-2 h-4 w-4" />
                        Free Registration
                      </>
                    )}
                  </Button>

                  <p className="text-xs text-gray-500 text-center">
                    By registering, you agree to our{' '}
                    <Link href="/terms" className="text-blue-600 hover:text-blue-500">
                      Terms of Service
                    </Link>
                    {' '}and{' '}
                    <Link href="/privacy" className="text-blue-600 hover:text-blue-500">
                      Privacy Policy
                    </Link>
                  </p>
                </form>
              </CardContent>
            </Card>
          </div>

          {/* Membership Benefits */}
          <div className="space-y-6">
            <h3 className="text-xl font-bold text-gray-900">Membership Benefits Comparison</h3>

            {Object.entries(MEMBERSHIP_BENEFITS).map(([type, benefits]) => (
              <Card key={type} className={`relative ${
                type ==="premium" ? 'border-blue-300 bg-blue-50' :
                type ==="enterprise" ? 'border-purple-300 bg-purple-50' : ''
              }`}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center">
                      {type ==="free" && <span className="text-green-600">Free User</span>}
                      {type ==="premium" && (
                        <>
                          <Crown className="h-5 w-5 mr-2 text-blue-600" />
                          <span className="text-blue-600">Premium Member</span>
                        </>
                      )}
                      {type ==="enterprise" && (
                        <>
                          <Building className="h-5 w-5 mr-2 text-purple-600" />
                          <span className="text-purple-600">Enterprise</span>
                        </>
                      )}
                    </CardTitle>
                    {type ==="premium" && (
                      <Badge className="bg-blue-600">Recommended</Badge>
                    )}
                  </div>
                  <CardDescription>{benefits.description}</CardDescription>
                  {'price' in benefits && benefits.price && (
                    <div className="text-sm">
                      <span className="text-2xl font-bold">¥{benefits.price.monthly}</span>
                      <span className="text-gray-500">/月</span>
                      <span className="ml-2 text-gray-500">
                        Annual ¥{benefits.price.yearly}
                      </span>
                    </div>
                  )}
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {benefits.features.map((feature) => (
                      <li key={feature} className="flex items-start">
                        <Check className="h-4 w-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                        <span className="text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  {type !=="free" && (
                    <Button
                      className={`w-full mt-4 ${
                        type ==="premium" ? 'bg-blue-600 hover:bg-blue-700' :
                        'bg-purple-600 hover:bg-purple-700'
                      }`}
                      asChild
                    >
                      <Link href={`/upgrade?plan=${type}`}>
                        Choose {benefits.name}
                      </Link>
                    </Button>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
