"use client";

import { useEffect, useState } from 'react';

export default function TestUSPMNPage() {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('开始获取US PMN数据...');
        const response = await fetch('/api/data/us_pmn?allFields=dental&page=1&limit=10');
        const result = await response.json();
        
        console.log('API响应:', result);
        
        if (result.success) {
          setData(result.data);
          console.log('数据加载成功:', result.data.length, '条记录');
        } else {
          setError(result.error || '未知错误');
          console.error('API返回错误:', result.error);
        }
      } catch (err) {
        const errorMsg = err instanceof Error ? err.message : '网络错误';
        setError(errorMsg);
        console.error('请求失败:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">US PMN 测试页面</h1>
        <div className="text-blue-600">正在加载数据...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">US PMN 测试页面</h1>
        <div className="text-red-600">错误: {error}</div>
      </div>
    );
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">US PMN 测试页面</h1>
      <div className="mb-4">
        <p className="text-green-600">成功加载 {data.length} 条记录</p>
      </div>
      
      {data.length > 0 && (
        <div className="overflow-x-auto">
          <table className="min-w-full border border-gray-300">
            <thead className="bg-gray-50">
              <tr>
                {Object.keys(data[0]).slice(0, 5).map((key) => (
                  <th key={key} className="border border-gray-300 px-4 py-2 text-left">
                    {key}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {data.slice(0, 5).map((row, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  {Object.keys(data[0]).slice(0, 5).map((key) => (
                    <td key={key} className="border border-gray-300 px-4 py-2">
                      {String(row[key] || '').substring(0, 100)}
                      {String(row[key] || '').length > 100 ? '...' : ''}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}
