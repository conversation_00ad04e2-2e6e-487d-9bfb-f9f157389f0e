"use client";

import { useState } from 'react';
import Navigation from '@/components/Navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function TestSearchFixPage() {
  const [searchQuery, setSearchQuery] = useState('medtronic');
  const [globalResults, setGlobalResults] = useState<any>(null);
  const [medicalResults, setMedicalResults] = useState<any>(null);
  const [loading, setLoading] = useState<Record<string, boolean>>({});

  // 测试全局搜索
  const testGlobalSearch = async () => {
    if (!searchQuery.trim()) return;
    
    setLoading(prev => ({ ...prev, global: true }));
    try {
      const response = await fetch(`/api/global-search?q=${encodeURIComponent(searchQuery)}`);
      const data = await response.json();
      setGlobalResults(data);
      console.log('Global search results:', data);
    } catch (error) {
      console.error('Global search failed:', error);
      setGlobalResults({ success: false, error: 'Network error' });
    } finally {
      setLoading(prev => ({ ...prev, global: false }));
    }
  };

  // 测试医疗设备搜索
  const testMedicalSearch = async (database: string) => {
    if (!searchQuery.trim()) return;
    
    setLoading(prev => ({ ...prev, medical: true }));
    try {
      const response = await fetch(
        `/api/medical-search?q=${encodeURIComponent(searchQuery)}&database=${database}&page=1&limit=5`
      );
      const data = await response.json();
      setMedicalResults(data);
      console.log('Medical search results:', data);
    } catch (error) {
      console.error('Medical search failed:', error);
      setMedicalResults({ success: false, error: 'Network error' });
    } finally {
      setLoading(prev => ({ ...prev, medical: false }));
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      <main className="pt-16 pb-24">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Search Fix Test
            </h1>
            <p className="text-gray-600">
              Test the fixed Elasticsearch search functionality
            </p>
          </div>

          {/* 搜索输入 */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Search Test</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex gap-2 mb-4">
                <Input
                  placeholder="Enter search query"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="flex-1"
                />
                <Button onClick={testGlobalSearch} disabled={loading.global}>
                  {loading.global ? 'Searching...' : 'Test Global Search'}
                </Button>
                <Button 
                  onClick={() => testMedicalSearch('us_fdaclass')} 
                  disabled={loading.medical}
                  variant="outline"
                >
                  {loading.medical ? 'Searching...' : 'Test us_fdaclass'}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 全局搜索结果 */}
          {globalResults && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>Global Search Results</CardTitle>
              </CardHeader>
              <CardContent>
                {globalResults.success ? (
                  <div className="space-y-2">
                    {globalResults.data.map((result: any) => (
                      <div key={result.database} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                        <span className="font-medium">{result.database}</span>
                        <span className="text-blue-600 font-bold">{result.count} results</span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-red-600">
                    Error: {globalResults.error}
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* 医疗设备搜索结果 */}
          {medicalResults && (
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>Medical Search Results (us_fdaclass)</CardTitle>
              </CardHeader>
              <CardContent>
                {medicalResults.success ? (
                  <div>
                    <div className="mb-4">
                      <strong>Total: {medicalResults.data.total} results</strong>
                    </div>
                    <div className="space-y-4">
                      {medicalResults.data.hits.map((hit: any, index: number) => (
                        <div key={index} className="p-4 bg-gray-50 rounded">
                          <div className="grid grid-cols-2 gap-2 text-sm">
                            <div><strong>ID:</strong> {hit._source.id}</div>
                            <div><strong>Table Code:</strong> {hit._source.table_code}</div>
                            <div><strong>Registration No:</strong> {hit._source.registration_no || 'N/A'}</div>
                            <div><strong>Product:</strong> {hit._source.product_combined || 'N/A'}</div>
                            <div className="col-span-2"><strong>Company:</strong> {hit._source.company_combined || 'N/A'}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="text-red-600">
                    Error: {medicalResults.error}
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* 调试信息 */}
          <Card>
            <CardHeader>
              <CardTitle>Debug Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Expected Behavior:</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Global search should return accurate counts for each database</li>
                    <li>• Medical search should show actual matching documents</li>
                    <li>• Search for "medtronic" should only match documents containing that exact term</li>
                    <li>• Results should be consistent between global and detailed search</li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-medium mb-2">Query Strategy:</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Phrase matching (highest priority)</li>
                    <li>• Cross-fields matching with AND operator</li>
                    <li>• Wildcard matching for partial matches</li>
                    <li>• Filtered by table_code for database-specific searches</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
