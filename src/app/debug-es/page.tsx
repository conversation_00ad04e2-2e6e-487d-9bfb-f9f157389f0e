"use client";

import { useState, useEffect } from 'react';
import Navigation from '@/components/Navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function DebugESPage() {
  const [indexInfo, setIndexInfo] = useState<any>(null);
  const [sampleData, setSampleData] = useState<any>(null);
  const [searchResults, setSearchResults] = useState<any>(null);
  const [countData, setCountData] = useState<any>(null);
  const [mapping, setMapping] = useState<any>(null);
  const [loading, setLoading] = useState<Record<string, boolean>>({});
  const [searchQuery, setSearchQuery] = useState('medtronic');
  const [selectedDatabase, setSelectedDatabase] = useState('');

  // 获取索引信息
  const fetchIndexInfo = async () => {
    setLoading(prev => ({ ...prev, info: true }));
    try {
      const response = await fetch('/api/debug-es?action=info');
      const data = await response.json();
      setIndexInfo(data);
    } catch (error) {
      console.error('Failed to fetch index info:', error);
    } finally {
      setLoading(prev => ({ ...prev, info: false }));
    }
  };

  // 获取样本数据
  const fetchSampleData = async (database?: string) => {
    setLoading(prev => ({ ...prev, sample: true }));
    try {
      const url = database 
        ? `/api/debug-es?action=sample&database=${database}`
        : '/api/debug-es?action=sample';
      const response = await fetch(url);
      const data = await response.json();
      setSampleData(data);
    } catch (error) {
      console.error('Failed to fetch sample data:', error);
    } finally {
      setLoading(prev => ({ ...prev, sample: false }));
    }
  };

  // 调试搜索
  const debugSearch = async () => {
    if (!searchQuery.trim()) return;
    
    setLoading(prev => ({ ...prev, search: true }));
    try {
      const url = selectedDatabase
        ? `/api/debug-es?action=search&query=${encodeURIComponent(searchQuery)}&database=${selectedDatabase}`
        : `/api/debug-es?action=search&query=${encodeURIComponent(searchQuery)}`;
      const response = await fetch(url);
      const data = await response.json();
      setSearchResults(data);
    } catch (error) {
      console.error('Failed to debug search:', error);
    } finally {
      setLoading(prev => ({ ...prev, search: false }));
    }
  };

  // 获取数据库统计
  const fetchCountData = async () => {
    setLoading(prev => ({ ...prev, count: true }));
    try {
      const response = await fetch('/api/debug-es?action=count');
      const data = await response.json();
      setCountData(data);
    } catch (error) {
      console.error('Failed to fetch count data:', error);
    } finally {
      setLoading(prev => ({ ...prev, count: false }));
    }
  };

  // 获取索引映射
  const fetchMapping = async () => {
    setLoading(prev => ({ ...prev, mapping: true }));
    try {
      const response = await fetch('/api/debug-es?action=mapping');
      const data = await response.json();
      setMapping(data);
    } catch (error) {
      console.error('Failed to fetch mapping:', error);
    } finally {
      setLoading(prev => ({ ...prev, mapping: false }));
    }
  };

  // 页面加载时获取基本信息
  useEffect(() => {
    fetchIndexInfo();
    fetchCountData();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      <main className="pt-16 pb-24">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Elasticsearch Debug Tool
            </h1>
            <p className="text-gray-600">
              Debug and inspect the medical_index Elasticsearch index
            </p>
          </div>

          <Tabs defaultValue="info" className="space-y-6">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="info">Index Info</TabsTrigger>
              <TabsTrigger value="sample">Sample Data</TabsTrigger>
              <TabsTrigger value="search">Debug Search</TabsTrigger>
              <TabsTrigger value="count">Count by DB</TabsTrigger>
              <TabsTrigger value="mapping">Mapping</TabsTrigger>
            </TabsList>

            {/* 索引信息 */}
            <TabsContent value="info">
              <Card>
                <CardHeader>
                  <CardTitle>Index Information</CardTitle>
                  <Button onClick={fetchIndexInfo} disabled={loading.info}>
                    {loading.info ? 'Loading...' : 'Refresh'}
                  </Button>
                </CardHeader>
                <CardContent>
                  <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
                    {JSON.stringify(indexInfo, null, 2)}
                  </pre>
                </CardContent>
              </Card>
            </TabsContent>

            {/* 样本数据 */}
            <TabsContent value="sample">
              <Card>
                <CardHeader>
                  <CardTitle>Sample Data</CardTitle>
                  <div className="flex gap-2">
                    <Input
                      placeholder="Database code (optional)"
                      value={selectedDatabase}
                      onChange={(e) => setSelectedDatabase(e.target.value)}
                      className="max-w-xs"
                    />
                    <Button onClick={() => fetchSampleData(selectedDatabase)} disabled={loading.sample}>
                      {loading.sample ? 'Loading...' : 'Fetch Sample'}
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96">
                    {JSON.stringify(sampleData, null, 2)}
                  </pre>
                </CardContent>
              </Card>
            </TabsContent>

            {/* 调试搜索 */}
            <TabsContent value="search">
              <Card>
                <CardHeader>
                  <CardTitle>Debug Search</CardTitle>
                  <div className="flex gap-2">
                    <Input
                      placeholder="Search query"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && debugSearch()}
                    />
                    <Input
                      placeholder="Database code (optional)"
                      value={selectedDatabase}
                      onChange={(e) => setSelectedDatabase(e.target.value)}
                      className="max-w-xs"
                    />
                    <Button onClick={debugSearch} disabled={loading.search || !searchQuery.trim()}>
                      {loading.search ? 'Searching...' : 'Debug Search'}
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96">
                    {JSON.stringify(searchResults, null, 2)}
                  </pre>
                </CardContent>
              </Card>
            </TabsContent>

            {/* 数据库统计 */}
            <TabsContent value="count">
              <Card>
                <CardHeader>
                  <CardTitle>Count by Database</CardTitle>
                  <Button onClick={fetchCountData} disabled={loading.count}>
                    {loading.count ? 'Loading...' : 'Refresh'}
                  </Button>
                </CardHeader>
                <CardContent>
                  <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
                    {JSON.stringify(countData, null, 2)}
                  </pre>
                </CardContent>
              </Card>
            </TabsContent>

            {/* 索引映射 */}
            <TabsContent value="mapping">
              <Card>
                <CardHeader>
                  <CardTitle>Index Mapping</CardTitle>
                  <Button onClick={fetchMapping} disabled={loading.mapping}>
                    {loading.mapping ? 'Loading...' : 'Fetch Mapping'}
                  </Button>
                </CardHeader>
                <CardContent>
                  <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96">
                    {JSON.stringify(mapping, null, 2)}
                  </pre>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  );
}
