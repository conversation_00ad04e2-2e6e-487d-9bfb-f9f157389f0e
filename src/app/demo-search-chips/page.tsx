import React from 'react';
import GlobalSearchWithChips from '@/components/GlobalSearchWithChips';

export default function DemoSearchChipsPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-12">
        {/* Page Title */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Medical Device Database Search
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Use intelligent search tags to easily manage and reuse your search conditions
          </p>
        </div>

        {/* Search Component */}
        <GlobalSearchWithChips className="max-w-4xl mx-auto" />

        {/* Feature Introduction */}
        <div className="mt-16 max-w-4xl mx-auto">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            Search Tags Features
          </h2>
          
          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Smart Search History</h3>
              <p className="text-gray-600">
                Automatically save search history, support quick re-search, and improve search efficiency
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Removable Tags</h3>
              <p className="text-gray-600">
                Click the × button on tags to remove individual search conditions, simple and intuitive operation
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">User Experience Optimization</h3>
              <p className="text-gray-600">
                Compact mode, maximum display limit, clear all and other features enhance user experience
              </p>
            </div>
          </div>
        </div>

        {/* Use Cases */}
        <div className="mt-16 max-w-4xl mx-auto">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            Use Cases
          </h2>

          <div className="bg-white rounded-lg shadow-md p-8">
            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Global Comprehensive Search</h3>
                <ul className="space-y-2 text-gray-600">
                  <li>• Search history management for homepage search box</li>
                  <li>• Cross-database search condition display</li>
                  <li>• Quick repeat search for common keywords</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Database Page Search</h3>
                <ul className="space-y-2 text-gray-600">
                  <li>• Display all currently applied filter conditions</li>
                  <li>• Support removing individual filter conditions</li>
                  <li>• Visual display of advanced search conditions</li>
                  <li>• Tag-based display of sorting conditions</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Technical Implementation */}
        <div className="mt-16 max-w-4xl mx-auto">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            Technical Implementation
          </h2>

          <div className="bg-gray-900 rounded-lg p-6 text-white">
            <h3 className="text-lg font-semibold mb-4">Core Components</h3>
            <div className="grid md:grid-cols-2 gap-6 text-sm">
              <div>
                <h4 className="font-medium text-blue-300 mb-2">SearchChips Component</h4>
                <ul className="space-y-1 text-gray-300">
                  <li>• Configurable tag display styles</li>
                  <li>• Support for different types of search conditions</li>
                  <li>• Compact mode and normal mode</li>
                  <li>• Maximum display quantity limit</li>
                </ul>
              </div>

              <div>
                <h4 className="font-medium text-green-300 mb-2">useSearchChips Hook</h4>
                <ul className="space-y-1 text-gray-300">
                  <li>• Unified search condition management</li>
                  <li>• Automatic conversion of different condition types</li>
                  <li>• Provides remove and clear operations</li>
                  <li>• Supports custom field configuration</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
