import { NextRequest, NextResponse } from 'next/server';
import { getSearchAnalytics, getPopularSearchQueries, getUserSearchHistory } from '@/lib/search-analytics';
import { getSession } from '@/lib/session';

export async function GET(__request: NextRequest) {
  try {
    const { searchParams } = new URL(__request.url);
    const action = searchParams.get('action') || 'analytics';
    const database = searchParams.get('database') || undefined;
    const timeRange = searchParams.get('timeRange') || '7d';
    const limit = parseInt(searchParams.get('limit') || '100');

    // 获取用户会话
    const session = await getSession();
    const userId = (typeof session?.userId === 'string' ? session.userId : undefined);

    switch (action) {
      case 'analytics':
        // 获取搜索分析数据
        const analytics = await getSearchAnalytics({
          database,
          userId: searchParams.get('userId') || undefined,
          timeRange,
          limit,
        });

        return NextResponse.json({
          success: true,
          data: analytics,
        });

      case 'popular':
        // 获取热门搜索词
        const popularQueries = await getPopularSearchQueries(database, limit);

        return NextResponse.json({
          success: true,
          data: popularQueries,
        });

      case 'history':
        // 获取用户搜索历史（需要登录）
        if (!userId) {
          return NextResponse.json(
            { success: false, error: 'Authentication required' },
            { status: 401 }
          );
        }

        const history = await getUserSearchHistory(userId, limit);

        return NextResponse.json({
          success: true,
          data: history,
        });

      case 'summary':
        // 获取搜索摘要统计
        const summary = await getSearchSummary(database, timeRange);

        return NextResponse.json({
          success: true,
          data: summary,
        });

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (__error) {
    console.error('Search analytics API error:', __error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// 获取搜索摘要统计
async function getSearchSummary(database?: string, timeRange = '7d') {
  try {
    const analytics = await getSearchAnalytics({
      database,
      timeRange,
      limit: 1000, // 获取更多数据用于统计
    });

    const { searches, statistics } = analytics;

    // 计算平均搜索时间
    const searchTimes = searches
      .filter(s => s.searchTime && s.searchTime > 0)
      .map(s => s.searchTime!);
    
    const avgSearchTime = searchTimes.length > 0 
      ? searchTimes.reduce((a, b) => a + b, 0) / searchTimes.length 
      : 0;

    // 计算平均结果数量
    const resultCounts = searches
      .filter(s => s.resultsCount !== null && s.resultsCount !== undefined)
      .map(s => s.resultsCount!);
    
    const avgResultsCount = resultCounts.length > 0
      ? resultCounts.reduce((a, b) => a + b, 0) / resultCounts.length
      : 0;

    // 计算搜索成功率（有结果的搜索比例）
    const successfulSearches = searches.filter(s => s.resultsCount && s.resultsCount > 0).length;
    const successRate = searches.length > 0 ? (successfulSearches / searches.length) * 100 : 0;

    // 按小时统计搜索量
    const hourlyStats = getHourlySearchStats(searches);

    // 最活跃的搜索时段
    const mostActiveHour = hourlyStats.reduce((max, current) => 
      current.count > max.count ? current : max, 
      { hour: 0, count: 0 }
    );

    return {
      overview: {
        totalSearches: searches.length,
        uniqueQueries: new Set(searches.map(s => s.searchQuery).filter(Boolean)).size,
        avgSearchTime: Math.round(avgSearchTime),
        avgResultsCount: Math.round(avgResultsCount),
        successRate: Math.round(successRate * 100) / 100,
        mostActiveHour: mostActiveHour.hour,
      },
      statistics,
      hourlyStats,
      timeRange,
      database,
    };
  } catch (__error) {
    console.error('Failed to get search summary:', __error);
    return {
      overview: {
        totalSearches: 0,
        uniqueQueries: 0,
        avgSearchTime: 0,
        avgResultsCount: 0,
        successRate: 0,
        mostActiveHour: 0,
      },
      statistics: {
        topSearchQueries: [],
        searchTypeStats: [],
        databaseStats: [],
        searchFieldsUsage: [],
      },
      hourlyStats: [],
      timeRange,
      database,
    };
  }
}

// 按小时统计搜索量
function getHourlySearchStats(searches: Record<string, unknown>[]) {
  const hourlyCount: Record<number, number> = {};
  
  // 初始化24小时
  for (let i = 0; i < 24; i++) {
    hourlyCount[i] = 0;
  }

  // 统计每小时的搜索量
  searches.forEach(search => {
    const hour = new Date(search.createdAt as string | Date).getHours();
    hourlyCount[hour]++;
  });

  return Object.entries(hourlyCount).map(([hour, count]) => ({
    hour: parseInt(hour),
    count,
  }));
}

// 支持POST请求用于记录搜索分析（如果需要从客户端直接调用）
export async function POST(__request: NextRequest) {
  try {
    const body = await __request.json();
    const { recordSearchAnalytics } = await import('@/lib/search-analytics');

    // 获取客户端IP
    const forwarded = __request.headers.get('x-forwarded-for');
    const ip = forwarded ? forwarded.split(',')[0].trim() : '127.0.0.1';

    await recordSearchAnalytics({
      ...body,
      ip,
    });

    return NextResponse.json({
      success: true,
      message: 'Search analytics recorded',
    });
  } catch (__error) {
    console.error('Failed to record search analytics:', __error);
    return NextResponse.json(
      { success: false, error: 'Failed to record search analytics' },
      { status: 500 }
    );
  }
}
