import { type NextRequest, NextResponse } from 'next/server';
import { getDynamicModel, isPrismaModel, validateDatabaseCode } from '@/lib/dynamicTableMapping';
import { getDatabaseAccessLevel } from '@/lib/permissions';
import { checkPermissions } from '@/lib/server/permissions';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ database: string; id: string }> }
) {
  try {
    const { database, id } = await params;

    // 输入验证
    if (!id || typeof id !== 'string') {
      return NextResponse.json(
        { success: false, error: 'Invalid item ID' },
        { status: 400 }
      );
    }

    // 使用新的统一验证函数
    const validation = await validateDatabaseCode(database);
    if (!validation.isValid) {
      return NextResponse.json(
        { success: false, error: validation.error },
        { status: validation.status || 400 }
      );
    }

    // 权限检查
    const requiredLevel = await getDatabaseAccessLevel(database) as "free" | "premium" | "enterprise";
    const hasAccess = await checkPermissions(requiredLevel);
    if (!hasAccess) {
      return NextResponse.json(
        { success: false, error: 'Permission denied' },
        { status: 403 }
      );
    }

    // 使用动态模型获取
    const model = await getDynamicModel(database);
    if (!isPrismaModel(model)) {
      return NextResponse.json(
        { success: false, error: 'Model not found or invalid' },
        { status: 500 }
      );
    }
    // 查询数据
    const item = await (model as any).findUnique({
      where: { id: id },
    });

    if (!item) {
      return NextResponse.json(
        { success: false, error: 'Item not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: item,
    });

  } catch (__error) {
    console.error('API Error:', __error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}