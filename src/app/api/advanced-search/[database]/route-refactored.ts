import { type NextRequest, NextResponse } from 'next/server';
import { getDynamicModel, isPrismaModel, validateDatabaseCode, getDatabaseAccessLevel } from '@/lib/dynamicTableMapping';
import { checkPermissions } from '@/lib/server/permissions';
import { getDatabaseConfig } from '@/lib/configCache';
import type { DatabaseConfig } from '@/lib/configCache';
import { validatePaginationParams, buildPaginationResponse } from '@/lib/globalPagination';

export const dynamic = 'force-dynamic';

// 高级搜索请求接口
interface AdvancedSearchRequest {
  conditions: SearchCondition[];
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

interface SearchCondition {
  field: string;
  operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'greaterThan' | 'lessThan' | 'between' | 'in';
  value: string | string[] | number | number[] | Date | Date[];
  logic?: 'AND' | 'OR';
}

// 构建高级搜索的where条件 - 重构版本
function buildAdvancedWhere(conditions: SearchCondition[], config: DatabaseConfig): Record<string, unknown> {
  if (!conditions || conditions.length === 0) {
    return {};
  }

  const searchableFields = config.fields.filter(f => f.isSearchable).map(f => f.fieldName);
  const filterableFields = config.fields.filter(f => f.isFilterable).map(f => f.fieldName);
  const allowedFields = [...searchableFields, ...filterableFields];

  const whereConditions: Record<string, unknown>[] = [];

  conditions.forEach(condition => {
    // 验证字段是否允许搜索/过滤
    if (!allowedFields.includes(condition.field)) {
      return;
    }

    let fieldCondition: Record<string, unknown> = {};

    switch (condition.operator) {
      case 'equals':
        fieldCondition = { [condition.field]: condition.value };
        break;
      case 'contains':
        fieldCondition = {
          [condition.field]: {
            contains: condition.value as string,
            mode: 'insensitive' as const
          }
        };
        break;
      case 'startsWith':
        fieldCondition = {
          [condition.field]: {
            startsWith: condition.value as string,
            mode: 'insensitive' as const
          }
        };
        break;
      case 'endsWith':
        fieldCondition = {
          [condition.field]: {
            endsWith: condition.value as string,
            mode: 'insensitive' as const
          }
        };
        break;
      case 'greaterThan':
        fieldCondition = { [condition.field]: { gt: condition.value } };
        break;
      case 'lessThan':
        fieldCondition = { [condition.field]: { lt: condition.value } };
        break;
      case 'between':
        if (Array.isArray(condition.value) && condition.value.length === 2) {
          fieldCondition = {
            [condition.field]: {
              gte: condition.value[0],
              lte: condition.value[1]
            }
          };
        }
        break;
      case 'in':
        if (Array.isArray(condition.value)) {
          fieldCondition = { [condition.field]: { in: condition.value } };
        }
        break;
      default:
        return;
    }

    whereConditions.push(fieldCondition);
  });

  if (whereConditions.length === 0) {
    return {};
  }

  // 根据logic组合条件
  const hasOr = conditions.some(c => c.logic === 'OR');
  
  if (hasOr) {
    // 如果有OR逻辑，需要更复杂的处理
    const andConditions: Record<string, unknown>[] = [];
    const orConditions: Record<string, unknown>[] = [];

    whereConditions.forEach((condition, _index) => {
      if (conditions[_index]?.logic === 'OR') {
        orConditions.push(condition);
      } else {
        andConditions.push(condition);
      }
    });

    const result: Record<string, unknown> = {};
    if (andConditions.length > 0) {
      Object.assign(result, andConditions.length === 1 ? andConditions[0] : { AND: andConditions });
    }
    if (orConditions.length > 0) {
      if (Object.keys(result).length > 0) {
        result.OR = orConditions;
      } else {
        return orConditions.length === 1 ? orConditions[0] : { OR: orConditions };
      }
    }
    return result;
  } else {
    // 默认用AND逻辑
    return whereConditions.length === 1 ? whereConditions[0] : { AND: whereConditions };
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database } = await params;

    // 使用新的统一验证函数
    const validationResult = await validateDatabaseCode(database);
    if (!validationResult.isValid) {
      return NextResponse.json(
        { success: false, error: validationResult.error },
        { status: validationResult.status || 400 }
      );
    }

    // 权限检查
    const requiredLevel = getDatabaseAccessLevel(database) as "free" | "premium" | "enterprise";
    const hasAccess = await checkPermissions(requiredLevel);
    if (!hasAccess) {
      return NextResponse.json(
        { success: false, error: 'Permission denied' },
        { status: 403 }
      );
    }

    const body: AdvancedSearchRequest = await request.json();
    const { conditions, sortBy, sortOrder = 'desc' } = body;

    // 使用全局翻页配置（性能优化）
    const requestedPage = body.page || 1;
    const requestedLimit = body.limit || 0;

    const { page, limit } = validatePaginationParams(requestedPage, requestedLimit);

    // 获取配置
    const config: DatabaseConfig = await getDatabaseConfig(database);
    const visibleFields = config.fields.filter(f => f.isVisible).map(f => f.fieldName);
    const sortableFields = config.fields.filter(f => f.isSortable).map(f => f.fieldName);

    // 构建复杂查询条件 - 使用重构后的函数，不再需要database字段过滤
    const where = buildAdvancedWhere(conditions, config);

    // 构建排序条件
    const defaultSortField = sortableFields[0] || 'approvalDate';
    const orderBy = {
      [sortBy || defaultSortField]: sortOrder,
    };

    // 使用动态模型获取
    const model = getDynamicModel(database);
    if (!isPrismaModel(model)) {
      return NextResponse.json(
        { success: false, error: 'Model not found or invalid' },
        { status: 500 }
      );
    }

    // 构建 select 对象
    const select: Record<string, boolean> = {};
    visibleFields.forEach(f => { select[f] = true; });
    // 主键id始终返回，但不再返回database字段
    select['id'] = true;

    const data = await model.findMany({
      where,
      orderBy,
      skip: (page - 1) * limit,
      take: limit,
      select,
    }) as unknown[];
    
    const totalCount = await (model as any).count({ where }) as number;

    return NextResponse.json({
      success: true,
      data,
      pagination: buildPaginationResponse(page, limit, totalCount),
      conditions,
      config,
      databaseInfo: {
        code: database,
        requiredLevel,
      }
    });
  } catch (__error) {
    console.error('Advanced Search API Error:', __error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
} 