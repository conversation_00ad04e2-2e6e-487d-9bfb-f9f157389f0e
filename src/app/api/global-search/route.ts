import { type NextRequest, NextResponse } from 'next/server';
import { getDynamicModel, isPrismaModel, getAllDatabaseCodes } from '@/lib/dynamicTableMapping';
import { getDatabaseConfig } from '@/lib/configCache';
import { esClient } from '@/lib/elasticsearch';

// 医疗设备索引搜索接口
interface MedicalSearchResult {
  database: string;
  count: number;
  hits?: Array<{
    id: string;
    table_code: string;
    registration_no?: string;
    product_combined?: string;
    company_combined?: string;
  }>;
}

/**
 * 搜索医疗设备索引的函数，按 table_code 分组统计
 * 修复版本：为每个数据库单独查询，避免聚合错误
 */
async function searchMedicalIndexByDatabase(query: string, databaseCodes: string[]): Promise<MedicalSearchResult[]> {
  try {
    console.log('Searching medical_index for query:', query, 'databases:', databaseCodes);

    // 为每个数据库代码单独执行搜索查询
    const searchPromises = databaseCodes.map(async (code) => {
      const shouldQueries = [
        // 精确短语匹配（最高权重）
        {
          multi_match: {
            query: query,
            fields: [
              'registration_no.raw^5',
              'product_combined^3',
              'company_combined^3'
            ],
            type: 'phrase',
            boost: 5
          }
        },
        // 包含匹配（中等权重）
        {
          multi_match: {
            query: query,
            fields: [
              'registration_no.*^2',
              'product_combined.*^2',
              'company_combined.*^2'
            ],
            type: 'cross_fields',
            operator: 'and',
            boost: 2
          }
        },
        // 通配符匹配（用于部分匹配）
        {
          bool: {
            should: [
              { wildcard: { 'registration_no.raw': `*${query.toLowerCase()}*` } },
              { wildcard: { 'product_combined': `*${query.toLowerCase()}*` } },
              { wildcard: { 'company_combined': `*${query.toLowerCase()}*` } }
            ],
            boost: 1
          }
        }
      ];

      const searchBody = {
        query: {
          bool: {
            must: [
              {
                term: { 'table_code': code }
              }
            ],
            should: shouldQueries,
            minimum_should_match: 1
          }
        },
        size: 0, // 只获取总数，不返回具体文档
        track_total_hits: true
      };

      console.log(`Searching for database ${code}:`, JSON.stringify(searchBody, null, 2));

      try {
        const response = await esClient.search({
          index: 'medical_index',
          body: searchBody as any
        });

        const responseBody = (response as any).body || response;
        const total = responseBody.hits?.total?.value || 0;

        console.log(`Database ${code} search result: ${total} hits`);

        return {
          database: code,
          count: total
        };
      } catch (dbError) {
        console.error(`Search failed for database ${code}:`, dbError);
        return {
          database: code,
          count: 0
        };
      }
    });

    const results = await Promise.all(searchPromises);
    console.log('Final search results:', results);

    return results;
  } catch (error) {
    console.error('Medical index search error:', error);
    // 返回所有数据库的零计数
    return databaseCodes.map(code => ({
      database: code,
      count: 0
    }));
  }
}

/**
 * GET /api/global-search?q=<keyword>
 * 返回每个数据库匹配到的记录数，用于首页/导航综合搜索。
 * 优先使用 medical_index ES 索引，回退到 Prisma
 */
export async function GET(_req: NextRequest) {
  try {
    const q = _req.nextUrl.searchParams.get('q')?.trim();
    if (!q) {
      return NextResponse.json({ success: false, error: 'q required' }, { status: 400 });
    }

    // 1. 获取数据库代码列表
    const dbCodes = await getAllDatabaseCodes();

    // 2. 优先尝试使用 medical_index ES 索引
    try {
      const medicalResults = await searchMedicalIndexByDatabase(q, dbCodes);

      // 如果 ES 搜索成功且有结果，直接返回
      if (medicalResults.length > 0) {
        return NextResponse.json({ success: true, data: medicalResults });
      }
    } catch (esError) {
      console.warn('Medical index search failed, falling back to original method:', esError);
    }

    // 3. 回退到原有的搜索方法
    const originalResults = await searchOriginalDatabases(q);
    return NextResponse.json({ success: true, data: originalResults });
  } catch (error) {
    console.error('Global search error:', error);
    return NextResponse.json({ success: false, error: 'Internal error' }, { status: 500 });
  }
}

/**
 * 搜索原有数据库的函数
 */
async function searchOriginalDatabases(q: string): Promise<Array<{ database: string; count: number }>> {
  try {
    // 1. 获取数据库代码列表
    const dbCodes = await getAllDatabaseCodes();

    // 尝试使用 Elasticsearch，如果失败则回退到 Prisma
    try {
      // 2. 获取每库可搜索字段
      const configs = await Promise.all(dbCodes.map((c) => getDatabaseConfig(c)));

      // 3. 构建 msearch 请求
      const searches: Record<string, unknown>[] = [];
      configs.forEach((cfg, idx) => {
        const code = dbCodes[idx];
        const fields = cfg.fields
          .filter((f) => f.isSearchable && f.searchType === 'contains')
          .map((f) => f.fieldName);

        // 添加索引头 - 使用小写索引名
        searches.push({ index: `db-${code}` });

        // 添加查询体
        if (fields.length === 0) {
          searches.push({ size: 0, query: { match_none: {} } });
        } else {
          const should = fields.map((f) => ({
            wildcard: {
              [`${f}.substr`]: {
                value: `*${q}*`,
                case_insensitive: true,
              },
            },
          }));
          searches.push({ size: 0, query: { bool: { should } } });
        }
      });

       // 4. 执行 msearch - 使用正确的ES 8.x API
       const response = await esClient.msearch({
         body: searches
       });

       // ES 8.x 的响应结构可能不同，尝试多种访问方式
       const responses = (response as any).body?.responses || (response as any).responses || [];
       return responses.map((resp: Record<string, unknown>, idx: number) => {
         if (resp.error) {
           console.warn(`ES search error for ${dbCodes[idx]}:`, resp.error);
           return { database: dbCodes[idx], count: 0 };
         }
         const total = (resp as any).hits?.total?.value ?? 0;
         return { database: dbCodes[idx], count: total };
       });

    } catch (_esError) {
      console.warn('Elasticsearch search failed, falling back to Prisma:');
      console.warn('Error details:', _esError);

      // 回退到 Prisma 搜索 - 重构版本：使用动态模型，不依赖database字段
      const fallbackDbCodes = await getAllDatabaseCodes();
      return await Promise.all(fallbackDbCodes.map(async (dbCode) => {
        try {
          const config = await getDatabaseConfig(dbCode);
          const searchableFields = config.fields.filter(f => f.isSearchable && f.searchType === 'contains');

          if (searchableFields.length === 0) {
            return { database: dbCode, count: 0 };
          }

          // 使用动态模型获取 - 重构版本
          const model = getDynamicModel(dbCode);
          if (!isPrismaModel(model)) {
            console.warn(`Model not found for database: ${dbCode}`);
            return { database: dbCode, count: 0 };
          }

          // 构建 OR 查询条件 - 不再需要database字段过滤
          const orConditions = searchableFields.map(field => ({
            [field.fieldName]: {
              contains: q,
              mode: 'insensitive' as const
            }
          }));

          const count = await (model as any).count({
            where: {
              OR: orConditions,
              isActive: true, // Only search active records
            }
          });

          return { database: dbCode, count };
        } catch (_dbError) {
          console.error(`Error searching database ${dbCode}:`, _dbError);
          return { database: dbCode, count: 0 };
        }
      }));
    }
  } catch (error) {
    console.error('Original databases search error:', error);
    return [];
  }
}