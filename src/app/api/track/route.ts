import { NextResponse } from 'next/server';
import { db } from '@/lib/prisma';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const {
      eventType,
      path,
      queryParams,
      referer,
      sessionId
    } = body;
    // 获取IP和User-Agent
    const ip = request.headers.get('x-forwarded-for')?.split(',')[0].trim() || request.headers.get('x-real-ip')?.trim() || '127.0.0.1';
    const userAgent = request.headers.get('user-agent') || undefined;
    await db.activityLog.create({
      data: {
        ip,
        userAgent,
        path,
        method: 'TRACK',
        queryParams: queryParams ? JSON.stringify(queryParams) : undefined,
        referer,
        eventType,
        sessionId,
      }
    });
    return NextResponse.json({ success: true });
  } catch (__error) {
    console.error('Track API Error:', __error);
    return NextResponse.json({ success: false, error: 'Internal server error' }, { status: 500 });
  }
} 