import { type NextRequest, NextResponse } from 'next/server';
import { UnifiedSearchService } from '@/lib/services/unifiedSearchService';

/**
 * GET /api/unified-global-search?q=<keyword>
 * 新版全站综合搜索API
 * 使用ES搜索 -> 返回各数据库的匹配条数
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const query = searchParams.get('q')?.trim();
    
    if (!query) {
      return NextResponse.json({
        success: false,
        error: '搜索关键词不能为空'
      }, { status: 400 });
    }
    
    console.log('[API] 全站搜索请求:', { query });
    
    // 使用统一搜索服务
    const result = await UnifiedSearchService.globalSearch(query);
    
    if (!result.success) {
      return NextResponse.json(result, { status: 500 });
    }
    
    // 兼容现有前端格式
    const compatibleData = result.data.map(item => ({
      database: item.database,
      count: item.count
    }));
    
    return NextResponse.json({
      success: true,
      data: compatibleData,
      search_info: result.search_info
    });
    
  } catch (error) {
    console.error('[API] 全站搜索失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '搜索服务异常'
    }, { status: 500 });
  }
}

/**
 * POST /api/unified-global-search
 * 支持更复杂的全站搜索参数
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { query, options = {} } = body;
    
    if (!query?.trim()) {
      return NextResponse.json({
        success: false,
        error: '搜索关键词不能为空'
      }, { status: 400 });
    }
    
    console.log('[API] 全站搜索POST请求:', { query, options });
    
    // 使用统一搜索服务
    const result = await UnifiedSearchService.globalSearch(query);
    
    if (!result.success) {
      return NextResponse.json(result, { status: 500 });
    }
    
    return NextResponse.json(result);
    
  } catch (error) {
    console.error('[API] 全站搜索POST失败:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '搜索服务异常'
    }, { status: 500 });
  }
}
