"use client";

import { useEffect, useState } from 'react';

export default function SimpleTestPage() {
  const [mounted, setMounted] = useState(false);
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 确保组件已挂载
  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted) return;

    const fetchData = async () => {
      try {
        console.log('开始获取数据...');
        const response = await fetch('/api/data/us_pmn?allFields=dental&page=1&limit=5');
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }
        
        const result = await response.json();
        console.log('API响应:', result);
        
        if (result.success) {
          setData(result.data || []);
          console.log('数据加载成功:', result.data?.length || 0, '条记录');
        } else {
          setError(result.error || '未知错误');
        }
      } catch (err) {
        const errorMsg = err instanceof Error ? err.message : '网络错误';
        setError(errorMsg);
        console.error('请求失败:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [mounted]);

  if (!mounted) {
    return <div>初始化中...</div>;
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">简单测试页面</h1>
      
      {loading && (
        <div className="text-blue-600">正在加载数据...</div>
      )}
      
      {error && (
        <div className="text-red-600">错误: {error}</div>
      )}
      
      {!loading && !error && (
        <div>
          <p className="text-green-600 mb-4">成功加载 {data.length} 条记录</p>
          
          {data.length > 0 && (
            <div className="bg-gray-100 p-4 rounded">
              <h3 className="font-bold mb-2">第一条记录:</h3>
              <pre className="text-xs overflow-auto">
                {JSON.stringify(data[0], null, 2)}
              </pre>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
