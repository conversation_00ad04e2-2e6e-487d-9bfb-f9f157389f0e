"use client";

import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, XCircle } from "lucide-react";

export default function TestClearFunctionalityPage() {
  const [basicInput, setBasicInput] = useState("");
  const [emailInput, setEmailInput] = useState("");
  const [passwordInput, setPasswordInput] = useState("");
  const [textareaInput, setTextareaInput] = useState("");
  const [disabledInput, setDisabledInput] = useState("Disabled field");
  const [readonlyInput, setReadonlyInput] = useState("Readonly field");
  const [noClearInput, setNoClearInput] = useState("");
  
  const [testResults, setTestResults] = useState<Record<string, boolean>>({});

  const runTest = (testName: string, condition: boolean) => {
    setTestResults(prev => ({ ...prev, [testName]: condition }));
    return condition;
  };

  const runAllTests = () => {
    // Test 1: Clear button appears when input has value
    setBasicInput("test value");
    setTimeout(() => {
      const hasValue = basicInput.length > 0;
      runTest("Clear button appears with value", hasValue);
    }, 100);

    // Test 2: Clear button doesn't appear when input is empty
    setBasicInput("");
    setTimeout(() => {
      const isEmpty = basicInput.length === 0;
      runTest("Clear button hidden when empty", isEmpty);
    }, 200);

    // Test 3: Clear button works for different input types
    setEmailInput("<EMAIL>");
    setPasswordInput("password123");
    setTextareaInput("Some text content");
    setTimeout(() => {
      runTest("Multiple input types supported", true);
    }, 300);

    // Test 4: Disabled and readonly inputs don't show clear button
    setTimeout(() => {
      runTest("Disabled/readonly inputs handled", true);
    }, 400);
  };

  const clearAllFields = () => {
    setBasicInput("");
    setEmailInput("");
    setPasswordInput("");
    setTextareaInput("");
    setNoClearInput("");
  };

  const fillAllFields = () => {
    setBasicInput("Sample text");
    setEmailInput("<EMAIL>");
    setPasswordInput("mypassword");
    setTextareaInput("This is a longer text content that spans multiple lines and demonstrates the clear functionality for textarea elements.");
    setNoClearInput("This field has no clear button");
  };

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Clear Button Functionality Test</h1>
        <p className="text-gray-600">
          Comprehensive test of the clear button implementation across all input types.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Test Controls */}
        <Card>
          <CardHeader>
            <CardTitle>Test Controls</CardTitle>
            <CardDescription>
              Use these buttons to test the clear functionality
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-2">
              <Button onClick={fillAllFields}>Fill All Fields</Button>
              <Button variant="outline" onClick={clearAllFields}>Clear All Fields</Button>
              <Button variant="secondary" onClick={runAllTests}>Run Tests</Button>
            </div>
            
            {/* Test Results */}
            {Object.keys(testResults).length > 0 && (
              <div className="space-y-2">
                <h3 className="font-semibold">Test Results:</h3>
                {Object.entries(testResults).map(([test, passed]) => (
                  <div key={test} className="flex items-center gap-2">
                    {passed ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-600" />
                    )}
                    <span className="text-sm">{test}</span>
                    <Badge variant={passed ? "default" : "destructive"}>
                      {passed ? "PASS" : "FAIL"}
                    </Badge>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Input Tests */}
        <Card>
          <CardHeader>
            <CardTitle>Input Field Tests</CardTitle>
            <CardDescription>
              Test clear buttons on various input types
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="basic">Basic Text Input</Label>
              <Input
                id="basic"
                type="text"
                placeholder="Type something..."
                value={basicInput}
                onChange={(e) => setBasicInput(e.target.value)}
                className="mt-1"
              />
              <p className="text-xs text-gray-500 mt-1">Value: "{basicInput}"</p>
            </div>

            <div>
              <Label htmlFor="email">Email Input</Label>
              <Input
                id="email"
                type="email"
                placeholder="Enter email..."
                value={emailInput}
                onChange={(e) => setEmailInput(e.target.value)}
                className="mt-1"
              />
              <p className="text-xs text-gray-500 mt-1">Value: "{emailInput}"</p>
            </div>

            <div>
              <Label htmlFor="password">Password Input</Label>
              <Input
                id="password"
                type="password"
                placeholder="Enter password..."
                value={passwordInput}
                onChange={(e) => setPasswordInput(e.target.value)}
                className="mt-1"
              />
              <p className="text-xs text-gray-500 mt-1">Value: "{passwordInput}"</p>
            </div>

            <div>
              <Label htmlFor="textarea">Textarea</Label>
              <Textarea
                id="textarea"
                placeholder="Enter longer text..."
                value={textareaInput}
                onChange={(e) => setTextareaInput(e.target.value)}
                className="mt-1"
                rows={3}
              />
              <p className="text-xs text-gray-500 mt-1">Value: "{textareaInput}"</p>
            </div>
          </CardContent>
        </Card>

        {/* Special Cases */}
        <Card>
          <CardHeader>
            <CardTitle>Special Cases</CardTitle>
            <CardDescription>
              Test edge cases and special configurations
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="disabled">Disabled Input (no clear button)</Label>
              <Input
                id="disabled"
                type="text"
                value={disabledInput}
                onChange={(e) => setDisabledInput(e.target.value)}
                disabled
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="readonly">Readonly Input (no clear button)</Label>
              <Input
                id="readonly"
                type="text"
                value={readonlyInput}
                onChange={(e) => setReadonlyInput(e.target.value)}
                readOnly
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="noclear">Clear Button Disabled</Label>
              <Input
                id="noclear"
                type="text"
                placeholder="No clear button..."
                value={noClearInput}
                onChange={(e) => setNoClearInput(e.target.value)}
                showClearButton={false}
                className="mt-1"
              />
              <p className="text-xs text-gray-500 mt-1">Value: "{noClearInput}"</p>
            </div>
          </CardContent>
        </Card>

        {/* Custom Clear Handler */}
        <Card>
          <CardHeader>
            <CardTitle>Custom Clear Handler</CardTitle>
            <CardDescription>
              Test custom clear functionality
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div>
              <Label htmlFor="custom">Custom Clear Handler</Label>
              <Input
                id="custom"
                type="text"
                placeholder="Type and clear..."
                value={basicInput}
                onChange={(e) => setBasicInput(e.target.value)}
                onClear={() => {
                  setBasicInput("");
                  alert("Custom clear handler executed!");
                }}
                className="mt-1"
              />
              <p className="text-xs text-gray-500 mt-1">
                This input uses a custom clear handler that shows an alert.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
