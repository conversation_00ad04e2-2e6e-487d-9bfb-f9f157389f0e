"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { DateRangePicker } from "@/components/ui/date-range-picker";

export default function TestDatePicker() {
  const [isOpen, setIsOpen] = useState(false);
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-8">DateRangePicker Test</h1>
      
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          <Button variant="outline">
            Open Advanced Search Test
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Advanced Search Test</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">
                Date Range (Between Operator Test)
              </label>
              <DateRangePicker
                startDate={startDate}
                endDate={endDate}
                onStartDateChange={setStartDate}
                onEndDateChange={setEndDate}
                placeholder="Select date range"
                className="w-full"
              />
            </div>
            
            <div className="mt-4 p-4 bg-gray-50 rounded">
              <h3 className="font-medium mb-2">Test Instructions:</h3>
              <ol className="list-decimal list-inside space-y-1 text-sm">
                <li>Click on the date range input field to open the calendar panel</li>
                <li>Click on the "Start Date" input field - <strong>this should NOT close the modal</strong></li>
                <li>Click on the "End Date" input field - <strong>this should NOT close the modal</strong></li>
                <li>Try clicking the calendar icon to toggle the panel</li>
                <li>Select dates from the native date pickers</li>
                <li>Verify the modal stays open throughout the process</li>
                <li>Click "Confirm" or "Cancel" to close the modal properly</li>
              </ol>
            </div>
            
            <div className="mt-4 p-4 bg-blue-50 rounded">
              <h3 className="font-medium mb-2">Current Values:</h3>
              <p className="text-sm">Start Date: {startDate || "Not selected"}</p>
              <p className="text-sm">End Date: {endDate || "Not selected"}</p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
