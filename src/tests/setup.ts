// This file is used to set up the test environment for Vitest.
// You can add global mocks, setup, or teardown logic here.

// Set up environment variables for testing
process.env.SESSION_SECRET = 'test-session-secret-for-testing-purposes-only';
// Use type assertion to bypass TypeScript's readonly restriction on NODE_ENV
(process.env as any).NODE_ENV = 'test';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test';

// Example: Mock a global function if needed
// vi.mock('some-module', () => ({
//   someFunction: () => 'mocked value',
// }));