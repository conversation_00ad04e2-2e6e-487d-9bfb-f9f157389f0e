# 🏗️ 数据库架构设计原则

## 📋 **设计核心理念**

### **原则1：数据类型隔离**
**每种不同的数据类型都使用独立的专用模型**

```typescript
// ✅ 正确的设计模式
model MedicalDevice_US_PMN {        // PMN数据有自己的字段结构
  kNumber           String           // 510(k)号码
  deviceName        String           // 设备名称
  applicant         String           // 申请人
  dateReceived      DateTime?        // 接收日期
  decisionDate      DateTime?        // 决定日期
  decision          String?          // 批准状态
  regulationNumber  String?          // 法规编号
  medicalSpecialty  String?          // 医学专科
  classificationName String?         // 分类名称
  productCode       String?          // 产品代码
  summary           String?          // 摘要
  // ... PMN特有字段
}

model MedicalDevice_US {             // 常规医疗器械数据
  productName       String           // 产品名称
  companyName       String           // 公司名称
  registrationNumber String?         // 注册号
  // ... 常规医疗器械字段
}

model Drug_CN {                     // 中国药品数据
  drugName          String           // 药品名称
  genericName       String?          // 通用名
  approvalNumber    String?          // 批准文号
  manufacturer      String           // 生产厂家
  dosageForm        String?          // 剂型
  // ... 药品特有字段
}
```

### **原则2：字段结构适配**
**不同数据类型的字段结构完全不同，不应强制统一**

| 数据类型 | 核心字段 | 特有字段示例 |
|---------|---------|-------------|
| **医疗器械-常规** | productName, companyName, registrationNumber | managementType, structureOrUse |
| **医疗器械-PMN** | kNumber, deviceName, applicant | decision, regulationNumber, productCode |
| **药品数据** | drugName, manufacturer, approvalNumber | dosageForm, indication, contraindication |
| **专利数据** | patentNumber, title, inventor | filingDate, grantDate, claims |
| **临床试验** | trialId, title, sponsor | phase, status, endpoints |

### **原则3：业务逻辑分离**
**每种数据类型有自己的业务规则**

```typescript
// 不同数据类型的唯一键生成规则
export const tableConfigs: Record<string, TableConfig> = {
  // PMN数据：使用K号作为唯一键
  MedicalDevice_US_PMN: {
    uniqueKeyRule: (row, context) => {
      if (row.kNumber) {
        return `${row.kNumber}_us_pmn`;
      }
      return `${row.deviceName}_${row.applicant}_us_pmn`;
    }
  },
  
  // 常规医疗器械：使用注册号
  MedicalDevice: {
    uniqueKeyRule: (row, context) => {
      if (row.registrationNumber) {
        return `${row.registrationNumber}_${context.databaseCode}`;
      }
      return `${row.productName}_${row.companyName}_${context.databaseCode}`;
    }
  },
  
  // 药品数据：使用批准文号
  Drug: {
    uniqueKeyRule: (row, context) => {
      if (row.approvalNumber) {
        return `${row.approvalNumber}_${context.databaseCode}`;
      }
      return `${row.drugName}_${row.manufacturer}_${context.databaseCode}`;
    }
  }
};
```

## 🗂️ **数据库分类体系**

### **当前支持的数据类型**

#### **1. 医疗器械类别**
```typescript
// 常规医疗器械（统一字段结构）
MedicalDevice_CN_Imported      // 中国大陆上市
MedicalDevice_CN_Evaluation    // 中国大陆审评
MedicalDevice_US              // 美国上市
MedicalDevice_HK              // 中国香港
MedicalDevice_JP              // 日本
MedicalDevice_UK              // 英国
MedicalDevice_SG              // 新加坡

// 特殊医疗器械（不同字段结构）
MedicalDevice_US_PMN          // 美国PMN(510k)数据
MedicalDevice_EU_MDR          // 欧盟MDR数据
MedicalDevice_FDA_De_Novo     // FDA De Novo数据
```

#### **2. 药品类别**
```typescript
Drug_CN                       // 中国药品
Drug_US_FDA                   // 美国FDA药品
Drug_EU_EMA                   // 欧洲EMA药品
Drug_JP_PMDA                  // 日本PMDA药品
```

#### **3. 专利类别**
```typescript
Patent_CN                     // 中国专利
Patent_US_USPTO               // 美国专利
Patent_Global_WIPO            // 全球专利
```

#### **4. 临床试验类别**
```typescript
ClinicalTrial_Global          // 全球临床试验
ClinicalTrial_CN              // 中国临床试验
ClinicalTrial_US_NIH          // 美国NIH临床试验
```

## 🎯 **新数据库添加指南**

### **步骤1：确定数据类型**
```typescript
// 判断是否需要新模型的决策树
if (字段结构与现有模型差异 > 30%) {
  创建新专用模型();
} else if (业务规则完全不同) {
  创建新专用模型();
} else {
  复用现有模型();
}
```

### **步骤2：创建专用模型**
```prisma
// 示例：添加中国药品数据库
model Drug_CN {
  id                String   @id @default(uuid())
  drugName          String   // 药品名称
  genericName       String?  // 通用名
  brandName         String?  // 商品名
  approvalNumber    String?  // 批准文号
  manufacturer      String   // 生产厂家
  dosageForm        String?  // 剂型
  specification     String?  // 规格
  indication        String?  // 适应症
  contraindication  String?  // 禁忌症
  approvalDate      DateTime?// 批准日期
  validUntil        DateTime?// 有效期
  
  // 统一的管理字段
  businessKey       String   @unique @db.VarChar(200)
  businessKeyHash   String?  @unique
  dataVersion       Int      @default(1)
  isActive          Boolean  @default(true)
  importedAt        DateTime @default(now())
  updatedAt         DateTime @updatedAt
  createdAt         DateTime @default(now())
  
  @@map("drug_cn")
  @@index([approvalNumber])
  @@index([drugName])
  @@index([manufacturer])
}
```

### **步骤3：更新动态映射**
```typescript
// src/lib/dynamicTableMapping.ts
export const DATABASE_TABLE_MAPPING: Record<string, TableMapping> = {
  // 新增药品数据库映射
  drugCN: {
    modelName: 'drug_CN',
    tableName: 'drug_cn',
    displayName: '中国药品数据',
    category: '药品信息',
    description: '中国国家药监局批准的药品信息'
  }
};
```

### **步骤4：配置字段规则**
```typescript
// src/lib/configCache.ts
export const DEFAULT_CONFIGS: Record<string, DatabaseConfig> = {
  drugCN: {
    fields: [
      {
        fieldName: 'drugName',
        displayName: '药品名称',
        fieldType: 'text',
        isVisible: true,
        isSearchable: true,
        isFilterable: true,
        listOrder: 1,
        detailOrder: 1
      }
      // ... 更多字段配置
    ]
  }
};
```

## 📊 **架构优势总结**

### **✅ 专用模型的优势**
1. **字段精确匹配** - 每种数据的字段完全符合其业务需求
2. **性能最优化** - 无冗余字段，索引精准
3. **业务逻辑清晰** - 每种数据有独立的验证和处理规则
4. **扩展性强** - 新增数据类型不影响现有系统
5. **维护简单** - 数据结构变更影响范围小

### **✅ 与传统单表方案对比**

| 方面 | 传统单表+database字段 | 新架构专用模型 |
|------|---------------------|---------------|
| **字段匹配度** | ❌ 60% (很多冗余字段) | ✅ 100% (完全匹配) |
| **查询性能** | ❌ 需要WHERE database过滤 | ✅ 直接查询目标表 |
| **索引效率** | ❌ 复合索引复杂 | ✅ 简单高效索引 |
| **扩展难度** | ❌ 修改影响全部数据 | ✅ 独立扩展 |
| **业务逻辑** | ❌ 混在一起难维护 | ✅ 分离清晰 |

## 🚀 **未来扩展计划**

当您需要添加新的数据库时，我们将按照这个架构原则：

```typescript
// 未来可能的扩展
model ClinicalTrial_Global {
  trialId           String   // 试验ID
  title             String   // 试验标题
  sponsor           String   // 申办方
  phase             String?  // 试验阶段
  status            String?  // 试验状态
  // ... 临床试验特有字段
}

model Company_Global {
  companyName       String   // 公司名称
  stockCode         String?  // 股票代码
  foundedDate       DateTime?// 成立日期
  industry          String?  // 行业分类
  // ... 公司信息特有字段
}
```

这样的架构设计确保了：
- **每种数据都有最适合的模型结构**
- **新增数据类型不会影响现有功能**
- **查询性能始终保持最优**
- **代码维护简单清晰**

您觉得这个架构设计原则如何？是否符合您的预期？ 