# Elasticsearch 医疗设备搜索实现

## 概述

本实现基于 Next.js + React + TypeScript，集成了 Elasticsearch 搜索功能，支持对 `medical_index` 索引的 multi_match 查询。

## 核心功能

1. **全局搜索** - 在首页搜索框中输入关键词，显示各数据库的匹配条数
2. **详细搜索** - 点击数据库名称跳转到详情页，显示具体的搜索结果
3. **智能回退** - ES 搜索失败时自动回退到 Prisma 搜索

## API 实现

### 1. 全局搜索 API (`/api/global-search`)

```typescript
// 搜索医疗设备索引，按 table_code 分组统计
async function searchMedicalIndexByDatabase(query: string, databaseCodes: string[]) {
  const aggs: Record<string, any> = {};
  databaseCodes.forEach(code => {
    aggs[`count_${code}`] = {
      filter: {
        term: { 'table_code': code }
      }
    };
  });

  const searchBody = {
    query: {
      multi_match: {
        query: query,
        fields: [
          'registration_no.*',
          'product_combined.*', 
          'company_combined.*'
        ],
        type: 'best_fields',
        fuzziness: 'AUTO'
      }
    },
    size: 0,
    aggs: aggs
  };

  const response = await esClient.search({
    index: 'medical_index',
    body: searchBody
  });

  // 处理聚合结果...
}
```

### 2. 医疗设备搜索 API (`/api/medical-search`)

```typescript
// GET /api/medical-search?q=<keyword>&database=<code>&page=1&limit=20
export async function GET(req: NextRequest) {
  const searchParams = req.nextUrl.searchParams;
  const query = searchParams.get('q')?.trim();
  const database = searchParams.get('database')?.trim();
  const page = parseInt(searchParams.get('page') || '1');
  const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100);

  const mustQueries = [];
  
  // 主搜索查询
  mustQueries.push({
    multi_match: {
      query: query,
      fields: [
        'registration_no.*',
        'product_combined.*', 
        'company_combined.*'
      ],
      type: 'best_fields',
      fuzziness: 'AUTO'
    }
  });

  // 数据库过滤
  if (database) {
    mustQueries.push({
      term: { 'table_code': database }
    });
  }

  const searchBody = {
    query: {
      bool: {
        must: mustQueries
      }
    },
    from: (page - 1) * limit,
    size: limit,
    track_total_hits: true
  };

  const response = await esClient.search({
    index: 'medical_index',
    body: searchBody
  });
  
  // 处理响应...
}
```

## 前端实现

### 1. 搜索框组件

```typescript
const [searchQuery, setSearchQuery] = useState('');
const [searchResults, setSearchResults] = useState<SearchResult[]>([]);

const performSearch = async () => {
  if (!searchQuery.trim()) return;

  const response = await fetch(`/api/global-search?q=${encodeURIComponent(searchQuery)}`);
  const data = await response.json();

  if (data.success) {
    setSearchResults(data.data);
  }
};
```

### 2. 搜索结果展示

```typescript
{searchResults.map((result) => {
  const config = databaseConfigs[result.database];
  const databaseName = config?.name || result.database;
  
  return (
    <Card key={result.database}>
      <CardContent>
        <h4>{databaseName}</h4>
        <div className="text-2xl font-bold text-blue-600">
          {result.count.toLocaleString()}
        </div>
        <div className="text-sm text-gray-500">results</div>
        
        {result.count > 0 && (
          <Link
            href={`/data/list/${result.database}?allFields=${encodeURIComponent(searchQuery)}`}
          >
            View detailed results
          </Link>
        )}
      </CardContent>
    </Card>
  );
})}
```

### 3. 数据库详情页面集成

```typescript
// 在 DatabasePageContent.tsx 中的 loadData 函数
const allFieldsQuery = currentFilters.allFields as string;
if (allFieldsQuery && allFieldsQuery.trim()) {
  try {
    // 使用 ES 搜索
    const esResponse = await fetch(
      `/api/medical-search?q=${encodeURIComponent(allFieldsQuery)}&database=${database}&page=${page}&limit=${pagination.limit}`
    );
    
    if (esResponse.ok) {
      const esData = await esResponse.json();
      
      if (esData.success && esData.data) {
        // 转换 ES 结果为标准格式
        const transformedData = esData.data.hits.map((hit: any) => ({
          id: hit._source.id,
          ...hit._source
        }));

        setData(transformedData);
        // 设置分页信息...
        return; // ES 搜索成功，直接返回
      }
    }
  } catch (esError) {
    console.warn('ES search failed, falling back to standard search:', esError);
  }
}

// 回退到标准搜索
const response = await searchData(/* ... */);
```

## Elasticsearch 索引结构

```json
{
  "settings": {
    "analysis": {
      "analyzer": {
        "zh_ngram": {
          "tokenizer": "ngram_tokenizer"
        }
      },
      "tokenizer": {
        "ngram_tokenizer": {
          "type": "ngram",
          "min_gram": 1,
          "max_gram": 2,
          "token_chars": ["letter", "digit"]
        }
      }
    }
  },
  "mappings": {
    "properties": {
      "id": { "type": "keyword" },
      "table_code": { "type": "keyword" },
      "registration_no": {
        "type": "text",
        "analyzer": "ik_max_word",
        "fields": {
          "raw": { "type": "keyword" },
          "ngram": { "type": "text", "analyzer": "zh_ngram" }
        }
      },
      "product_combined": {
        "type": "text",
        "analyzer": "ik_max_word",
        "fields": {
          "en": { "type": "text", "analyzer": "standard" },
          "ngram": { "type": "text", "analyzer": "zh_ngram" }
        }
      },
      "company_combined": {
        "type": "text",
        "analyzer": "ik_max_word",
        "fields": {
          "en": { "type": "text", "analyzer": "standard" },
          "ngram": { "type": "text", "analyzer": "zh_ngram" }
        }
      }
    }
  }
}
```

## 使用方法

### 1. 环境配置

```bash
# .env.local
ELASTICSEARCH_NODE=http://localhost:9200
ELASTICSEARCH_USERNAME=your_username  # 可选
ELASTICSEARCH_PASSWORD=your_password  # 可选
```

### 2. 测试搜索功能

访问 `/test-medical-search` 页面测试搜索功能。

### 3. 集成到现有页面

主页搜索框已经集成了 ES 搜索功能：
- 输入关键词后，会显示各数据库的匹配条数
- 点击数据库名称跳转到详情页，使用 `allFields` 参数传递搜索关键词
- 详情页会优先使用 ES 搜索，失败时回退到 Prisma 搜索

## 特性

- ✅ 支持中文、英文和模糊匹配
- ✅ 多字段搜索 (registration_no, product_combined, company_combined)
- ✅ 按数据库代码分组统计
- ✅ 智能回退机制
- ✅ 分页支持
- ✅ 搜索分析和追踪
- ✅ TypeScript 类型安全

## 注意事项

1. 确保 Elasticsearch 服务正在运行
2. 确保 `medical_index` 索引已创建并包含数据
3. 数据中的 `table_code` 字段应该对应数据库配置中的 `code`
4. ES 搜索失败时会自动回退到 Prisma 搜索，保证功能可用性
