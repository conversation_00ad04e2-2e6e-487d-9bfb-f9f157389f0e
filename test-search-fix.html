<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Search Functionality Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Navigation Search URL Generation</h2>
        <p>This test simulates what happens when a user searches from the navigation bar on a database page.</p>
        <button onclick="testNavigationSearch()">Test Navigation Search</button>
        <div id="nav-test-result"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: API Endpoint Availability</h2>
        <p>This test checks if the required API endpoints are available.</p>
        <button onclick="testAPIEndpoints()">Test API Endpoints</button>
        <div id="api-test-result"></div>
    </div>

    <div class="test-section">
        <h2>Test 3: Database Search with allFields</h2>
        <p>This test simulates a search with allFields parameter.</p>
        <button onclick="testDatabaseSearch()">Test Database Search</button>
        <div id="db-test-result"></div>
    </div>

    <script>
        function testNavigationSearch() {
            const resultDiv = document.getElementById('nav-test-result');
            
            // Simulate being on a database page
            const currentPath = '/data/list/medical_devices';
            const searchQuery = 'dental implant';
            
            // Test the URL generation logic from Navigation.tsx
            const databasePageMatch = currentPath.match(/^\/data\/list\/([^\/]+)/);
            
            if (databasePageMatch) {
                const databaseCode = databasePageMatch[1];
                const expectedURL = `/data/list/${databaseCode}?allFields=${encodeURIComponent(searchQuery)}`;
                
                resultDiv.innerHTML = `
                    <div class="success">
                        <h3>✅ Navigation Search Test Passed</h3>
                        <p><strong>Current Path:</strong> ${currentPath}</p>
                        <p><strong>Search Query:</strong> ${searchQuery}</p>
                        <p><strong>Generated URL:</strong> ${expectedURL}</p>
                        <p><strong>Database Code:</strong> ${databaseCode}</p>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ Navigation Search Test Failed</h3>
                        <p>Could not extract database code from path: ${currentPath}</p>
                    </div>
                `;
            }
        }

        async function testAPIEndpoints() {
            const resultDiv = document.getElementById('api-test-result');
            const endpoints = [
                '/api/unified-database-search/medical_devices',
                '/api/unified-search/medical_devices'
            ];
            
            let results = [];
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint + '?q=test', {
                        method: 'GET'
                    });
                    
                    results.push({
                        endpoint,
                        status: response.status,
                        ok: response.ok,
                        statusText: response.statusText
                    });
                } catch (error) {
                    results.push({
                        endpoint,
                        status: 'Error',
                        ok: false,
                        statusText: error.message
                    });
                }
            }
            
            const resultsHTML = results.map(result => `
                <div class="${result.ok ? 'success' : 'error'}">
                    <p><strong>Endpoint:</strong> ${result.endpoint}</p>
                    <p><strong>Status:</strong> ${result.status} ${result.statusText}</p>
                    <p><strong>Available:</strong> ${result.ok ? '✅ Yes' : '❌ No'}</p>
                </div>
            `).join('');
            
            resultDiv.innerHTML = `
                <h3>API Endpoint Test Results</h3>
                ${resultsHTML}
            `;
        }

        async function testDatabaseSearch() {
            const resultDiv = document.getElementById('db-test-result');
            
            try {
                // Test the unified-database-search endpoint with allFields
                const response = await fetch('/api/unified-database-search/medical_devices?allFields=dental&page=1&limit=5', {
                    method: 'GET'
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ Database Search Test Passed</h3>
                            <p><strong>Status:</strong> ${response.status}</p>
                            <p><strong>Results Count:</strong> ${data.data ? data.data.length : 0}</p>
                            <p><strong>Total Results:</strong> ${data.pagination ? data.pagination.total_results : 'N/A'}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Database Search Test Failed</h3>
                            <p><strong>Status:</strong> ${response.status}</p>
                            <p><strong>Error:</strong> ${data.error || 'Unknown error'}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ Database Search Test Failed</h3>
                        <p><strong>Error:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
